# 少儿自然拼读资源管理系统 - 前端需求文档

## 1. 项目概述

### 1.1 项目背景
基于现有的少儿自然拼读数据库设计，开发一个高效的资源管理编辑器，用于管理C1-C6课程的自然拼读内容，包括音素、例词、儿歌、绘本等资源。

### 1.2 核心目标
- 提供直观的可视化编辑界面
- 支持音频、图片等媒体资源管理
- 实现高效的批量编辑功能
- 确保数据的完整性和一致性

## 2. 功能需求

### 2.1 课程管理
- 课程列表展示（C1-C6）
- 课程基本信息编辑
- 课程发布状态管理
- 课程封面图片上传

### 2.2 单元管理
- 单元列表展示和编辑
- 音素类型分类管理（单个字母、元音组合、其它）
- 单元顺序调整
- 单元状态管理

### 2.3 音素管理
- 音素列表展示
- 音素基本信息编辑（字母、音标）
- 音素发音音频上传
- 组合音素的组成部分管理

### 2.4 例词管理
- 例词列表展示和编辑
- 例词音频管理（完整发音、首音、韵尾）
- 例词图片上传
- 例词翻译和音标编辑

### 2.5 儿歌管理
- 儿歌列表展示
- 儿歌视频上传和管理
- 儿歌封面图片管理

### 2.6 绘本管理
- 绘本列表展示
- 绘本内容页管理
- 绘本句子编辑
- 绘本音频管理

## 3. 技术需求

### 3.1 前端技术栈
- **框架**: Vue 2.7 + TypeScript
- **UI组件库**: Element UI 2.15+
- **状态管理**: Vuex 3.x
- **路由**: Vue Router 3.x
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI 4.x / Webpack 4.x
- **CSS预处理器**: Sass/SCSS
- **代码规范**: ESLint + Prettier

### 3.2 核心功能组件

#### 3.2.1 表格编辑器组件
```typescript
// 可编辑表格组件 (Vue 2 Options API)
interface EditableTableProps {
  data: any[]
  columns: TableColumn[]
  editable: boolean
}

// Vue 2 组件定义
export default {
  name: 'EditableTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editingCell: null,
      tempValue: ''
    }
  },
  methods: {
    handleCellEdit(row: any, field: string, value: any) {
      this.$emit('cell-edit', row, field, value)
    },
    handleRowAdd() {
      this.$emit('row-add')
    },
    handleRowDelete(row: any) {
      this.$emit('row-delete', row)
    }
  }
}
```

#### 3.2.2 媒体上传组件
```typescript
// 媒体文件上传组件 (Vue 2 Options API)
interface MediaUploaderProps {
  type: 'audio' | 'image' | 'video'
  accept: string
  maxSize: number
}

export default {
  name: 'MediaUploader',
  props: {
    type: {
      type: String,
      default: 'image',
      validator: (value: string) => ['audio', 'image', 'video'].includes(value)
    },
    accept: {
      type: String,
      default: ''
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    }
  },
  data() {
    return {
      uploading: false,
      uploadProgress: 0
    }
  },
  methods: {
    handleUploadSuccess(url: string) {
      this.$emit('upload-success', url)
    },
    handleUploadError(error: string) {
      this.$emit('upload-error', error)
    },
    beforeUpload(file: File) {
      if (file.size > this.maxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize / 1024 / 1024}MB`)
        return false
      }
      return true
    }
  }
}
```

#### 3.2.3 音频播放器组件
```typescript
// 音频播放器组件 (Vue 2 Options API)
export default {
  name: 'AudioPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    controls: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isPlaying: false,
      duration: 0,
      currentTime: 0
    }
  },
  methods: {
    play() {
      const audio = this.$refs.audio as HTMLAudioElement
      audio.play()
      this.isPlaying = true
      this.$emit('play')
    },
    pause() {
      const audio = this.$refs.audio as HTMLAudioElement
      audio.pause()
      this.isPlaying = false
      this.$emit('pause')
    },
    onEnded() {
      this.isPlaying = false
      this.$emit('ended')
    }
  }
}
```

### 3.3 页面结构设计

#### 3.3.1 主布局
```
┌─────────────────────────────────────────┐
│ Header (导航栏)                          │
├─────────────────────────────────────────┤
│ Sidebar │ Main Content Area             │
│ (课程   │ ┌─────────────────────────────┐ │
│  导航)  │ │ 工具栏 (保存/发布/导入/导出) │ │
│         │ ├─────────────────────────────┤ │
│         │ │ 内容编辑区                   │ │
│         │ │ (表格/表单/媒体预览)         │ │
│         │ └─────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### 3.3.2 课程编辑页面
- 左侧：课程树形导航（课程 > 单元 > 音素）
- 右侧：详细编辑区域
- 底部：操作按钮区域

#### 3.3.3 例词编辑表格
| 序号 | 例词 | 音标 | 中文释义 | 完整发音 | 首音 | 韵尾 | 图片 | 操作 |
|------|------|------|----------|----------|------|------|------|------|
| 1    | bag  | /bæɡ/| n.包     | 🔊播放   | 🔊   | 🔊   | 📷   | 编辑 |

### 3.4 状态管理设计 (Vuex)

```typescript
// store/modules/phonics.ts
interface PhonicsState {
  courses: KidPhonicsCourse[]
  currentCourse: KidPhonicsCourse | null
  currentUnit: KidPhonicsUnit | null
  letters: KidPhonicsLetter[]
  words: Record<number, KidPhonicsWord[]>
  loading: boolean
  error: string | null
}

const state: PhonicsState = {
  courses: [],
  currentCourse: null,
  currentUnit: null,
  letters: [],
  words: {},
  loading: false,
  error: null
}

const mutations = {
  SET_COURSES(state: PhonicsState, courses: KidPhonicsCourse[]) {
    state.courses = courses
  },
  SET_CURRENT_COURSE(state: PhonicsState, course: KidPhonicsCourse) {
    state.currentCourse = course
  },
  SET_CURRENT_UNIT(state: PhonicsState, unit: KidPhonicsUnit) {
    state.currentUnit = unit
  },
  SET_LETTERS(state: PhonicsState, letters: KidPhonicsLetter[]) {
    state.letters = letters
  },
  SET_WORDS(state: PhonicsState, { letterId, words }: { letterId: number, words: KidPhonicsWord[] }) {
    Vue.set(state.words, letterId, words)
  },
  SET_LOADING(state: PhonicsState, loading: boolean) {
    state.loading = loading
  },
  SET_ERROR(state: PhonicsState, error: string | null) {
    state.error = error
  }
}

const actions = {
  async fetchCourses({ commit }: ActionContext<PhonicsState, any>) {
    commit('SET_LOADING', true)
    try {
      const response = await axios.get('/kid-phonics/courses')
      commit('SET_COURSES', response.data.data)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async fetchUnitsByCourse({ commit }: ActionContext<PhonicsState, any>, courseId: number) {
    try {
      const response = await axios.get(`/kid-phonics/courses/${courseId}/units`)
      return response.data.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  }
}

const getters = {
  currentCourseUnits: (state: PhonicsState) => {
    return state.currentCourse ? state.currentCourse.units || [] : []
  },
  
  currentUnitLetters: (state: PhonicsState) => {
    return state.letters.filter(letter => letter.unitId === state.currentUnit?.id)
  },
  
  getWordsByLetterId: (state: PhonicsState) => (letterId: number) => {
    return state.words[letterId] || []
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### 3.5 路由配置 (Vue Router 3.x)

```typescript
// router/index.ts
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/phonics'
  },
  {
    path: '/phonics',
    component: () => import('@/layouts/PhonicsLayout.vue'),
    children: [
      {
        path: '',
        name: 'PhonicsHome',
        component: () => import('@/views/phonics/PhonicsHome.vue')
      },
      {
        path: 'course/:courseId',
        name: 'CourseDetail',
        component: () => import('@/views/phonics/CourseDetail.vue'),
        props: true
      },
      {
        path: 'unit/:unitId',
        name: 'UnitEditor',
        component: () => import('@/views/phonics/UnitEditor.vue'),
        props: true
      },
      {
        path: 'letter/:letterId/words',
        name: 'WordEditor',
        component: () => import('@/views/phonics/WordEditor.vue'),
        props: true
      }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
```

### 3.6 项目配置

#### 3.6.1 Vue CLI 配置 (vue.config.js)
```javascript
const path = require('path')

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/phonics-editor/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  
  devServer: {
    port: 8080,
    proxy: {
      '/kid-phonics': {
        target: 'http://localhost:8083/redbook-resource',
        changeOrigin: true,
        pathRewrite: {
          '^/kid-phonics': '/kid-phonics'
        }
      }
    }
  },
  
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  
  chainWebpack: config => {
    // 优化打包
    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        vendor: {
          name: 'chunk-vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial'
        },
        elementUI: {
          name: 'chunk-elementUI',
          priority: 20,
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/
        }
      }
    })
  },
  
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  }
}
```

#### 3.6.2 TypeScript 配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": [
    "node_modules"
  ]
}
```

#### 3.6.3 依赖包配置 (package.json)
```json
{
  "dependencies": {
    "vue": "^2.7.14",
    "vue-router": "^3.6.5",
    "vuex": "^3.6.2",
    "element-ui": "^2.15.13",
    "axios": "^1.4.0",
    "lodash": "^4.17.21",
    "dayjs": "^1.11.8"
  },
  "devDependencies": {
    "@vue/cli-plugin-typescript": "^5.0.8",
    "@vue/cli-service": "^5.0.8",
    "typescript": "^4.9.5",
    "sass": "^1.62.1",
    "sass-loader": "^13.3.1",
    "eslint": "^8.42.0",
    "eslint-plugin-vue": "^9.14.1",
    "@typescript-eslint/eslint-plugin": "^5.59.11",
    "@typescript-eslint/parser": "^5.59.11",
    "prettier": "^2.8.8"
  }
}
```

## 4. API接口需求

### 4.1 完整的现有接口清单

基于 `src/main/java/com/woxue/resourcemanage/controller/KidPhonicsController.java` 的实际接口：

**课程管理**
```typescript
GET    /kid-phonics/courses                    // 获取所有课程列表
GET    /kid-phonics/courses/{id}               // 获取课程详情
POST   /kid-phonics/courses                    // 创建课程
PUT    /kid-phonics/courses/{id}               // 更新课程
DELETE /kid-phonics/courses/{id}               // 删除课程
POST   /kid-phonics/courses/{id}/publish       // 发布课程
```

**单元管理**
```typescript
GET    /kid-phonics/courses/{courseId}/units   // 获取课程下所有单元
GET    /kid-phonics/units/{id}                 // 获取单元详情
GET    /kid-phonics/units/{id}/with-resources  // 获取单元详情(包含资源)
POST   /kid-phonics/courses/{courseId}/units   // 创建单元
PUT    /kid-phonics/units/{id}                 // 更新单元
DELETE /kid-phonics/units/{id}                 // 删除单元
```

**音素管理**
```typescript
GET    /kid-phonics/units/{unitId}/letters     // 获取单元下所有音素
GET    /kid-phonics/letters/{id}               // 获取音素详情
POST   /kid-phonics/units/{unitId}/letters     // 创建音素
PUT    /kid-phonics/letters/{id}               // 更新音素
DELETE /kid-phonics/letters/{id}               // 删除音素
```

**音素组件管理**
```typescript
GET    /kid-phonics/letters/{letterId}/components     // 获取音素下所有组件
GET    /kid-phonics/components/{id}                   // 获取组件详情
POST   /kid-phonics/letters/{letterId}/components     // 创建组件
PUT    /kid-phonics/components/{id}                   // 更新组件
DELETE /kid-phonics/components/{id}                   // 删除组件
```

**例词管理**
```typescript
GET    /kid-phonics/letters/{letterId}/words   // 获取音素下所有例词
GET    /kid-phonics/words/{id}                 // 获取例词详情
POST   /kid-phonics/letters/{letterId}/words   // 创建例词
PUT    /kid-phonics/words/{id}                 // 更新例词
DELETE /kid-phonics/words/{id}                 // 删除例词
```

**儿歌管理**
```typescript
GET    /kid-phonics/units/{unitId}/rhymes      // 获取单元下所有儿歌
GET    /kid-phonics/rhymes/{id}                // 获取儿歌详情
POST   /kid-phonics/units/{unitId}/rhymes      // 创建儿歌
PUT    /kid-phonics/rhymes/{id}                // 更新儿歌
DELETE /kid-phonics/rhymes/{id}                // 删除儿歌
```

**绘本管理**
```typescript
GET    /kid-phonics/units/{unitId}/picture-books              // 获取单元下所有绘本
GET    /kid-phonics/picture-books/{id}                        // 获取绘本详情
POST   /kid-phonics/units/{unitId}/picture-books              // 创建绘本
PUT    /kid-phonics/picture-books/{id}                        // 更新绘本
DELETE /kid-phonics/picture-books/{id}                        // 删除绘本

// 绘本内容管理
GET    /kid-phonics/picture-books/{bookId}/contents           // 获取绘本下所有内容页
GET    /kid-phonics/picture-book-contents/{id}                // 获取绘本内容详情
POST   /kid-phonics/picture-books/{bookId}/contents           // 创建绘本内容
PUT    /kid-phonics/picture-book-contents/{id}                // 更新绘本内容
DELETE /kid-phonics/picture-book-contents/{id}                // 删除绘本内容

// 绘本句子管理
GET    /kid-phonics/picture-book-contents/{contentId}/sentences  // 获取内容页下所有句子
GET    /kid-phonics/picture-book-sentences/{id}                  // 获取绘本句子详情
POST   /kid-phonics/picture-book-contents/{contentId}/sentences  // 创建绘本句子
PUT    /kid-phonics/picture-book-sentences/{id}                  // 更新绘本句子
DELETE /kid-phonics/picture-book-sentences/{id}                  // 删除绘本句子
```

**文件上传**
```typescript
POST   /kid-phonics/upload/audio               // 上传音频文件
POST   /kid-phonics/upload/image               // 上传图片文件
POST   /kid-phonics/upload/video               // 上传视频文件
```

### 4.2 基于现有接口的功能实现策略

**批量操作的替代方案**
由于没有专门的批量接口，前端需要通过以下方式实现批量操作：

```typescript
// Vue 2 + Vuex 实现批量操作
export default {
  methods: {
    // 批量删除例词
    async batchDeleteWords(wordIds: number[]) {
      this.$store.commit('phonics/SET_LOADING', true)
      try {
        const promises = wordIds.map(id => 
          this.$http.delete(`/kid-phonics/words/${id}`)
        )
        await Promise.all(promises)
        this.$message.success('批量删除成功')
        this.refreshWordList()
      } catch (error) {
        this.$message.error('批量删除失败: ' + error.message)
      } finally {
        this.$store.commit('phonics/SET_LOADING', false)
      }
    },

    // 批量更新例词
    async batchUpdateWords(words: KidPhonicsWord[]) {
      this.$store.commit('phonics/SET_LOADING', true)
      try {
        const promises = words.map(word => 
          this.$http.put(`/kid-phonics/words/${word.id}`, word)
        )
        await Promise.all(promises)
        this.$message.success('批量更新成功')
      } catch (error) {
        this.$message.error('批量更新失败: ' + error.message)
      } finally {
        this.$store.commit('phonics/SET_LOADING', false)
      }
    },

    // 批量创建例词
    async batchCreateWords(words: Omit<KidPhonicsWord, 'id'>[], letterId: number) {
      this.$store.commit('phonics/SET_LOADING', true)
      try {
        const promises = words.map(word => 
          this.$http.post(`/kid-phonics/letters/${letterId}/words`, word)
        )
        await Promise.all(promises)
        this.$message.success('批量创建成功')
        this.refreshWordList()
      } catch (error) {
        this.$message.error('批量创建失败: ' + error.message)
      } finally {
        this.$store.commit('phonics/SET_LOADING', false)
      }
    }
  }
}
```

**自动保存的替代方案**
使用现有的更新接口实现自动保存：

```typescript
import { debounce } from 'lodash'

export default {
  data() {
    return {
      saveStatus: '已保存',
      unsavedChanges: new Set()
    }
  },
  
  created() {
    // 创建防抖自动保存函数
    this.debouncedAutoSave = debounce(this.autoSave, 1000)
  },
  
  methods: {
    // 防抖自动保存
    async autoSave(data: any, type: string, id: number) {
      this.saveStatus = '保存中...'
      try {
        let response
        switch (type) {
          case 'word':
            response = await this.$http.put(`/kid-phonics/words/${id}`, data)
            break
          case 'letter':
            response = await this.$http.put(`/kid-phonics/letters/${id}`, data)
            break
          case 'unit':
            response = await this.$http.put(`/kid-phonics/units/${id}`, data)
            break
        }
        this.saveStatus = '已保存'
        this.unsavedChanges.delete(`${type}-${id}`)
      } catch (error) {
        this.saveStatus = '保存失败'
        this.$message.error('自动保存失败: ' + error.message)
      }
    },
    
    // 触发自动保存
    onDataChange(data: any, type: string, id: number) {
      this.unsavedChanges.add(`${type}-${id}`)
      this.saveStatus = '未保存'
      this.debouncedAutoSave(data, type, id)
    }
  }
}
```

### 4.3 前端状态管理策略

由于没有专门的统计接口，前端需要自行计算和维护状态：

```typescript
// Vuex store 计算统计信息
const getters = {
  // 统计信息计算
  stats: (state: PhonicsState) => {
    const letters = state.letters
    const words = state.words
    
    const totalLetters = letters.length
    const completedLetters = letters.filter(letter => 
      letter.soundUrl && words[letter.id]?.length > 0
    ).length
    
    const allWords = Object.values(words).flat()
    const totalWords = allWords.length
    const completedWords = allWords.filter(word => 
      word.fullSoundUrl && word.imageUrl
    ).length
    
    return {
      totalWords,
      completedWords,
      totalLetters,
      completedLetters,
      wordCompletionRate: totalWords > 0 ? (completedWords / totalWords * 100).toFixed(1) : '0',
      letterCompletionRate: totalLetters > 0 ? (completedLetters / totalLetters * 100).toFixed(1) : '0'
    }
  },
  
  // 获取当前编辑状态
  editingStatus: (state: PhonicsState) => {
    return {
      hasUnsavedChanges: state.unsavedChanges.size > 0,
      unsavedCount: state.unsavedChanges.size,
      currentEditingCell: state.editingCell
    }
  }
}
```

### 4.4 文件上传处理

基于现有的上传接口实现文件管理：

```typescript
// Vue 2 文件上传 mixin
export const uploadMixin = {
  methods: {
    // 音频上传
    async uploadAudio(file: File): Promise<string> {
      const formData = new FormData()
      formData.append('file', file)
      
      try {
        const response = await this.$http.post('/kid-phonics/upload/audio', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          onUploadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            this.$emit('upload-progress', progress)
          }
        })
        
        return response.data.data.url
      } catch (error) {
        this.$message.error('音频上传失败: ' + error.message)
        throw error
      }
    },

    // 图片上传
    async uploadImage(file: File): Promise<string> {
      const formData = new FormData()
      formData.append('file', file)
      
      try {
        const response = await this.$http.post('/kid-phonics/upload/image', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        return response.data.data.url
      } catch (error) {
        this.$message.error('图片上传失败: ' + error.message)
        throw error
      }
    },

    // 视频上传
    async uploadVideo(file: File): Promise<string> {
      const formData = new FormData()
      formData.append('file', file)
      
      try {
        const response = await this.$http.post('/kid-phonics/upload/video', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        return response.data.data.url
      } catch (error) {
        this.$message.error('视频上传失败: ' + error.message)
        throw error
      }
    },

    // 文件类型验证
    validateFileType(file: File, allowedTypes: string[]): boolean {
      const fileType = file.type
      const isValidType = allowedTypes.some(type => fileType.includes(type))
      
      if (!isValidType) {
        this.$message.error(`不支持的文件类型: ${fileType}`)
        return false
      }
      
      return true
    },

    // 文件大小验证
    validateFileSize(file: File, maxSize: number): boolean {
      if (file.size > maxSize) {
        this.$message.error(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`)
        return false
      }
      
      return true
    }
  }
}
```

### 4.5 数据加载策略

**懒加载策略**
```typescript
export default {
  data() {
    return {
      loadingStates: {
        units: false,
        letters: false,
        words: false
      }
    }
  },
  
  methods: {
    // 懒加载单元详情
    async loadUnitDetails(unitId: number) {
      this.loadingStates.units = true
      
      try {
        // 1. 加载单元基本信息
        const unitResponse = await this.$http.get(`/kid-phonics/units/${unitId}`)
        const unit = unitResponse.data.data
        this.$store.commit('phonics/SET_CURRENT_UNIT', unit)
        
        // 2. 加载音素列表
        this.loadingStates.letters = true
        const lettersResponse = await this.$http.get(`/kid-phonics/units/${unitId}/letters`)
        const letters = lettersResponse.data.data
        this.$store.commit('phonics/SET_LETTERS', letters)
        
        // 3. 并行加载每个音素的例词
        this.loadingStates.words = true
        const wordsPromises = letters.map((letter: KidPhonicsLetter) =>
          this.$http.get(`/kid-phonics/letters/${letter.id}/words`)
        )
        const wordsResponses = await Promise.all(wordsPromises)
        
        // 4. 组织数据结构并存储到 Vuex
        letters.forEach((letter: KidPhonicsLetter, index: number) => {
          this.$store.commit('phonics/SET_WORDS', {
            letterId: letter.id,
            words: wordsResponses[index].data.data
          })
        })
        
        return { unit, letters }
      } catch (error) {
        this.$message.error('加载单元详情失败: ' + error.message)
        throw error
      } finally {
        this.loadingStates.units = false
        this.loadingStates.letters = false
        this.loadingStates.words = false
      }
    }
  }
}
```

**缓存策略**
```typescript
// utils/cache.ts
class SimpleCache {
  private cache = new Map<string, { data: any, timestamp: number }>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟

  async get<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data
    }
    
    const data = await fetcher()
    this.cache.set(key, { data, timestamp: Date.now() })
    return data
  }

  clear(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }
}

export const cache = new SimpleCache()

// 在 Vue 组件中使用缓存
export default {
  methods: {
    async getCachedCourses() {
      return cache.get('courses', () => 
        this.$http.get('/kid-phonics/courses').then(res => res.data.data)
      )
    }
  }
}
```

### 4.6 错误处理和重试机制

```typescript
// utils/api.ts
export const apiCall = async <T>(
  apiFunc: () => Promise<T>, 
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiFunc()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
  throw new Error('Max retries exceeded')
}

// Vue 2 组件中使用
export default {
  methods: {
    async updateWordWithRetry(wordId: number, word: KidPhonicsWord) {
      try {
        return await apiCall(() => 
          this.$http.put(`/kid-phonics/words/${wordId}`, word)
        )
      } catch (error) {
        this.$message.error('更新失败，请重试')
        throw error
      }
    }
  }
}
```

### 4.7 性能优化策略

**虚拟滚动 (Element UI)**
```vue
<template>
  <div class="virtual-table">
    <el-table
      :data="visibleData"
      :height="tableHeight"
      @scroll="handleScroll"
    >
      <el-table-column prop="word" label="例词" />
      <el-table-column prop="phonetic" label="音标" />
      <!-- 其他列 -->
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      allData: [],
      visibleData: [],
      itemHeight: 50,
      tableHeight: 400,
      scrollTop: 0
    }
  },
  
  computed: {
    visibleCount() {
      return Math.ceil(this.tableHeight / this.itemHeight) + 2
    },
    
    startIndex() {
      return Math.floor(this.scrollTop / this.itemHeight)
    },
    
    endIndex() {
      return Math.min(this.startIndex + this.visibleCount, this.allData.length)
    }
  },
  
  watch: {
    startIndex() {
      this.updateVisibleData()
    },
    
    allData() {
      this.updateVisibleData()
    }
  },
  
  methods: {
    updateVisibleData() {
      this.visibleData = this.allData.slice(this.startIndex, this.endIndex)
    },
    
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop
    }
  }
}
</script>
```

**防抖和节流**
```typescript
import { debounce, throttle } from 'lodash'

export default {
  created() {
    // 搜索防抖
    this.debouncedSearch = debounce(this.performSearch, 300)
    
    // 滚动节流
    this.throttledScroll = throttle(this.handleScroll, 100)
  },
  
  methods: {
    async performSearch(keyword: string) {
      if (!keyword) {
        this.searchResults = []
        return
      }
      
      try {
        // 使用现有接口进行搜索（客户端过滤）
        const response = await this.$http.get('/kid-phonics/courses')
        const filtered = response.data.data.filter((course: any) => 
          course.courseNameZh.includes(keyword) || 
          course.courseNameEn.includes(keyword)
        )
        this.searchResults = filtered
      } catch (error) {
        this.$message.error('搜索失败: ' + error.message)
      }
    },
    
    handleScroll() {
      // 处理滚动事件
      console.log('Scrolling...')
    }
  }
}
```

## 5. 数据结构定义

### 5.1 核心实体 (TypeScript 接口)

```typescript
// 统一响应格式
interface ApiResponse<T> {
  code: number
  message: string
  data: T
  success: boolean
}

// 课程数据结构
interface KidPhonicsCourse {
  id: number
  courseCode: string
  courseNameZh: string
  courseNameEn: string
  description?: string
  coverImg?: string
  branch: number
  publishStatus: number
  publishTime?: string
  sequence: number
  createTime: string
  updateTime: string
}

// 单元数据结构
interface KidPhonicsUnit {
  id: number
  courseId: number
  unitCode: string
  unitName: string
  phonemeType: string  // 对应 KidPhonicsUnitTypeEnum
  description?: string
  coverImg?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 音素数据结构
interface KidPhonicsLetter {
  id: number
  unitId: number
  letter: string
  ipa?: string
  isCombination: boolean
  soundUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 例词数据结构
interface KidPhonicsWord {
  id: number
  letterId: number
  word: string
  phonetic?: string
  translation?: string
  fullSoundUrl?: string
  initialSoundUrl?: string
  rhymeSoundUrl?: string
  imageUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 音素组件数据结构
interface KidPhonicsLetterComponent {
  id: number
  letterId: number
  componentType: string
  content: string
  mediaUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 儿歌数据结构
interface KidPhonicsRhyme {
  id: number
  unitId: number
  title: string
  content: string
  audioUrl?: string
  videoUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 绘本数据结构
interface KidPhonicsPictureBook {
  id: number
  unitId: number
  title: string
  description?: string
  coverImg?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 绘本内容数据结构
interface KidPhonicsPictureBookContent {
  id: number
  pictureBookId: number
  pageNumber: number
  imageUrl?: string
  audioUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}

// 绘本句子数据结构
interface KidPhonicsPictureBookSentence {
  id: number
  contentId: number
  sentence: string
  audioUrl?: string
  sequence: number
  status: number
  createTime: string
  updateTime: string
}
```

## 6. 用户体验设计

### 6.1 交互设计原则
- **直观性**: 界面布局清晰，操作流程简单
- **效率性**: 支持快捷键和批量操作
- **一致性**: 统一的设计语言和交互模式
- **反馈性**: 及时的操作反馈和状态提示

### 6.2 关键交互场景

#### 6.2.1 例词编辑流程
1. 选择课程 → 选择单元 → 选择音素
2. 在例词表格中直接编辑单词、音标、翻译
3. 点击音频图标上传或播放音频文件
4. 点击图片图标上传或预览图片
5. 自动保存编辑内容

#### 6.2.2 批量操作流程
1. 选择多行数据（复选框）
2. 选择批量操作类型（删除/导出/状态更新）
3. 确认操作并执行
4. 显示操作结果和进度

#### 6.2.3 媒体文件管理
1. 拖拽上传或点击上传按钮
2. 显示上传进度和预览
3. 支持音频播放和图片预览
4. 提供文件替换和删除功能

### 6.3 响应式设计
- 支持桌面端（1920x1080及以上）
- 适配平板端（768px-1024px）
- 基本支持移动端（375px-768px）

## 7. 性能要求

### 7.1 加载性能
- 首屏加载时间 < 3秒
- 页面切换响应时间 < 500ms
- 大数据量表格渲染 < 1秒

### 7.2 交互性能
- 表格编辑响应时间 < 200ms
- 文件上传支持进度显示
- 音频播放延迟 < 100ms

### 7.3 数据处理
- 支持单页显示1000+条记录
- 虚拟滚动优化大数据量显示
- 懒加载减少初始数据请求

## 8. 兼容性要求

### 8.1 浏览器兼容性
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- IE 11+ (基本支持)

### 8.2 设备兼容性
- Windows 7/10/11
- macOS 10.13+
- iOS 12+ (基本支持)
- Android 8+ (基本支持)

## 9. 安全要求

### 9.1 文件上传安全
- 文件类型验证
- 文件大小限制
- 恶意文件检测

### 9.2 数据安全
- 输入数据验证
- XSS防护
- CSRF防护

## 10. 部署要求

### 10.1 构建配置
- 开发环境：热重载、源码映射
- 测试环境：代码压缩、错误监控
- 生产环境：代码混淆、性能优化

### 10.2 CDN配置
- 静态资源CDN加速
- 图片和音频文件CDN存储
- 缓存策略配置

## 11. 测试要求

### 11.1 单元测试
- 组件测试覆盖率 > 80%
- 工具函数测试覆盖率 > 90%
- API接口模拟测试

### 11.2 集成测试
- 端到端测试关键流程
- 跨浏览器兼容性测试
- 性能基准测试

### 11.3 用户测试
- 可用性测试
- 用户体验测试
- 无障碍访问测试

## 12. 维护要求

### 12.1 代码质量
- TypeScript严格模式
- ESLint代码规范检查
- Prettier代码格式化

### 12.2 文档要求
- 组件使用文档
- API接口文档
- 部署运维文档

### 12.3 监控要求
- 错误监控和报警
- 性能监控和分析
- 用户行为分析