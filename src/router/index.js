import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/auth-redirect",
    component: () => import("@/views/login/auth-redirect"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401"),
    hidden: true,
  },
  {
    path: "/cardPreview",
    component: () => import("@/views/xhb/grammar/cardPreview"),
    name: "cardPreview",
    hidden: true,
    meta: {
      title: "互动学练-预览",
    },
  }, {
    path: "/cardEdit",
    component: () => import("@/views/xhb/grammar/cardEdit"),
    name: "cardEdit",
    hidden: true,
    meta: {
      title: "语法卡片",
    },
  }, {
    path: "/knowledge",
    component: () => import("@/views/xhb/edition/leXueReadSon/knowledge"),
    name: "knowledge",
    hidden: true,
    meta: {
      title: "知识要点",
    },
  }, {
    path: "/teach",
    component: () => import("@/views/xhb/grammar/teach"),
    name: "teach",
    hidden: true,
    meta: {
      title: "卡片讲解",
    },
  }, {
    path: "/interactiveLearning",
    component: () => import("@/views/xhb/grammar/interactiveLearning"),
    name: "interactiveLearning",
    hidden: true,
    meta: {
      title: "互动学练-学习",
    },
  }, {
    path: "/seeGrammar",
    component: () => import("@/views/xhb/grammar/seeGrammar"),
    name: "seeGrammar",
    hidden: true,
    meta: {
      title: "互动学练-学习",
    },
  },
  {
    path: "/jnjl",
    component: () => import("@/views/xhb/grammar/jnjl"),
    name: "jnjl",
    hidden: true,
    meta: {
      title: "互动学练-学习",
    },
  },
  {
    path: "/xhb/leXueRead",
    component: () => import("@/views/xhb/edition/leXueRead"),
    name: "leXueRead",
    hidden: true,
    meta: {
      title: "leXueRead",
    },
  }, {
    path: "/writecardEdit",
    component: () => import("@/views/xhb/edition/writecardEdit"),
    name: "writecardEdit",
    hidden: true,
    meta: {
      title: "写作方法",
    },
  },
  {
    path: "/writePreview",
    component: () => import("@/views/xhb/edition/writePreview"),
    name: "writePreview",
    hidden: true,
    meta: {
      title: "写作预览",
    },
  },
];

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index"),
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "el-icon-sunset",
          affix: true,
          roles: ["admin", "editor"],
        },
      },
    ],
  },
  {
    path: '/xhb/edition',
    component: Layout,
    children: [
      {
        path: "/xhb/edition",
        component: () => import("@/views/xhb/edition/edition"),
        name: "Edition",
        meta: {
          title: "edition",
          roles: ["admin", "editor"],
        },
      },
    ]
  },
  {
    path: '/xhb/devEdition',
    component: Layout,
    children: [
      {
        path: "/xhb/devEdition",
        component: () => import("@/views/xhb/devEdition/devEdition.vue"),
        name: "DevEdition",
        meta: {
          title: "devEdition",
          roles: ["admin", "editor"],
        },
      },
    ]
  },
  {
    path: '/xhb/interferenceEdit',
    component: Layout,
    children: [
      {
        path: "/xhb/interferenceEdit",
        component: () => import("@/views/xhb/interferenceEdit/interferenceEdit.vue"),
        name: "interferenceEdit",
        meta: {
          title: "interferenceEdit",
        },
      },
    ]
  },
  {
    path: '/xhb/pictureBook',
    component: Layout,
    children: [
      {
        path: "/xhb/pictureBook",
        component: () => import("@/views/xhb/pictureBook/pictureBook.vue"),
        name: "pictureBook",
        meta: {
          title: "pictureBook",
        },
      },
    ]
  },
  {
    path: '/xhb/spoken',
    component: Layout,
    children: [
      {
        path: "/xhb/spoken",
        component: () => import("@/views/xhb/spoken/spoken.vue"),
        name: "spoken",
        meta: {
          title: "AI口语",
        },
      },
    ]
  },
  {
    path: '/xhb/matchSentence',
    component: Layout,
    children: [
      {
        path: "/xhb/matchSentence",
        component: () => import("@/views/dcwProcessingResources/matchSentence"),
        name: "matchSentence",
        meta: {
          title: "matchSentence",
          roles: ["admin", "editor"],
        },
      },
    ]
  },
  {
    path: '/xhb/editMemonic',
    component: Layout,
    children: [
      {
        path: "/xhb/editMemonic",
        component: () =>
          import("@/views/dcwProcessingResources/editMemonic/index"),
        name: "editMemonic",
        meta: {
          title: "editMemonic",
          roles: ["admin", "editor"],
        },
      },
    ]
  },
  {
    path: '/xhb/wordProofread',
    component: Layout,
    children: [
      {
        path: "/xhb/wordProofread",
        component: () => import("@/views/xhb/wordProofread/wordProofread.vue"),
        name: "wordProofread",
        meta: {
          title: "wordProofread",
        },
      },
    ]
  },
  {
    path: '/xhb/abbreviation',
    component: Layout,
    children: [
      {
        path: "/xhb/abbreviation",
        component: () => import("@/views/xhb/abbreviation/abbreviation.vue"),
        name: "abbreviation",
        meta: {
          title: "abbreviation",
        },
      },
    ]
  },
  {
    path: '/xhb/curriculum',
    component: Layout,
    children: [
      {
        path: "/xhb/curriculum",
        component: () => import("@/views/xhb/edition/curriculum"),
        name: "Curriculum",
        hidden: true,
        meta: {
          title: "curriculum",
        },
      },
      {
        path: "/xhb/curriculum/essayList",
        component: () => import("@/views/xhb/edition/essayList"),
        name: "essayList",
        hidden: true,
        meta: {
          title: "文章管理",
        },
      },
      {
        path: "/xhb/curriculum/editContentView",
        component: () => import("@/views/xhb/edition/editContentView"),
        name: "editContentView",
        hidden: true,
        meta: {
          title: "文章编辑",
        },
      },
      {
        path: "/xhb/curriculum/hearingList",
        component: () => import("@/views/xhb/edition/hearingView/hearingList"),
        name: "hearingList",
        hidden: true,
        meta: {
          title: "同步听力列表",
        },
      },
      {
        path: "/xhb/curriculum/hearingEdit",
        component: () => import("@/views/xhb/edition/hearingView/hearingEdit"),
        name: "hearingEdit",
        hidden: true,
        meta: {
          title: "同步听力编辑",
        },
      },
    ]
  },
  // {
  //   path: "/xhb/read",
  //   component: Layout,
  //   children:[
  //     {
  //       path: "/xhb/read",
  //       component: () => import("@/views/xhb/edition/readView"),
  //       name: "readView",
  //       hidden: true,
  //       meta: {
  //           title: "readView",
  //       },
  //   },
  //   ]
  // },
  {
    path: '/xhb/resourceProofreading',
    component: Layout,
    children: [
      {
        path: "/xhb/resourceProofreading",
        component: () => import("@/views/xhb/edition/resourceProofreading"),
        name: "resourceProofreading",
        hidden: true,
        meta: {
          title: "resourceProofreading",
        },
      },
    ]
  },
  {
    path: '/xhb/syncResourceProofreading',
    component: Layout,
    children: [
      {
        path: "/xhb/syncResourceProofreading",
        component: () => import("@/views/xhb/edition/syncResourceProofreading"),
        name: "syncResourceProofreading",
        hidden: true,
        meta: {
          title: "syncResourceProofreading",
        },
      },
    ]
  },
  {
    path: '/xhb/grammarList',
    component: Layout,
    children: [
      {
        path: "/xhb/grammarList",
        component: () => import("@/views/xhb/edition/grammarList"),
        name: "grammarList",
        hidden: true,
        meta: {
          title: "grammarList",
        },
      },
    ]
  },
  {
    path: '/xhb/questionList',
    component: Layout,
    children: [
      {
        path: "/xhb/questionList",
        component: () => import("@/views/xhb/edition/questionList"),
        name: "questionList",
        hidden: true,
        meta: {
          title: "questionList",
        },
      },
    ]
  },
  {
    path: '/xhb/manageList',
    component: Layout,
    children: [
      {
        path: "/xhb/manageList",
        component: () => import("@/views/xhb/edition/manageList"),
        name: "ManageList",
        hidden: true,
        meta: {
          title: "manageList",
        },
      },
    ]
  },
  {
    path: '/xhb/unit',
    component: Layout,
    children: [
      {
        path: "/xhb/unit",
        component: () => import("@/views/xhb/edition/unit"),
        name: "Unit",
        hidden: true,
        meta: {
          title: "unit",
        },
      },
    ]
  },
  {
    path: '/xhb/articleList',
    component: Layout,
    children: [
      {
        path: "/xhb/articleList",
        component: () => import("@/views/xhb/edition/articleList"),
        name: "articleList",
        hidden: true,
        meta: {
          title: "articleList",
        },
      },
    ]
  },
  {
    path: '/xhb/xieZuoList',
    component: Layout,
    children: [
      {
        path: "/xhb/xieZuoList",
        component: () => import("@/views/xhb/edition/xieZuoList"),
        name: "xieZuoList",
        hidden: true,
        meta: {
          title: "写作",
        },
      },
      {
        path: "sentenceTrain",
        component: () => import("@/views/xhb/edition/sentenceTrain"),
        name: "sentenceTrain",
        hidden: true,
        meta: {
          title: "句型训练",
        },
      },
      {
        path: "articleEdit",
        component: () => import("@/views/xhb/edition/articleEdit"),
        name: "articleEdit",
        hidden: true,
        meta: {
          title: "题目范文",
        },
      },
    ]
  },
  {
    path: '/xhb/leXueList',
    component: Layout,
    children: [
      {
        path: "/xhb/leXueList",
        component: () => import("@/views/xhb/edition/leXueList"),
        name: "leXueList",
        hidden: true,
        meta: {
          title: "leXueList",
        },
      },
    ]
  },
  {
    path: '/xhb/pictureDetail',
    component: Layout,
    children: [
      {
        path: "/xhb/pictureDetail",
        component: () => import("@/views/xhb/pictureBook/pictureDetail.vue"),
        name: "pictureDetail",
        hidden: true,
        meta: {
          title: "pictureDetail",
        },
      },
    ]
  },
  {
    path: '/xhb/pictureDetailWord',
    component: Layout,
    children: [
      {
        path: "/xhb/pictureDetailWord",
        component: () => import("@/views/xhb/pictureBook/pictureDetailWord.vue"),
        name: "pictureDetailWord",
        hidden: true,
        meta: {
          title: "绘本单词编辑",
        },
      },
    ]
  },
  {
    path: '/xhb/spokenDetail',
    component: Layout,
    children: [
      {
        path: "/xhb/spokenDetail",
        component: () => import("@/views/xhb/spoken/spokenDetail.vue"),
        name: "spokenDetail",
        hidden: true,
        meta: {
          title: "AI口语内容",
        },
      },
    ]
  },
  {
    path: '/xhb/devCurriculum',
    component: Layout,
    children: [
      {
        path: "devCurriculum",
        component: () => import("@/views/xhb/devEdition/devCurriculum"),
        name: "DevCurriculum",
        hidden: true,
        meta: {
          title: "devCurriculum",
          keepAlive: true,
        },
      },
    ]
  },
  {
    path: '/xhb/devUnit',
    component: Layout,
    children: [
      {
        path: "devUnit",
        component: () => import("@/views/xhb/devEdition/devUnit"),
        name: "DevUnit",
        hidden: true,
        meta: {
          title: "devUnit",
        },
      },
    ]
  },
  {
    path: '/sentenceProofreading',
    name: 'sentenceProofreading',
    component: Layout,
    children: [
      {
        path: '/sentenceProofreading',
        name: 'sentenceProofreading',
        component: () => import ('@/views/xhb/sentenceProofreading'),
        meta: {
          title: '句子校对',
        }
      }
    ]
  },

  // {
  //     path: "/xhb",
  //     component: Layout,
  //     redirect: "/xhb/edition",
  //     alwaysShow: true, // will always show the root menu
  //     name: "xhb",
  //     meta: {
  //         title: "xhb",
  //         icon: "el-icon-coordinate",
  //         roles: ["admin", "editor"],
  //     },
  //     children: [
  //         /*同步版本管理*/
  //         {
  //             path: "edition",
  //             component: () => import("@/views/xhb/edition/edition"),
  //             name: "Edition",
  //             meta: {
  //                 title: "edition",
  //                 roles: ["admin", "editor"],
  //             },
  //         },
  //         /*拓展版本管理*/
  //         {
  //             path: "devEdition",
  //             component: () => import("@/views/xhb/devEdition/devEdition.vue"),
  //             name: "DevEdition",
  //             meta: {
  //                 title: "devEdition",
  //                 roles: ["admin", "editor"],
  //             },
  //         },
  //         /*干扰项编辑*/
  //         {
  //             path: "interferenceEdit",
  //             component: () => import("@/views/xhb/interferenceEdit/interferenceEdit.vue"),
  //             name: "interferenceEdit",
  //             meta: {
  //                 title: "interferenceEdit",
  //             },
  //         },
  //         /*绘本编辑*/
  //         {
  //             path: "pictureBook",
  //             component: () => import("@/views/xhb/pictureBook/pictureBook.vue"),
  //             name: "pictureBook",
  //             meta: {
  //                 title: "pictureBook",
  //             },
  //         },
  //         /*匹配例句*/
  //         {
  //             path: "matchSentence",
  //             component: () => import("@/views/dcwProcessingResources/matchSentence"),
  //             name: "matchSentence",
  //             meta: {
  //               title: "matchSentence",
  //               roles: ["admin", "editor"],
  //             },
  //         },
  //         /*助记编辑*/
  //         {
  //             path: "editMemonic",
  //             component: () =>
  //                 import("@/views/dcwProcessingResources/editMemonic/index"),
  //             name: "editMemonic",
  //             meta: {
  //                 title: "editMemonic",
  //                 roles: ["admin", "editor"],
  //             },
  //         },
  //         /*单词校对*/
  //         {
  //             path: "wordProofread",
  //             component: () => import("@/views/xhb/wordProofread/wordProofread.vue"),
  //             name: "wordProofread",
  //             meta: {
  //                 title: "wordProofread",
  //             },
  //         },
  //         /*缩写单词绑定*/
  //         {
  //             path: "abbreviation",
  //             component: () => import("@/views/xhb/abbreviation/abbreviation.vue"),
  //             name: "abbreviation",
  //             meta: {
  //                 title: "abbreviation",
  //             },
  //         },
  //         {
  //             path: "curriculum",
  //             component: () => import("@/views/xhb/edition/curriculum"),
  //             name: "Curriculum",
  //             hidden: true,
  //             meta: {
  //                 title: "curriculum",
  //             },
  //         },
  //         {
  //             path: "resourceProofreading",
  //             component: () => import("@/views/xhb/edition/resourceProofreading"),
  //             name: "resourceProofreading",
  //             hidden: true,
  //             meta: {
  //                 title: "resourceProofreading",
  //             },
  //         },
  //         {
  //             path: "syncResourceProofreading",
  //             component: () => import("@/views/xhb/edition/syncResourceProofreading"),
  //             name: "syncResourceProofreading",
  //             hidden: true,
  //             meta: {
  //                 title: "syncResourceProofreading",
  //             },
  //         },
  //         {
  //             path: "grammarList",
  //             component: () => import("@/views/xhb/edition/grammarList"),
  //             name: "grammarList",
  //             hidden: true,
  //             meta: {
  //                 title: "grammarList",
  //             },
  //         },
  //         {
  //             path: "questionList",
  //             component: () => import("@/views/xhb/edition/questionList"),
  //             name: "questionList",
  //             hidden: true,
  //             meta: {
  //                 title: "questionList",
  //             },
  //         },
  //         {
  //             path: "manageList",
  //             component: () => import("@/views/xhb/edition/manageList"),
  //             name: "ManageList",
  //             hidden: true,
  //             meta: {
  //                 title: "manageList",
  //             },
  //         },
  //         //   {
  //         //     path: "levelManager",
  //         //     component: () => import("@/views/xhb/grammar/levelManager"),
  //         //     name: "levelManager",
  //         //     // hidden: true,
  //         //     meta: {
  //         //       title: "语法管理（章）",
  //         //     },
  //         //   },
  //         //   {
  //         //     path: "levelMangerSection",
  //         //     component: () => import("@/views/xhb/grammar/levelMangerSection"),
  //         //     name: "levelMangerSection",
  //         //     // hidden: true,
  //         //     meta: {
  //         //       title: "语法管理（节）",
  //         //     },
  //         //   },

  //         {
  //             path: "unit",
  //             component: () => import("@/views/xhb/edition/unit"),
  //             name: "Unit",
  //             hidden: true,
  //             meta: {
  //                 title: "unit",
  //             },
  //         },

  //         {
  //             path: "articleList",
  //             component: () => import("@/views/xhb/edition/articleList"),
  //             name: "articleList",
  //             hidden: true,
  //             meta: {
  //                 title: "articleList",
  //             },
  //         },

  //         {
  //             path: "pictureDetail",
  //             component: () => import("@/views/xhb/pictureBook/pictureDetail.vue"),
  //             name: "pictureDetail",
  //             hidden: true,
  //             meta: {
  //                 title: "pictureDetail",
  //             },
  //         },
  //         {
  //             path: "devCurriculum",
  //             component: () => import("@/views/xhb/devEdition/devCurriculum"),
  //             name: "DevCurriculum",
  //             hidden: true,
  //             meta: {
  //                 title: "devCurriculum",
  //                 keepAlive: true,
  //             },
  //         },
  //         {
  //             path: "devUnit",
  //             component: () => import("@/views/xhb/devEdition/devUnit"),
  //             name: "DevUnit",
  //             hidden: true,
  //             meta: {
  //                 title: "devUnit",
  //             },
  //         },
  //     ],
  // },
  {
    path: '/homonym',
    name: 'homonym',
    component: Layout,
    children: [
      {
        path: '/homonym',
        name: 'homonym',
        component: () => import ('@/views/xhb/homonym'),
        meta: {
          title: '同音词管理',
        }
      }
    ]
  },
  {
    path: '/disyllabic',
    name: 'disyllabic',
    component: Layout,
    children: [
      {
        path: '/disyllabic',
        name: 'disyllabic',
        component: () => import ('@/views/xhb/disyllabic'),
        meta: {
          title: '分音节管理',
        }
      }
    ]
  },
  {
    path: '/xhb/dict',
    component: Layout,
    children: [
      {
        path: "/xhb/dict",
        component: () => import("@/views/xhb/dict"),
        name: "dict",
        meta: {
          title: "优词典",
        }
      },
    ]
  },
  {
    path: '/editWordUse',
    name: 'editWordUse',
    component: Layout,
    children: [
      {
        path: '/editWordUse',
        name: 'editWordUse',
        component: () => import ('@/views/xhb/editWordUse'),
        meta: {
          title: '用词管理',
        }
      }
    ]
  },
  {
    path: '/lollipop',
    name: 'lollipop',
    component: Layout,
    children: [
      {
        path: '/lollipop',
        name: 'lollipop',
        component: () => import ('@/views/lollipop/course.vue'),
        meta: {
          title: '棒棒糖课程管理'
        }
      },
      {
        path: '/lollipop/scene',
        name: 'lollipop/scene',
        hidden: true,
        component: () => import ('@/views/lollipop/scene.vue'),
        meta: {
          title: '场景管理'
        }
      },
      {
        path: '/lollipop/syncUnit',
        name: 'lollipop/syncUnit',
        hidden: true,
        component: () => import ('@/views/lollipop/syncUnit.vue'),
        meta: {
          title: '同步单元管理'
        }
      },
      {
        path: '/lollipop/letterUnit',
        name: 'lollipop/letterUnit',
        hidden: true,
        component: () => import ('@/views/lollipop/letterUnit.vue'),
        meta: {
          title: '字母单元管理'
        }
      },
      {
        path: '/lollipop/unit/picture',
        name: 'picture',
        hidden: true,
        component: () => import ('@/views/lollipop/module/picture.vue'),
        meta: {
          title: '绘本管理'
        }
      },
      {
        path: '/lollipop/unit/word',
        name: 'word',
        hidden: true,
        component: () => import ('@/views/lollipop/module/word.vue'),
        meta: {
          title: '词汇管理'
        }
      },
      /* {
        path: '/lollipop/word',
        name: 'lollipop/word',
        hidden: true,
        component: () => import ('@/views/lollipop/word.vue'),
        meta: {
          title: '单词管理'
        }
      }*/
    ]
  },
  {
    path: '/AIGeneratedContent',
    name: 'AIGeneratedContent',
    component: Layout,
    children: [
      {
        path: '/AIGeneratedContent',
        name: 'AIGeneratedContent',
        component: () => import ('@/views/xhb/AIGeneratedContent'),
        meta: {
          title: 'AI生成内容管理',
        }
      }
    ]
  },


  /**
   * 儿歌资源管理
   */
  {
    path: '/nursery-song',
    name: 'nurserySong',
    component: Layout,
    meta: {
      title: '儿歌资源管理',
      icon: 'el-icon-headset'
    },
    children: [
      {
        path: '',
        name: 'nurserySongList',
        component: () => import('@/views/nurserySong/index.vue'),
        meta: {
          title: '儿歌资源管理'
        }
      },
      {
        path: 'timeline-editor/:songId',
        name: 'timelineEditor',
        hidden: true,
        component: () => import('@/views/nurserySong/TimelineEditor.vue'),
        meta: {
          title: '时间轴编辑器'
        }
      }
    ]
  },
  /**
   * 单词王资源处理
   */
  // {
  //     path: "/dcwProcessingResources",
  //     component: Layout,
  //     alwaysShow: true, // will always show the root menu
  //     name: "dcwProcessingResources",
  //     redirect: "/dcwProcessingResources/editMemonic",
  //     meta: {
  //         title: "dcwProcessingResources",
  //         icon: "el-icon-takeaway-box",
  //         roles: ["admin", "editor"],
  //     },
  //     children: [
  //         //   {
  //         //     path: "matchSentence",
  //         //     component: () => import("@/views/dcwProcessingResources/matchSentence"),
  //         //     name: "matchSentence",
  //         //     meta: {
  //         //       title: "matchSentence",
  //         //       roles: ["admin", "editor"],
  //         //     },
  //         //   },
  //         //   {
  //         //     path: "phoneticSymbols",
  //         //     component: () =>
  //         //       import("@/views/dcwProcessingResources/phoneticSymbols"),
  //         //     name: "phoneticSymbols",
  //         //     meta: {
  //         //       title: "phoneticSymbols",
  //         //       roles: ["admin", "editor"],
  //         //     },
  //         //   },
  //         //   {
  //         //     path: "editSyllables",
  //         //     component: () => import("@/views/dcwProcessingResources/editSyllables"),
  //         //     name: "editSyllables",
  //         //     meta: {
  //         //       title: "editSyllables",
  //         //       roles: ["admin", "editor"],
  //         //     },
  //         //   },
  //         // {
  //         //     path: "editMemonic",
  //         //     component: () =>
  //         //         import("@/views/dcwProcessingResources/editMemonic/index"),
  //         //     name: "editMemonic",
  //         //     meta: {
  //         //         title: "editMemonic",
  //         //         roles: ["admin", "editor"],
  //         //     },
  //         // },
  //     ],
  // },
  {
    path: "/error",
    component: Layout,
    redirect: "noRedirect",
    name: "ErrorPages",
    hidden: true,
    meta: {
      title: "errorPages",
      icon: "404",
    },
    children: [
      {
        path: "401",
        component: () => import("@/views/error-page/401"),
        name: "Page401",
        meta: {title: "page401", noCache: true},
      },
      {
        path: "404",
        component: () => import("@/views/error-page/404"),
        name: "Page404",
        meta: {title: "page404", noCache: true},
      },
    ],
  },
  {path: "*", redirect: "/404", hidden: true},
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({y: 0}),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

console.log("fjw router", router)
export default router;
