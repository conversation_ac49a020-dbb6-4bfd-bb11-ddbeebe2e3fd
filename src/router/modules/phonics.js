/** 自然拼读路由配置 **/

import Layout from '@/layout'

const phonicsRouter = {
  path: '/phonics',
  component: Layout,
  redirect: '/phonics/dashboard',
  name: 'Phonics',
  meta: {
    title: '自然拼读管理',
    icon: 'education'
  },
  children: [
    {
      path: 'dashboard',
      component: () => import('@/views/phonics/dashboard/index'),
      name: 'PhonicsDashboard',
      meta: { 
        title: '概览', 
        icon: 'dashboard',
        noCache: true
      }
    },
    {
      path: 'courses',
      component: () => import('@/views/phonics/course/index'),
      name: 'PhonicsCourses',
      meta: { 
        title: '课程管理', 
        icon: 'book'
      }
    },
    {
      path: 'course/:courseId/units',
      component: () => import('@/views/phonics/unit/index'),
      name: 'PhonicsUnits',
      meta: { 
        title: '单元管理',
        activeMenu: '/phonics/courses'
      },
      hidden: true
    },
    {
      path: 'unit/:unitId/letters',
      component: () => import('@/views/phonics/letter/index'),
      name: 'PhonicsLetters',
      meta: { 
        title: '音素管理',
        activeMenu: '/phonics/courses'
      },
      hidden: true
    },
    {
      path: 'letter/:letterId/words',
      component: () => import('@/views/phonics/word/index'),
      name: 'PhonicsWords',
      meta: { 
        title: '例词管理',
        activeMenu: '/phonics/courses'
      },
      hidden: true
    }
  ]
}

export default phonicsRouter
