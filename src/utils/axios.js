import context from '../main.js'
import Axios from 'axios'

import qs from 'qs'

import {Message} from 'element-ui';
import {removeToken} from '@/utils/auth'

import router, {resetRouter} from '@/router'
import {getUserToken, setUserToken, removeUserToken } from '@/utils/auth'

export let baseUrl = process.env.VUE_APP_AXIOS_BASE_URL; //这里是一个默认的url，可以没有


// 全局配置
const myAxios = Axios.create({
  baseURL: baseUrl,
  timeout: 5000
})


// 携带user-token
// myAxios.defaults.headers.common['authorization'] = `${getUserToken()}`


// 请求拦截器
myAxios.interceptors.request.use(function (request){
    if(getUserToken()){
        // 携带user-token
        request.headers['authorization'] = `${getUserToken()}`
    }
    return request
}, function (error) {
    context.$message.closeAll();
    return Promise.reject(error)
});


// 响应拦截器
myAxios.interceptors.response.use(function (response) {
  // 将后台的参数结果设置到 response
  const {data} = response
  //如果有user-token  存起来
  if(response.headers['authorization']){
      console.log("response.headers['authorization']",response.headers['authorization'])
      setUserToken(response.headers['authorization']);
  }
  // 统一异常处理
  if (response.status !== 200) {
    return Promise.reject(response.headers.sessionstatus)
  } else if (response.headers.sessionstatus == 'timeout') {
    context.$message.closeAll();
    Message({
      message: '请重新登录',
      type: 'error'
    })
    removeToken()
    location.reload()
    resetRouter()
    return Promise.reject(response.headers.sessionstatus)
  }
  if(response.request.responseType != "blob"){
  
    try {
      // 尝试解析 response.request.response 为 JSON
     response.data = JSON.parse(response.request.response)
    } catch (error) {
      // 解析失败，说明不是有效的 JSON 字符串，保持原样
      response.data = response.request.response
    }
    response.status = response.request.status
    response.statusText = response.request.statusText
    return response.data
  }else {
    return response
  }
}, function (error) {
  context.$message.closeAll();
  return Promise.reject(error)
})

/*
* get 请求
* */
export function get (url, params,baseURL=baseUrl) {
  console.log(baseURL)
  return myAxios({
    method: 'get',
    baseURL,
    url,
    params, // get 请求带的参数          name=张三&age=13
    timeout: 10000,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
}


export function get_blob(url, params,baseURL=baseUrl){
  return myAxios({
    method: 'get',
    baseURL,
    url,
    params, // get 请求带的参数          name=张三&age=13
    timeout: 10000,
    responseType: "blob",
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
}

/*
* post 请求  数据为查询字符串   key=val&key=val
* */
export function post_formData (url, data,baseURL=baseUrl) {

  let formData = new FormData();
  for (let dataKey in data) {
    formData.append(dataKey, data[dataKey])
  }
  return myAxios({
    method: 'post',
    baseURL,
    url,
    data: formData,
    timeout: 5000000,
  })
}

/*
* post 请求  数据为查询字符串   对象中嵌套数组
* {
* id:1，
* arr:[]}
* */
export function post_obj_array (url, data,baseURL=baseUrl) {
  return myAxios({
    method: 'post',
    baseURL,
    url,
    data: qs.stringify(data, {allowDots: true}), // get 请求带的参数          name=张三&age=13
    timeout: 5000000,
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/*
* post 请求  数据为查询字符串   对象中嵌套数组
*
* ids:[1,2,3]，
* 转为
* ids=1&ids=2&ids=3
* */
export function post_array (url, data,baseURL=baseUrl) {
  return myAxios({
    method: 'post',
    baseURL,
    url,
    data: qs.stringify(data, {arrayFormat: 'repeat'}), // get 请求带的参数          name=张三&age=13
    timeout: 5000000,
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/*
* post 请求  数据为json 字符串
*
* */
export function post_json (url, data,baseURL=baseUrl) {
  return myAxios({
    method: 'post',
    baseURL,
    url,
    data,
    timeout: 5000000,
  })
}

export function post (url,data,baseURL=baseUrl) {
  return myAxios({
    method: 'post',
    baseURL,
    url,
    data,
    timeout: 5000000,
  })
}

export default myAxios
