<template>
  <div class="timeline-editor">
    <!-- 媒体播放器 -->
    <div class="media-player">
      <!-- 视频播放器 -->
      <div class="video-container" v-if="videoUrl">
        <video
          ref="videoPlayer"
          :src="videoUrl"
          @loadedmetadata="onMediaLoaded"
          @timeupdate="onTimeUpdate"
          @ended="onMediaEnded"
          @error="onMediaError"
          @loadstart="onVideoLoadStart"
          @canplay="onVideoCanPlay"
          preload="metadata"
          controls
          muted
          style="width: 100%; max-height: 400px;"
        ></video>
      </div>
      
      <!-- 音频播放器（始终显示，用于播放原唱音频） -->
      <div class="audio-container" v-if="audioUrl">
        <audio
          ref="audioPlayer"
          :src="audioUrl"
          @loadedmetadata="onMediaLoaded"
          @timeupdate="onTimeUpdate"
          @ended="onMediaEnded"
          @error="onMediaError"
          @loadstart="onAudioLoadStart"
          @canplay="onAudioCanPlay"
          preload="metadata"
          controls
          style="width: 100%;"
        ></audio>
      </div>
      
      <!-- 播放控制面板 -->
      <div class="player-controls" v-if="audioUrl || videoUrl">
        <el-button
          :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
          @click="togglePlay"
          size="small"
          circle
        ></el-button>
        
        <span class="time-display">
          {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
        </span>
        
        <el-button
          icon="el-icon-refresh-left"
          @click="seekBackward"
          size="small"
          title="后退5秒 (←)"
        ></el-button>
        
        <el-button
          icon="el-icon-refresh-right"
          @click="seekForward"
          size="small"
          title="前进5秒 (→)"
        ></el-button>
        
        <div class="playback-rate-control">
          <span class="rate-label">速度:</span>
          <el-slider
            v-model="playbackRate"
            :min="0.5"
            :max="2"
            :step="0.1"
            :show-tooltip="false"
            @change="onPlaybackRateChange"
            style="width: 100px;"
          ></el-slider>
          <span class="rate-display">{{ playbackRate }}x</span>
        </div>
        
        <div class="volume-control">
          <i class="el-icon-voice"></i>
          <el-slider
            v-model="volume"
            :min="0"
            :max="100"
            @change="onVolumeChange"
            style="width: 80px;"
          ></el-slider>
        </div>
      </div>
    </div>

    <!-- 可视化时间轴 -->
    <div class="timeline-container" ref="timelineContainer">
      <div class="timeline-header">
        <div class="timeline-ruler" ref="timelineRuler">
          <div
            v-for="mark in timeMarks"
            :key="mark.time"
            class="time-mark"
            :style="{ left: mark.position + 'px' }"
          >
            <div class="mark-line"></div>
            <div class="mark-label">{{ formatTime(mark.time) }}</div>
          </div>
        </div>
        
        <!-- 播放进度指示器 -->
        <div
          class="playhead"
          :style="{ left: playheadPosition + 'px' }"
          v-if="duration > 0"
        ></div>
      </div>

      <!-- 句子时间轴 -->
      <div class="timeline-tracks">
        <div
          v-for="(sentence, index) in sentences"
          :key="sentence.id || index"
          class="timeline-track"
          :class="{ active: selectedSentence === sentence }"
          @click="selectSentence(sentence)"
        >
          <div class="track-label">
            <span class="sentence-index">{{ sentence.sentenceIndex }}</span>
            <span class="sentence-text">{{ sentence.lyricsText }}</span>
          </div>
          
          <div class="track-timeline">
            <!-- 句子时间段 -->
            <div
              class="sentence-segment"
              :style="{
                left: getTimePosition(sentence.startTime) + 'px',
                width: getTimePosition(sentence.endTime - sentence.startTime) + 'px'
              }"
              @mousedown="startDrag($event, sentence, 'move')"
            >
              <div class="segment-content">
                <span class="segment-text">{{ sentence.lyricsText }}</span>
                <span class="segment-time">
                  {{ formatTime(sentence.startTime) }} - {{ formatTime(sentence.endTime) }}
                </span>
              </div>
              
              <!-- 左边界拖拽手柄 -->
              <div
                class="resize-handle left"
                @mousedown.stop="startDrag($event, sentence, 'start')"
                title="拖拽调整开始时间"
              ></div>
              
              <!-- 右边界拖拽手柄 -->
              <div
                class="resize-handle right"
                @mousedown.stop="startDrag($event, sentence, 'end')"
                title="拖拽调整结束时间"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 精确编辑面板 -->
    <div class="precision-editor" v-if="selectedSentence">
      <h4>精确编辑 - 句子 {{ selectedSentence.sentenceIndex }}</h4>
      
      <el-form>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始时间">
              <el-input-number
                v-model="selectedSentence.startTime"
                :min="0"
                :step="100"
                @change="onTimeChange"
                style="width: 100%"
              ></el-input-number>
              <div class="time-display">{{ formatTime(selectedSentence.startTime) }}</div>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="结束时间">
              <el-input-number
                v-model="selectedSentence.endTime"
                :min="0"
                :step="100"
                @change="onTimeChange"
                style="width: 100%"
              ></el-input-number>
              <div class="time-display">{{ formatTime(selectedSentence.endTime) }}</div>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="时长">
              <el-input-number
                :value="selectedSentence.endTime - selectedSentence.startTime"
                disabled
                style="width: 100%"
              ></el-input-number>
              <div class="time-display">{{ formatDuration(selectedSentence.endTime - selectedSentence.startTime) }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div class="quick-actions">
        <el-button size="small" @click="playSegment(selectedSentence)">
          <i class="el-icon-video-play"></i> 播放片段
        </el-button>
        
        <el-button size="small" @click="setCurrentTimeAsStart">
          <i class="el-icon-position"></i> 设为开始时间
        </el-button>
        
        <el-button size="small" @click="setCurrentTimeAsEnd">
          <i class="el-icon-position"></i> 设为结束时间
        </el-button>
        
        <el-button size="small" @click="adjustTime(-100)">
          <i class="el-icon-minus"></i> -0.1s
        </el-button>
        
        <el-button size="small" @click="adjustTime(100)">
          <i class="el-icon-plus"></i> +0.1s
        </el-button>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations">
      <h4>批量操作</h4>
      
      <el-form>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间偏移 (毫秒)">
              <el-input-number
                v-model="batchOffset"
                :step="100"
                placeholder="正数向后偏移，负数向前偏移"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <div class="batch-buttons">
              <el-button @click="applyBatchOffset" :disabled="!batchOffset">
                应用偏移
              </el-button>
              
              <el-button @click="resetAllTimes">
                重置所有时间
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimelineEditor',
  props: {
    sentences: {
      type: Array,
      default: () => []
    },
    audioUrl: {
      type: String,
      default: ''
    },
    videoUrl: {
      type: String,
      default: ''
    },
    timelineWidth: {
      type: Number,
      default: 800
    },
    songDetail: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      // 音频播放相关
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      playbackRate: 1.0,
      volume: 50,
      
      // 时间轴相关
      selectedSentence: null,
      timeMarks: [],
      pixelsPerSecond: 50, // 每秒对应的像素数
      
      // 拖拽相关
      isDragging: false,
      dragType: '', // 'move', 'start', 'end'
      dragSentence: null,
      dragStartX: 0,
      dragStartTime: 0,
      
      // 批量操作
      batchOffset: 0
    }
  },
  computed: {
    playheadPosition() {
      return this.getTimePosition(this.currentTime * 1000)
    }
  },
  watch: {
    // 监听音频URL变化
    audioUrl: {
      handler(newUrl) {
        if (newUrl) {
          this.$nextTick(() => {
            this.resetMediaState()
          })
        }
      },
      immediate: true
    },
    // 监听视频URL变化
    videoUrl: {
      handler(newUrl) {
        if (newUrl) {
          this.$nextTick(() => {
            this.resetMediaState()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.generateTimeMarks()
    this.setupKeyboardShortcuts()
    this.setupMouseEvents()
  },
  beforeDestroy() {
    this.removeKeyboardShortcuts()
    this.removeMouseEvents()
  },
  methods: {
    // 音频播放控制
    togglePlay() {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        if (this.isPlaying) {
          mediaElement.pause()
          // 同时暂停视频播放器
          if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
            this.$refs.videoPlayer.pause()
          }
        } else {
          mediaElement.play()
          // 同时播放视频播放器
          if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
            this.$refs.videoPlayer.play()
          }
        }
        this.isPlaying = !this.isPlaying
      }
    },
    
    seekBackward() {
      const newTime = Math.max(0, this.currentTime - 5)
      this.seekTo(newTime)
    },
    
    seekForward() {
      const newTime = Math.min(this.duration, this.currentTime + 5)
      this.seekTo(newTime)
    },
    
    // 跳转到指定时间
    seekTo(time) {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        mediaElement.currentTime = time
        this.currentTime = time
        
        // 同步视频播放器的时间
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
          this.$refs.videoPlayer.currentTime = time
        }
      }
    },
    
    onMediaLoaded() {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        this.duration = mediaElement.duration
        this.generateTimeMarks()
        // 设置初始音量
        mediaElement.volume = this.volume / 100
        console.log('Media loaded successfully:', {
          duration: this.duration,
          url: this.videoUrl || this.audioUrl
        })
      }
    },
    
    // 媒体加载错误处理
    onMediaError(event) {
      console.error('Media load error:', event)
      const mediaElement = event.target
      const error = mediaElement.error
      if (error) {
        let errorMessage = '媒体加载失败'
        switch (error.code) {
          case 1:
            errorMessage = '媒体加载被中止'
            break
          case 2:
            errorMessage = '网络错误'
            break
          case 3:
            errorMessage = '媒体解码错误'
            break
          case 4:
            errorMessage = '不支持的媒体格式'
            break
        }
        this.$message.error(`${errorMessage}: ${this.videoUrl || this.audioUrl}`)
      }
    },
    
    // 重置媒体状态
    resetMediaState() {
      this.isPlaying = false
      this.currentTime = 0
      this.duration = 0
      this.selectedSentence = null
      
      // 等待DOM更新后重新绑定事件
      this.$nextTick(() => {
        const mediaElement = this.getMediaElement()
        if (mediaElement) {
          // 添加错误处理事件
          mediaElement.addEventListener('error', this.onMediaError)
          mediaElement.addEventListener('loadstart', () => {
            console.log('Media loading started:', this.videoUrl || this.audioUrl)
          })
          mediaElement.addEventListener('canplay', () => {
            console.log('Media can start playing')
          })
        }
      })
    },
    
    onTimeUpdate() {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        this.currentTime = mediaElement.currentTime
        
        // 同步视频播放器的时间
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
          const timeDiff = Math.abs(this.$refs.videoPlayer.currentTime - mediaElement.currentTime)
          if (timeDiff > 0.1) { // 如果时间差超过0.1秒，进行同步
            this.$refs.videoPlayer.currentTime = mediaElement.currentTime
          }
        }
      }
    },
    
    onMediaEnded() {
      this.isPlaying = false
    },
    
    onPlaybackRateChange() {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        mediaElement.playbackRate = this.playbackRate
      }
    },
    
    // 音量改变
    onVolumeChange() {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        mediaElement.volume = this.volume / 100
      }
    },
    
    // 媒体加载事件处理
    onVideoLoadStart() {
      console.log('Video loading started:', this.videoUrl)
    },
    
    onVideoCanPlay() {
      console.log('Video can start playing')
    },
    
    onAudioLoadStart() {
      console.log('Audio loading started:', this.audioUrl)
    },
    
    onAudioCanPlay() {
      console.log('Audio can start playing')
    },
    
    // 获取当前媒体元素（优先使用音频播放器进行控制）
    getMediaElement() {
      // 当同时有视频和音频时，优先使用音频播放器进行控制
      if (this.audioUrl && this.$refs.audioPlayer) {
        return this.$refs.audioPlayer
      } else if (this.videoUrl && this.$refs.videoPlayer) {
        return this.$refs.videoPlayer
      }
      return null
    },
    
    // 播放指定片段
    playSegment(sentence) {
      const mediaElement = this.getMediaElement()
      if (mediaElement) {
        const startTime = sentence.startTime / 1000
        mediaElement.currentTime = startTime
        
        // 同步视频播放器的时间
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
          this.$refs.videoPlayer.currentTime = startTime
          this.$refs.videoPlayer.play()
        }
        
        mediaElement.play()
        this.isPlaying = true
        
        // 在结束时间停止播放
        const stopTime = sentence.endTime / 1000
        const checkTime = () => {
          if (mediaElement.currentTime >= stopTime) {
            mediaElement.pause()
            this.isPlaying = false
            // 同时暂停视频播放器
            if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && mediaElement === this.$refs.audioPlayer) {
              this.$refs.videoPlayer.pause()
            }
          } else if (this.isPlaying) {
            requestAnimationFrame(checkTime)
          }
        }
        requestAnimationFrame(checkTime)
      }
    },
    
    // 时间轴相关
    generateTimeMarks() {
      this.timeMarks = []
      if (this.duration <= 0) return
      
      const totalWidth = this.timelineWidth
      const totalTime = this.duration * 1000 // 转换为毫秒
      this.pixelsPerSecond = totalWidth / this.duration
      
      // 生成时间刻度
      const interval = this.getTimeInterval()
      for (let time = 0; time <= totalTime; time += interval) {
        this.timeMarks.push({
          time: time,
          position: this.getTimePosition(time)
        })
      }
    },
    
    getTimeInterval() {
      // 根据总时长决定时间间隔
      if (this.duration <= 30) return 1000 // 1秒
      if (this.duration <= 120) return 5000 // 5秒
      if (this.duration <= 600) return 10000 // 10秒
      return 30000 // 30秒
    },
    
    getTimePosition(timeMs) {
      return (timeMs / 1000) * this.pixelsPerSecond
    },
    
    getTimeFromPosition(positionPx) {
      return (positionPx / this.pixelsPerSecond) * 1000
    },
    
    // 句子选择和编辑
    selectSentence(sentence) {
      this.selectedSentence = sentence
      this.$emit('sentence-selected', sentence)
    },
    
    onTimeChange() {
      if (this.selectedSentence) {
        this.selectedSentence.duration = this.selectedSentence.endTime - this.selectedSentence.startTime
        this.$emit('sentence-updated', this.selectedSentence)
      }
    },
    
    setCurrentTimeAsStart() {
      if (this.selectedSentence) {
        this.selectedSentence.startTime = Math.round(this.currentTime * 1000)
        this.onTimeChange()
      }
    },
    
    setCurrentTimeAsEnd() {
      if (this.selectedSentence) {
        this.selectedSentence.endTime = Math.round(this.currentTime * 1000)
        this.onTimeChange()
      }
    },
    
    adjustTime(offset) {
      if (this.selectedSentence) {
        this.selectedSentence.startTime = Math.max(0, this.selectedSentence.startTime + offset)
        this.selectedSentence.endTime = Math.max(this.selectedSentence.startTime + 100, this.selectedSentence.endTime + offset)
        this.onTimeChange()
      }
    },
    
    // 拖拽功能
    startDrag(event, sentence, type) {
      this.isDragging = true
      this.dragType = type
      this.dragSentence = sentence
      this.dragStartX = event.clientX
      
      if (type === 'move') {
        this.dragStartTime = sentence.startTime
      } else if (type === 'start') {
        this.dragStartTime = sentence.startTime
      } else if (type === 'end') {
        this.dragStartTime = sentence.endTime
      }
      
      this.selectSentence(sentence)
      event.preventDefault()
    },
    
    onMouseMove(event) {
      if (!this.isDragging) return
      
      const deltaX = event.clientX - this.dragStartX
      const deltaTime = this.getTimeFromPosition(deltaX)
      
      if (this.dragType === 'move') {
        const duration = this.dragSentence.endTime - this.dragSentence.startTime
        this.dragSentence.startTime = Math.max(0, this.dragStartTime + deltaTime)
        this.dragSentence.endTime = this.dragSentence.startTime + duration
      } else if (this.dragType === 'start') {
        this.dragSentence.startTime = Math.max(0, Math.min(this.dragSentence.endTime - 100, this.dragStartTime + deltaTime))
      } else if (this.dragType === 'end') {
        this.dragSentence.endTime = Math.max(this.dragSentence.startTime + 100, this.dragStartTime + deltaTime)
      }
      
      this.onTimeChange()
    },
    
    onMouseUp() {
      if (this.isDragging) {
        this.isDragging = false
        this.dragType = ''
        this.dragSentence = null
      }
    },
    
    // 批量操作
    applyBatchOffset() {
      this.sentences.forEach(sentence => {
        sentence.startTime = Math.max(0, sentence.startTime + this.batchOffset)
        sentence.endTime = Math.max(sentence.startTime + 100, sentence.endTime + this.batchOffset)
      })
      this.$emit('batch-updated', this.sentences)
      this.$message.success(`已对所有句子应用 ${this.batchOffset}ms 的时间偏移`)
    },
    
    resetAllTimes() {
      this.$confirm('确定要重置所有句子的时间吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.sentences.forEach((sentence, index) => {
          sentence.startTime = index * 3000 // 每句间隔3秒
          sentence.endTime = (index + 1) * 3000
        })
        this.$emit('batch-updated', this.sentences)
        this.$message.success('已重置所有句子时间')
      })
    },
    
    // 键盘快捷键
    setupKeyboardShortcuts() {
      this.keydownHandler = (event) => {
        if (!this.selectedSentence) return
        
        switch (event.key) {
          case 'ArrowLeft':
            if (event.shiftKey) {
              this.adjustTime(-100)
            } else {
              this.seekBackward()
            }
            event.preventDefault()
            break
          case 'ArrowRight':
            if (event.shiftKey) {
              this.adjustTime(100)
            } else {
              this.seekForward()
            }
            event.preventDefault()
            break
          case ' ':
            this.togglePlay()
            event.preventDefault()
            break
          case 'Enter':
            this.playSegment(this.selectedSentence)
            event.preventDefault()
            break
        }
      }
      
      document.addEventListener('keydown', this.keydownHandler)
    },
    
    removeKeyboardShortcuts() {
      if (this.keydownHandler) {
        document.removeEventListener('keydown', this.keydownHandler)
      }
    },
    
    setupMouseEvents() {
      document.addEventListener('mousemove', this.onMouseMove)
      document.addEventListener('mouseup', this.onMouseUp)
    },
    
    removeMouseEvents() {
      document.removeEventListener('mousemove', this.onMouseMove)
      document.removeEventListener('mouseup', this.onMouseUp)
    },
    
    // 工具函数
    formatTime(milliseconds) {
      if (!milliseconds && milliseconds !== 0) return '00:00.000'
      
      const totalSeconds = Math.floor(milliseconds / 1000)
      const minutes = Math.floor(totalSeconds / 60)
      const seconds = totalSeconds % 60
      const ms = milliseconds % 1000
      
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
    },
    
    formatDuration(milliseconds) {
      if (!milliseconds && milliseconds !== 0) return '0ms'
      
      if (milliseconds < 1000) {
        return `${milliseconds}ms`
      } else {
        const seconds = (milliseconds / 1000).toFixed(1)
        return `${seconds}s`
      }
    }
  }
}
</script>

<style scoped>
.timeline-editor {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 音频播放器样式 */
.media-player {
  background: white;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.video-container {
  margin-bottom: 15px;
  border-radius: 6px;
  overflow: hidden;
  background: #000;
}

.audio-container {
  margin-bottom: 15px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.playback-rate-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.rate-display {
  font-size: 12px;
  color: #666;
  min-width: 30px;
  text-align: center;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-control i {
  color: #666;
  font-size: 14px;
}

.time-display {
  font-family: monospace;
  font-size: 14px;
  color: #666;
  min-width: 120px;
}

.rate-display {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

/* 时间轴容器样式 */
.timeline-container {
  background: white;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-header {
  position: relative;
  height: 60px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.timeline-ruler {
  position: relative;
  height: 100%;
  min-width: 800px;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
}

.mark-line {
  width: 1px;
  height: 20px;
  background: #ccc;
  margin-top: 10px;
}

.mark-label {
  font-size: 11px;
  color: #666;
  margin-top: 5px;
  transform: translateX(-50%);
  white-space: nowrap;
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: #409EFF;
  z-index: 10;
  pointer-events: none;
}

.playhead::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  width: 12px;
  height: 12px;
  background: #409EFF;
  border-radius: 50%;
}

/* 轨道样式 */
.timeline-tracks {
  min-height: 200px;
}

.timeline-track {
  display: flex;
  height: 60px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.timeline-track:hover {
  background: #f0f0f0;
}

.timeline-track.active {
  background: #e3f2fd;
}

.track-label {
  width: 200px;
  padding: 10px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.sentence-index {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.sentence-text {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.track-timeline {
  flex: 1;
  position: relative;
  min-width: 800px;
}

/* 句子片段样式 */
.sentence-segment {
  position: absolute;
  top: 10px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  cursor: move;
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
  min-width: 50px;
  border: 2px solid transparent;
}

.sentence-segment:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.timeline-track.active .sentence-segment {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.segment-content {
  flex: 1;
  overflow: hidden;
}

.segment-text {
  display: block;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

.segment-time {
  display: block;
  font-size: 10px;
  opacity: 0.8;
  font-family: monospace;
}

/* 拖拽手柄样式 */
.resize-handle {
  position: absolute;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
  background: rgba(255,255,255,0.3);
  transition: all 0.2s ease;
  opacity: 0;
}

.sentence-segment:hover .resize-handle {
  opacity: 1;
}

.resize-handle:hover {
  background: rgba(255,255,255,0.6);
}

.resize-handle.left {
  left: 0;
  border-radius: 4px 0 0 4px;
}

.resize-handle.right {
  right: 0;
  border-radius: 0 4px 4px 0;
}

/* 精确编辑面板样式 */
.precision-editor {
  background: white;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #409EFF;
}

.precision-editor h4 {
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.precision-editor h4::before {
  content: '🎯';
  font-size: 16px;
}

.precision-editor .time-display {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.quick-actions {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.quick-actions .el-button {
  margin-right: 10px;
  margin-bottom: 5px;
}

/* 批量操作样式 */
.batch-operations {
  background: white;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #67C23A;
}

.batch-operations h4 {
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-operations h4::before {
  content: '⚡';
  font-size: 16px;
}

.batch-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-editor {
    padding: 10px;
  }
  
  .track-label {
    width: 150px;
  }
  
  .player-controls {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .quick-actions .el-button {
    margin-right: 5px;
  }
  
  .precision-editor,
  .batch-operations {
    padding: 15px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.sentence-segment.playing {
  animation: pulse 1s infinite;
  border-color: #F56C6C !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;
}
</style>