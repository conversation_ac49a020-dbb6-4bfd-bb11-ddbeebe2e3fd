<template>
  <div class="image-uploader">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      accept="image/*"
      class="upload-container"
    >
      <div v-if="imageUrl" class="image-preview">
        <img :src="imageUrl" class="uploaded-image" @click="previewImage"/>
        <div class="image-overlay">
          <i class="el-icon-zoom-in" @click.stop="previewImage"></i>
          <i class="el-icon-delete" @click.stop="removeImage"></i>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <i class="el-icon-plus" v-if="!uploading"></i>
        <i class="el-icon-loading" v-else></i>
        <div class="upload-text">
          {{ uploading ? '上传中...' : '点击上传图片' }}
        </div>
      </div>
    </el-upload>
    
    <div class="upload-tip">
      支持jpg、png格式，文件大小不超过5MB
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" width="60%">
      <img :src="imageUrl" style="width: 100%"/>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImageUploader',
  props: {
    value: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      required: true
    },
    headers: {
      type: Object,
      default: () => ({})
    },
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    }
  },
  data() {
    return {
      uploading: false,
      imageUrl: this.value,
      previewVisible: false
    }
  },
  watch: {
    value(newValue) {
      this.imageUrl = newValue
    },
    imageUrl(newValue) {
      this.$emit('input', newValue)
    }
  },
  methods: {
    beforeUpload(file) {
      // 检查文件类型
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      
      // 检查文件大小
      const isLtMaxSize = file.size < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`图片文件大小不能超过 ${Math.floor(this.maxSize / 1024 / 1024)}MB!`)
        return false
      }
      
      this.uploading = true
      return true
    },
    
    handleSuccess(response) {
      this.uploading = false
      if (response.success) {
        this.imageUrl = response.data.url
        this.$message.success('图片上传成功')
        this.$emit('success', response.data)
      } else {
        this.$message.error('图片上传失败: ' + response.message)
      }
    },
    
    handleError(error) {
      this.uploading = false
      this.$message.error('图片上传失败: ' + error.message)
      this.$emit('error', error)
    },
    
    removeImage() {
      this.imageUrl = ''
      this.$emit('remove')
    },
    
    previewImage() {
      this.previewVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.image-uploader {
  .upload-container {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        border-color: #409EFF;
      }
    }
    
    .image-preview {
      position: relative;
      width: 100%;
      height: 100%;
      
      .uploaded-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        opacity: 0;
        transition: opacity 0.3s;
        
        i {
          color: white;
          font-size: 20px;
          cursor: pointer;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
      
      &:hover .image-overlay {
        opacity: 1;
      }
    }
    
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #8c939d;
      
      i {
        font-size: 28px;
        margin-bottom: 8px;
      }
      
      .upload-text {
        font-size: 12px;
      }
    }
  }
  
  .upload-tip {
    margin-top: 8px;
    color: #606266;
    font-size: 12px;
    text-align: center;
  }
}
</style>
