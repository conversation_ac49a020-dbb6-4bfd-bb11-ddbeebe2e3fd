<template>
  <div class="audio-uploader">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      accept="audio/*"
      class="upload-container"
    >
      <el-button size="small" type="primary" :loading="uploading">
        <i class="el-icon-upload2"></i>
        {{ uploading ? '上传中...' : '选择音频' }}
      </el-button>
      <div slot="tip" class="el-upload__tip">
        支持mp3、wav格式，文件大小不超过10MB
      </div>
    </el-upload>
    
    <!-- 音频预览 -->
    <div v-if="audioUrl" class="audio-preview">
      <div class="audio-info">
        <i class="el-icon-headset"></i>
        <span>{{ fileName || '音频文件' }}</span>
      </div>
      <div class="audio-controls">
        <el-button 
          size="mini" 
          type="text" 
          icon="el-icon-video-play"
          @click="playAudio"
        >
          播放
        </el-button>
        <el-button 
          size="mini" 
          type="text" 
          icon="el-icon-delete"
          @click="removeAudio"
        >
          删除
        </el-button>
      </div>
    </div>
    
    <!-- 隐藏的音频播放器 -->
    <audio ref="audioPlayer" style="display: none;"></audio>
  </div>
</template>

<script>
export default {
  name: 'AudioUploader',
  props: {
    value: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      required: true
    },
    headers: {
      type: Object,
      default: () => ({})
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    }
  },
  data() {
    return {
      uploading: false,
      audioUrl: this.value,
      fileName: ''
    }
  },
  watch: {
    value(newValue) {
      this.audioUrl = newValue
    },
    audioUrl(newValue) {
      this.$emit('input', newValue)
    }
  },
  methods: {
    beforeUpload(file) {
      // 检查文件类型
      const isAudio = file.type.startsWith('audio/')
      if (!isAudio) {
        this.$message.error('只能上传音频文件!')
        return false
      }
      
      // 检查文件大小
      const isLtMaxSize = file.size < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`音频文件大小不能超过 ${Math.floor(this.maxSize / 1024 / 1024)}MB!`)
        return false
      }
      
      this.uploading = true
      this.fileName = file.name
      return true
    },
    
    handleSuccess(response) {
      this.uploading = false
      if (response.success) {
        this.audioUrl = response.data.url
        this.$message.success('音频上传成功')
        this.$emit('success', response.data)
      } else {
        this.$message.error('音频上传失败: ' + response.message)
      }
    },
    
    handleError(error) {
      this.uploading = false
      this.$message.error('音频上传失败: ' + error.message)
      this.$emit('error', error)
    },
    
    removeAudio() {
      this.audioUrl = ''
      this.fileName = ''
      this.$emit('remove')
    },
    
    playAudio() {
      if (this.audioUrl) {
        const audio = this.$refs.audioPlayer
        audio.src = this.audioUrl
        audio.play().catch(error => {
          this.$message.error('音频播放失败')
          console.error('Audio play error:', error)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.audio-uploader {
  .upload-container {
    .el-upload {
      display: inline-block;
    }
  }
  
  .audio-preview {
    margin-top: 10px;
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .audio-info {
      display: flex;
      align-items: center;
      color: #606266;
      
      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
    
    .audio-controls {
      display: flex;
      gap: 5px;
    }
  }
}
</style>
