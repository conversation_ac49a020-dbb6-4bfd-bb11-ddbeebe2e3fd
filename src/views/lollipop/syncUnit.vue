<template>
  <div class="redbook-resource-unit">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" icon="el-icon-plus" @click="handleAddUnit">添加单元</el-button>
      <el-button icon="el-icon-back" @click="$router.back()">返回</el-button>
    </div>

    <!-- 单元列表表格 -->
    <el-table border v-loading="loading" :data="unitList" style="width: 100%" class="unit-table" row-key="id">
      <el-table-column prop="id" align="center" label="ID"></el-table-column>
      <el-table-column prop="topicName" align="center" label="主题名称"></el-table-column>
      <el-table-column prop="nameCn" align="center" label="单元中文名称"></el-table-column>
      <el-table-column prop="nameEn" align="center" label="单元英文名称"></el-table-column>
      <el-table-column prop="level" align="center" label="等级"></el-table-column>
      <el-table-column prop="displayOrder" align="center" label="显示顺序"></el-table-column>
      <el-table-column align="center" label="标题图" width="120">
        <template slot-scope="{ row }">
          <el-image
            v-if="row.topicImg"
            style="width: 60px; height: 60px"
            :src="row.topicImg"
            fit="cover"
            :preview-src-list="[row.topicImg]">
          </el-image>
          <FileUpload
            v-else
            accept=".jpg,.png"
            uploadType="1"
            :formData="{ unitId: row.id }"
            @onSuccess="handleImageUploadSuccess($event, row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="儿歌" width="120">
        <template slot-scope="{ row }">
          <el-button size="mini" v-if="row.song" type="success" icon="el-icon-video-play" @click="handlePreviewSong(row)"> 预览</el-button>
          <FileUpload v-else accept=".mp4,.mov" uploadType="3" :formData="{ unitId: row.id }" @onSuccess="handleUploadSuccess($event, row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="绘本">
        <template slot-scope="scope">
          <el-button size="mini" @click="pictureHandle(scope.$index, scope.row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="词汇">
        <template slot-scope="scope">
          <el-button size="mini" @click="wordHandle(scope.$index, scope.row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="300">
        <template slot-scope="{ row, $index }">
            <el-button size="mini" :type="row.onLine ? 'danger' : 'success'"
              @click="handleStatusChange(row)">
              {{ row.onLine ? '下架' : '上架' }}
            </el-button>
            <el-button size="mini" type="primary" @click="handleEditUnit($index, row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger"  @click="handleDeleteUnit($index, row)">
              删除
            </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 单元表单对话框 -->
    <el-dialog :title="isEditUnit ? '编辑单元' : '添加单元'" :visible.sync="dialogAddUnitVisible" :close-on-click-modal="false"
      width="500px" @close="handleDialogClose">
      <el-form ref="unitForm" :model="unitForm" :rules="rules" label-width="120px" :validate-on-rule-change="false">
        <el-form-item label="主题名称" prop="topicName">
          <el-input v-model.trim="unitForm.topicName"></el-input>
        </el-form-item>
        <el-form-item label="单元中文名称" prop="nameCn">
          <el-input v-model.trim="unitForm.nameCn"></el-input>
        </el-form-item>
        <el-form-item label="单元英文名称" prop="nameEn">
          <el-input v-model.trim="unitForm.nameEn"></el-input>
        </el-form-item>
        <el-form-item label="关联场景" prop="nameEn">
          <el-select v-model="unitForm.sceneId" placeholder="请选择场景">
          <el-option
            v-for="item in sceneIdOptions"
            :key="item.id"
            :label="item.nameCn"
            :value="item.id">
          </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input-number v-model.trim="unitForm.level"  :min="1"  label="等级"></el-input-number>
        </el-form-item>
        <el-form-item label="显示顺序" prop="displayOrder">
          <el-input-number v-model.trim="unitForm.displayOrder"  :min="1"  label="显示顺序"></el-input-number>
        </el-form-item>
        <el-form-item label="标题图">
          <div class="upload-container">
            <el-image
              v-if="unitForm.topicImg"
              style="width: 100px; height: 100px; margin-right: 10px;"
              :src="unitForm.topicImg"
              fit="cover"
              :preview-src-list="[unitForm.topicImg]">
            </el-image>
            <FileUpload
              accept=".jpg,.png"
              uploadType="1"
              :formData="{ unitId: unitForm.id || 'temp' }"
              @onSuccess="handleFormImageUpload" />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogAddUnitVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmitUnit">
          确 定
        </el-button>
      </span>
    </el-dialog>

    <!-- 儿歌预览对话框 -->
    <el-dialog title="儿歌预览" :visible.sync="songVisible" width="640px" :close-on-click-modal="false" @close="handleSongDialogClose">
      <div class="video-container">
        <video v-if="songUrl" width="100%" controls :src="songUrl" @error="handleVideoError"></video>
      </div>
      <div slot="footer" style="display: flex;gap: 10px;">
        <FileUpload :btnText="songObj.subtitleEnUrl?'更换英文字幕':'上传英文字幕'" size="medium" accept=".lrc,.srt,.ass,.ssa,.vit,.sub" uploadType="3" :formData="{ unitId: songData && songData.id }"  @onSuccess="replaceEnUrl" />
        <FileUpload :btnText="songObj.subtitleCnUrl?'更换中文字幕':'上传中文字幕'" size="medium" accept=".lrc,.srt,.ass,.ssa,.vir,.sub" uploadType="3" :formData="{ unitId: songData && songData.id }"  @onSuccess="replaceCnUrl" />
        <FileUpload btnText="更换视频" size="medium" accept=".mp4,.mov" uploadType="3" :formData="{ unitId: songData && songData.id }"  @onSuccess="replaceVideoHandle" />
        <el-button style="margin-left: 20px;" type="danger" @click="removeVideoHandle">删除视频</el-button>
        <el-button style="margin-left: auto;" @click="handleSongDialogClose">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getSceneList, getUnitList, addUnit, updateUnit, deleteUnit, addVideo, updateVideo, deleteVideo, unitSetStatus } from '@/api/xhb/lollipop'
import FileUpload from "./components/FileUpload.vue"

export default {
  name: 'RedbookResourceUnit',
  components: {
    FileUpload
  },
  data() {
    return {
      songVisible: false,
      songUrl: null,
      songObj:{},
      songData: null,
      sceneIdOptions:[],

      dialogAddUnitVisible: false,
      unitList: [],
      isEditUnit: false,
      loading: false,
      submitting: false,

      unitForm: this.getInitialUnitForm(),
      rules: {
        nameCn: [
          { required: true, message: '请输入单元中文名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        nameEn: [
          { required: true, message: '请输入单元英文名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        topicName: [
          { required: true, message: '请输入主题名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    }
  },

  watch: {
    '$route': {
      handler(route) {
        this.unitForm.courseId = route.query.courseId
        this.fetchUnitList(route.query.courseId)
        getSceneList({ courseId: this.$route.query.courseId, }).then(res => {
          this.sceneIdOptions = res.data
        })
      },
      immediate: true
    }
  },

  methods: {
    // 初始化表单数据
    getInitialUnitForm() {
      return {
        id: null,
        courseId: this.$route.query.courseId,
        nameCn: '',
        nameEn: '',
        topicName: '',
        level:"",
        displayOrder:"",
        topicImg: "",
        sceneId: ""
      }
    },
    // 儿歌相关方法
    handlePreviewSong(row) {
      this.songData = row
      if (!row.song?.videoUrl) {
        this.$message.warning('视频地址无效')
        return
      }
      this.songUrl = row.song.videoUrl
      this.songObj = JSON.parse(JSON.stringify(row.song))
      this.songVisible = true
    },
    async replaceVideoHandle(url){
      try {
        const query = {
          id: this.songData.id,
          unitId: this.songData.id,
          videoUrl: url,
          subtitleCnUrl: null,
          subtitleEnUrl: null
        }
        await updateVideo(query)
        this.$message.success('更新成功')
        this.songVisible = false
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        this.$message.error('更新成功' + (error.message || '未知错误'))
      }
    },
  async  replaceEnUrl(url){
      try {
        const query = {
          id: this.songData.id,
          unitId: this.songData.id,
          videoUrl: this.songObj.videoUrl,
          subtitleCnUrl: this.songObj.subtitleCnUrl,
          subtitleEnUrl: url
        }
        await updateVideo(query)
        this.$message.success('上传成功')
        this.songVisible = false
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        this.$message.error('更新成功' + (error.message || '未知错误'))
      }
    },
    async  replaceCnUrl(url){
      try {
        const query = {
          id: this.songData.id,
          unitId: this.songData.id,
          videoUrl: this.songObj.videoUrl,
          subtitleCnUrl: url,
          subtitleEnUrl: this.songObj.subtitleEnUrl
        }
        await updateVideo(query)
        this.$message.success('上传成功')
        this.songVisible = false
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        this.$message.error('更新成功' + (error.message || '未知错误'))
      }
    },
    async removeVideoHandle() {
      try {
        if (!this.songData?.id) {
          this.$message.warning('无效的视频数据')
          return
        }

        const resp = await deleteVideo({ id: this.songData.id })
        if (resp.code === 200) {
          this.$message.success('删除成功')
          this.songVisible = false
          await this.fetchUnitList(this.unitForm.courseId)
        } else {
          this.$message.error(resp.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败：' + (error.message || '未知错误'))
      }
    },
    handleSongDialogClose() {
      this.songVisible = false
      this.songUrl = null
      this.songData = null
    },
    handleVideoError() {
      this.$message.error('视频加载失败')
    },
    // 上传相关方法
    async handleUploadSuccess(url, row) {
        const query = {
          id: row.id,
          unitId: row.id,
          videoUrl: url,
          subtitleCnUrl: null,
          subtitleEnUrl: null
        }
        await addVideo(query)
        await this.fetchUnitList(this.unitForm.courseId)
    },
    // 标题图上传相关方法
    async handleImageUploadSuccess(url, row) {
      try {
        // 更新单元的标题图 - 保留原有所有字段信息
        const updateData = { ...row, topicImg: url }
        await updateUnit(updateData)
        this.$message.success('标题图上传成功')
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        this.$message.error('标题图上传失败：' + (error.message || '未知错误'))
      }
    },
    // 表单中的标题图上传
    handleFormImageUpload(url) {
      this.unitForm.topicImg = url
    },
    // 发布相关方法
    async handleStatusChange(row) {
        await unitSetStatus({
          unitId: row.id,
          onLine: !row.onLine
        })
        this.$message.success(row.onLine ? '上架成功' : '下架成功')
        await this.fetchUnitList(this.unitForm.courseId)
    },

    pictureHandle(index, row) {
      this.$router.push({ name: 'picture', query: { unitID: row.id } })
    },
    wordHandle(index, row) {
      this.$router.push({ name: 'word', query: { unitID: row.id } })
    },
    async handleSubmitUnit() {
      try {
        await this.$refs.unitForm.validate()
        this.submitting = true
        if (this.isEditUnit) {
          await updateUnit(this.unitForm)
          this.$message.success('更新成功')
        } else {
          await addUnit(this.unitForm)
          this.$message.success('添加成功')
        }
        await this.fetchUnitList(this.unitForm.courseId)
        this.dialogAddUnitVisible = false
        this.resetUnitForm()
      } catch (error) {
        this.$message.error(error.message || '操作失败')
      } finally {
        this.submitting = false
      }
    },
    async handleDeleteUnit(index, row) {
      try {
        await this.$confirm('确认删除该单元?', '提示', {
          type: 'warning'
        })

        await deleteUnit({ id: row.id })
        this.$message.success('删除成功')
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败')
        }
      }
    },
    handleAddUnit() {
      this.isEditUnit = false
      this.dialogAddUnitVisible = true
    },
    unitPublishHandle() {
      this.$message.success('发布按钮')
    },
    handleEditUnit(index, row) {
      this.unitForm = { ...row }
      this.isEditUnit = true
      this.dialogAddUnitVisible = true
    },
    handleDialogClose() {
      this.$refs.unitForm.resetFields()
      this.resetUnitForm()
    },
    async fetchUnitList(courseId) {
      if (!courseId) return
      try {
        this.loading = true
        const response = await getUnitList({ courseId })
        this.unitList = response.data
      } catch (error) {
        this.$message.error(error.message || '获取单元列表失败')
      } finally {
        this.loading = false
      }
    },
    resetUnitForm() {
      this.unitForm = {
        id: null,
        courseId: this.unitForm.courseId, // 保留courseId
        nameCn: '',
        nameEn: '',
        sceneId: '',
        topicImg: '',
        level: '',
        displayOrder: '',
        topicName: ''
      }
      this.isEditUnit = false
    },
  },
}
</script>

<style lang="scss" scoped>
.redbook-resource-unit {
  padding: 20px;

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .unit-table {
    margin-top: 20px;
  }

  .video-container {
    width: 100%;
    background: #000;

    video {
      display: block;
      margin: 0 auto;
    }
  }

  .upload-container {
    display: flex;
    align-items: center;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .el-button-group {
    .el-button {
      margin-left: -1px;
    }
  }
}
</style>
