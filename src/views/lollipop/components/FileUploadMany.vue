<template>
  <div class="file-upload">
    <el-upload
      ref="upload"
      :action="action"
      :accept="accept"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :file-list="fileList"
      :http-request="customUpload"
      :multiple="true"
      :limit="limit"
      :on-exceed="handleExceed"
      list-type="picture-card"
    >
      <i class="el-icon-plus"></i>
      <div class="el-upload__tip" slot="tip">
        {{ loading ? '上传中...' : tipText }}
      </div>
    </el-upload>
  </div>
</template>

<script>
import { singleUploadFiles } from '@/api/xhb/lollipop'

export default {
  name: 'FileUpload',
  props: {
    accept: {
      type: String,
      default: ".jpg, .png"
    },
    maxSize: {
      type: Number,
      default: 100
    },
    limit: {
      type: Number,
      default: 50
    },
    uploadType: {
      type: String,
      default: '1'
    },
    tipText: {
      type: String,
      default: '请上传图片'
    },
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      action: '',
      fileList: [],
      loading: false,
      uploadedFiles: [] // 存储已上传成功的文件信息
    }
  },
  methods: {
    handleBeforeUpload(file) {
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB!`)
        return false
      }
      const fileExtension = file.name.split('.').pop().toLowerCase()
      const allowedExtensions = this.accept.split(',').map(ext => ext.replace('.', '').trim())
      if (!allowedExtensions.includes(fileExtension)) {
        this.$message.error(`只能上传 ${this.accept} 格式的文件!`)
        return false
      }
      return true
    },

    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传 ${this.limit} 个文件!`)
    },

    handleRemove(file) {
      const index = this.uploadedFiles.findIndex(item => item.url === file.url)
      if (index !== -1) {
        this.uploadedFiles.splice(index, 1)
        this.$emit('onRemove', this.uploadedFiles)
      }
    },

    async customUpload({ file }) {
      this.loading = true
      try {
        const formData = {
          file: file,
          type: this.uploadType,
          ...this.formData
        }
        const { data } = await singleUploadFiles(formData)
        // 添加到已上传文件列表
        this.uploadedFiles.push({
          name: file.name,
          url: data.url,
          ...data
        })
        this.handleSuccess(this.uploadedFiles)
      } catch (error) {
        this.handleError(error)
      } finally {
        this.loading = false
      }
    },

    handleSuccess(files) {
      this.$emit('onSuccess', files)
      this.$message.success('上传成功')
    },

    handleError(error) {
      this.$message.error('上传失败')
      this.$emit('onError', error)
    },

    reset() {
      this.fileList = []
      this.uploadedFiles = []
      this.$refs.upload.clearFiles()
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload {
  .el-upload {
    width: 100%;
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-upload__tip {
    line-height: 1.2;
    padding: 8px 0;
    color: #909399;
  }
}
</style>