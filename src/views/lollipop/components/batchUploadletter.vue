<template>
  <div class="batch-upload">
    <el-form :model="uploadForm" label-width="120px">
      <el-form-item label="上传文件">
        <el-upload
          class="upload-demo"
          ref="upload"
          action=""
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :before-remove="beforeRemove"
          multiple
          :limit="9999"
          accept=".jpg,.jpeg,.png,.gif"
          :on-exceed="handleExceed"
          :file-list="fileList"
          :auto-upload="false"
          :http-request="uploadFiles"
        >
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <el-button
            style="margin-left: 10px;"
            size="small"
            type="success"
            @click="submitUpload"
            :loading="uploading"
          >
            {{ uploading ? '上传中...' : '上传到服务器' }}
          </el-button>
          <div slot="tip" class="el-upload__tip">
            支持格式：.jpg .jpeg .png .gif，单个文件不超过5MB
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { uploadLetter } from '@/api/xhb/lollipop'

export default {
  name: 'RedbookResourceH5BatchUpload',

  data() {
    return {

      fileList: [],
      uploading: false
    }
  },
  methods: {
    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择文件')
        return
      }
      this.$refs.upload.submit()
    },

    handleChange(file, fileList) {
      this.fileList = fileList
    },

    handleRemove(file, fileList) {
      this.fileList = fileList
    },

    beforeUpload(file) {
      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < 50
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 5MB!`)
        return false
      }
      return true
    },

    handlePreview(file) {
      // 根据文件类型预览
      if (this.uploadForm.type === '1') {
        // 图片预览
        const previewUrl = URL.createObjectURL(file.raw)
        window.open(previewUrl)
      }
    },

    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 9999 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },

    async uploadFiles(options) {
        const { file } = options
        this.uploading = true
        const formData ={
          file:file,
        }
        const response = await uploadLetter(formData)

        if (response.code == 200) {
          this.$message.success(`${file.name} 上传成功`)
          // 清空文件列表
          this.fileList = this.fileList.filter(item => item.uid !== file.uid)
          // 触发成功事件
          this.$emit('upload-success', response.data)
        } else {
          this.$message.error(`${file.name} 上传失败: ${response.message}`)
        }

        this.uploading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-upload {
  .upload-demo {
    .el-upload__tip {
      color: #909399;
      font-size: 12px;
      margin-top: 8px;
    }
  }
}
</style>
