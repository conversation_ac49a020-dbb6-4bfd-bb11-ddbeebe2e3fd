<template>
  <div class="batch-upload">
    <el-form :model="uploadForm" label-width="120px">
      <el-form-item label="文件类型">
        <el-select v-model="uploadForm.type" placeholder="请选择文件类型">
          <el-option
            v-for="(label, value) in fileTypes"
            :key="value"
            :label="label"
            :value="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传文件">
        <el-upload
          class="upload-demo"
          ref="upload"
          action=""
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :before-remove="beforeRemove"
          multiple
          :limit="9999"
          :accept="accept[uploadForm.type]"
          :on-exceed="handleExceed"
          :file-list="fileList"
          :auto-upload="false"
          :http-request="uploadFiles"
        >
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <el-button
            style="margin-left: 10px;"
            size="small"
            type="success"
            @click="submitUpload"
            :loading="uploading"
          >
            {{ uploading ? '上传中...' : '上传到服务器' }}
          </el-button>
          <div slot="tip" class="el-upload__tip">
            支持格式：{{ accept[uploadForm.type] }}，单个文件不超过{{ maxSize }}MB
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { batchUploadFiles } from '@/api/xhb/lollipop'

export default {
  name: 'RedbookResourceH5BatchUpload',

  data() {
    return {
      fileTypes: {
        '1': '图片',
        '2': '音频',
        '3': '视频'
      },
      accept: {
        1: '.jpg,.jpeg,.png.gif',
        2: '.mp3',
        3: '.mp4,.mov'
      },
      maxSizeMap: {
        1: 200,  // 图片最大5MB
        2: 200, // 音频最大20MB
        3: 200  // 视频最大50MB
      },
      uploadForm: {
        type: '1'
      },
      fileList: [],
      uploading: false
    }
  },

  computed: {
    maxSize() {
      return this.maxSizeMap[this.uploadForm.type]
    }
  },

  methods: {
    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择文件')
        return
      }
      this.$refs.upload.submit()
    },

    handleChange(file, fileList) {
      this.fileList = fileList
    },

    handleRemove(file, fileList) {
      this.fileList = fileList
    },

    beforeUpload(file) {
      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB!`)
        return false
      }

      // 检查文件类型
      const fileExtension = file.name.split('.').pop().toLowerCase()
      const allowedExtensions = this.accept[this.uploadForm.type].split(',').map(ext => ext.replace('.', ''))
      if (!allowedExtensions.includes(fileExtension)) {
        this.$message.error(`只能上传${this.accept[this.uploadForm.type]}格式的文件!`)
        return false
      }

      return true
    },

    handlePreview(file) {

      // 根据文件类型预览
      if (this.uploadForm.type === '1') {
        // 图片预览
        const previewUrl = URL.createObjectURL(file.raw)
        window.open(previewUrl)
      }
    },

    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 9999 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },

    async uploadFiles(options) {
      const { file } = options
        this.uploading = true
        // const formData = new FormData()
        // formData.append('files', file)
        // formData.append('type', this.uploadForm.type)
        // // 检查 FormData 内容
        // for (let [key, value] of formData.entries()) {
        //   console.log(`${key}:`, value instanceof File ? {
        //     name: value.name,
        //     size: value.size,
        //     type: value.type
        //   } : value)
        // }


        const formData ={
          files:file,
          type:this.uploadForm.type
        }
        const response = await batchUploadFiles(formData)

        if (response.code == 200) {
          this.$message.success(`${file.name} 上传成功`)
          // 清空文件列表
          this.fileList = this.fileList.filter(item => item.uid !== file.uid)
          // 触发成功事件
          this.$emit('upload-success', response.data)
        } else {
          this.$message.error(`${file.name} 上传失败: ${response.message}`)
        }

        this.uploading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-upload {
  .upload-demo {
    .el-upload__tip {
      color: #909399;
      font-size: 12px;
      margin-top: 8px;
    }
  }
}
</style>
