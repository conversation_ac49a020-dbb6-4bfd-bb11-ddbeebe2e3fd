<!-- src/views/lollipop/course.vue -->
<template>
  <div class="course-management">
    <el-button type="primary" @click="dialogCourseVisible = true">添加课程</el-button>
    <el-button type="primary" @click="batchUploadisShow = true">批量上传</el-button>
    <el-table v-loading="courseList.length === 0" :data="courseList" border style="width: 100%" class="course-table">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="nameCn" label="课程中文名称"></el-table-column>
      <el-table-column prop="nameEn" label="课程英文名称"></el-table-column>
      <el-table-column prop="branch" label="版本/最新发布时间" >
        <template slot-scope="scope">
          <div class="version-info" >
            <el-tag size="small" type="primary" effect="plain">{{ scope.row.branch?`${scope.row.branch}`:'-' }}</el-tag>
            <div v-if="scope.row.branchTime" class="publish-time">
              <i class="el-icon-time"></i>
              {{ formatDateTime(scope.row.branchTime) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="课程类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 'SYNC' ? 'success' : 'warning'">
            {{ scope.row.type === 'SYNC' ? '同步' : '字母' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="年级"></el-table-column>
      <el-table-column label="操作" width="380">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="handlePublishCourse(scope.row)" :loading="scope.row.btnLoading">发布</el-button>
          <el-button size="mini" type="primary" @click="handleEditCourse(scope.$index, scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDeleteCourse(scope.$index, scope.row)">删除</el-button>
          |
          <el-button size="mini" type="primary" @click="handleManageUnits(scope.row)">管理单元</el-button>
          <el-button v-if="scope.row.type === 'SYNC'" size="mini" type="primary" @click="handleManageScenes(scope.row)">管理场景</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 课程对话框 -->
    <el-dialog title="课程信息" :visible.sync="dialogCourseVisible" width="30%">
      <el-form :model="courseForm" label-width="120px">
        <el-form-item label="课程中文名称">
          <el-input v-model="courseForm.nameCn"></el-input>
        </el-form-item>
        <el-form-item label="课程英文名称">
          <el-input v-model="courseForm.nameEn"></el-input>
        </el-form-item>
        <el-form-item label="课程类型">
          <el-select v-model="courseForm.type" placeholder="请选择课程类型">
            <el-option label="同步" value="SYNC"></el-option>
            <el-option label="字母" value="LETTER"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select v-model="courseForm.grade" placeholder="请选择年级">
            <el-option label="不限" value="-1"></el-option>
            <el-option label="g1" value="g1"></el-option>
            <el-option label="g2" value="g2"></el-option>
            <el-option label="k1" value="k1"></el-option>
            <el-option label="k2" value="k2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCourseVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitCourse">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 批量上传文件对话框 -->
    <el-dialog title="批量上传文件" :visible.sync="batchUploadisShow" width="40%">
      <batchUploadView />
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchUploadisShow = false">取 消</el-button>
      </span>
    </el-dialog>



  </div>
</template>

<script>
import {
  getCourseList, addCourse, updateCourse, deleteCourse, coursePublishInterFace
} from '@/api/xhb/lollipop'

import batchUploadView from "./components/batchUpload.vue"

export default {
  components: {
    batchUploadView,
  },
  data() {
    return {
      batchUploadisShow: false,
      courseList: [],
      dialogCourseVisible: false,
      rowData: {},
      courseForm: {
        id: null,
        nameCn: '',
        nameEn: '',
        type: '',
        grade: ''
      },
      isEditCourse: false,

      dialogUnitVisible: false,
      isEditUnit: false,
      fileList: []
    }
  },
  created() {
    this.fetchCourseList()
  },
  methods: {
    formatDateTime(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    async handlePublishCourse(row){
      row.btnLoading = true
      await coursePublishInterFace({ courseId: row.id })
      this.$message.success('发布成功')
      row.btnLoading = false
      this.fetchCourseList()
    },
    fetchCourseList() {
      getCourseList().then(response => {
        this.courseList = response.data
        this.courseList.forEach(item=>{
          this.$set(item,'btnLoading',false)
        })
      })
    },
    handleEditCourse(index, row) {
      this.courseForm = { ...row }
      this.dialogCourseVisible = true
      this.isEditCourse = true
    },
    handleDeleteCourse(index, row) {
      deleteCourse({ id: row.id }).then(() => {
        this.$message.success('删除成功')
        this.fetchCourseList()
      })
    },
    handleSubmitCourse() {
      if (this.isEditCourse) {
        updateCourse(this.courseForm).then(() => {
          this.$message.success('更新成功')
          this.fetchCourseList()
        })
      } else {
        addCourse(this.courseForm).then(() => {
          this.$message.success('添加成功')
          this.fetchCourseList()
        })
      }
      this.dialogCourseVisible = false
      this.resetCourseForm()
    },
    resetCourseForm() {
      this.courseForm = {
        id: null,
        nameCn: '',
        nameEn: '',
        type: '',
        grade: ''
      }
      this.isEditCourse = false
    },

    handleManageScenes(course) {
      this.$router.push({ name: 'lollipop/scene', query: { courseId: course.id } })
    },
    handleManageUnits(course) {
      // console.log('%c [ course ]-167', 'font-size:13px; background:pink; color:#bf2c9f;', course)
      // this.rowData=course
      // // this.unitForm.courseId = course.id
      // // this.fetchUnitList(course.id)
      // this.isEditUnit = true

      if (course.type === 'SYNC') {
        this.$router.push({ name: 'lollipop/syncUnit', query: { courseId: course.id } })
      }

      if (course.type === 'LETTER') {
        this.$router.push({ name: 'lollipop/letterUnit', query: { courseId: course.id } })
      }




    },

  }
}
</script>

<style scoped>
.course-management {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 48px);
}

/* 顶部按钮样式 */
.el-button {
  margin-right: 12px;
  transition: all 0.3s;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.course-table,
.scene-table,
.unit-table {
  margin-top: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 表格内按钮组样式 */
.el-table .el-button {
  margin: 4px;
  padding: 7px 12px;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px;
  background-color: #f5f7fa;
  margin-right: 0;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 表单样式 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input),
:deep(.el-select) {
  width: 100%;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-info .publish-time {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.version-info .el-tag {
  width: fit-content;
}
</style>
