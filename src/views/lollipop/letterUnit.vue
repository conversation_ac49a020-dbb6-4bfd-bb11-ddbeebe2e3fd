<template>
  <div class="redbook-resource-unit">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAddUnit"
        >添加字母</el-button
      > -->
      <el-button type="primary" icon="el-icon-plus" @click="handleUploadLetter">批量上传字母图片</el-button>

      <el-button style="margin-left: auto;" icon="el-icon-back" @click="$router.back()">返回</el-button>
    </div>

    <!-- 单元列表表格 -->
    <el-table
      border
      v-loading="loading"
      :data="unitList"
      style="width: 100%"
      class="unit-table"
      row-key="id"
    >
      <el-table-column
        prop="unitId"
        align="center"
        width="150"
        label="ID"
      ></el-table-column>
      <el-table-column
        prop="unitName"
        align="center"
        label="单元名"
        width="150"
      ></el-table-column>
      <el-table-column align="center" label="歌曲" width="120">
        <template slot-scope="{ row }">
          <el-button size="mini" v-if="row.song" type="success" icon="el-icon-video-play" @click="handlePreviewSong(row)"> 预览</el-button>
          <FileUpload v-else accept=".mp4,.mov" uploadType="3" :formData="{ unitId: row.unitId }" @onSuccess="handleUploadSuccess($event, row)" />
        </template>
      </el-table-column>
      <el-table-column label="字母" align="center"  width="400">
        <template slot-scope="{ row }">
          <el-card class="letter-card" shadow="hover">
            <div class="card-header">
              <span class="header-title">字母信息</span>
              <el-button
                type="text"
                size="mini"
                @click="handleEditUnit(row)"
              >
                <i class="el-icon-edit-outline"></i>
                编辑
              </el-button>
            </div>
            <div class="letter-basic">
              <div class="letter-row">
                <div class="letter-item">
                  <div class="letter-title">小写</div>
                  <div class="letter-content">
                    <span class="letter-text">{{ row.letter.letter }}</span>
                    <el-image
                      v-if="row.letter.letterImgFile"
                      :src="row.letter.letterImgFile"
                      fit="contain"
                      class="letter-image"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
                <div class="letter-item">
                  <div class="letter-title">大写</div>
                  <div class="letter-content">
                    <span class="letter-text">{{
                      row.letter.letterUpCase
                    }}</span>
                    <el-image
                      v-if="row.letter.letterUpCaseImgFile"
                      :src="row.letter.letterUpCaseImgFile"
                      fit="contain"
                      class="letter-image"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
              </div>
              <div class="sound-section" v-if="row.letter.soundFile">
                <audio controls class="audio-player">
                  <source :src="row.letter.soundFile" type="audio/mpeg" />
                </audio>
              </div>
            </div>
            <div
              class="example-section"
              v-if="row.letter.exampleList && row.letter.exampleList.length"
            >
              <div class="section-title">
                <i class="el-icon-collection-tag"></i>
                <span>例词列表</span>
              </div>
              <div class="example-list">
                <div
                  v-for="(item, index) in row.letter.exampleList"
                  :key="index"
                  class="example-item"
                >
                  <div class="example-main">
                    <span class="example-order">{{ item.displayOrder }}.</span>
                    <span class="example-word">{{ item.spell }}</span>
                    <span class="example-meaning">{{ item.meaningZhCN }}</span>
                  </div>
                  <div class="example-media">
                    <el-image
                      v-if="item.imgFile"
                      :src="item.imgFile"
                      fit="contain"
                      class="example-image"
                    />
                    <audio v-if="item.soundFile" controls class="audio-player">
                      <source :src="item.soundFile" type="audio/mpeg" />
                    </audio>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </template>
      </el-table-column>
      <el-table-column label="例句列表" align="center" width="800">
        <template slot-scope="{ row }">
          <el-card class="sentence-card" shadow="hover">
            <div class="card-header">
              <span class="header-title">例句列表</span>
              <el-button
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  plain
                  :disabled="row.letterList && row.letterList.length >= 2"
                  @click="handleAddWord(row)"
                >
                  添加例句
                </el-button>
            </div>
            <div class="sentence-list">
              <div
                v-for="(item, index) in row.letterList"
                :key="index"
                class="sentence-item"
              >
                <div class="sentence-main">
                  <div class="sentence-header">
                    <span class="order"
                      >{{ item.displayOrder || index + 1 }}.</span
                    >
                    <span class="spell">{{ item.spell }}</span>
                    <div class="action-buttons">
                      <el-button
                        type="text"
                        size="mini"
                        @click="handleEditWord(row, index)"
                      >
                        <i class="el-icon-edit-outline"></i>
                      </el-button>
                      <el-button
                        type="text"
                        size="mini"
                        @click="handleDeleteWord(row, index)"
                        class="delete"
                      >
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </div>
                  </div>
                  <div class="sentence-meaning">{{ item.meaningZhCN }}</div>
                </div>
                <div class="media-section">
                  <el-image
                    v-if="item.imgFile"
                    :src="item.imgFile"
                    fit="contain"
                    class="sentence-image"
                  />
                  <audio v-if="item.soundFile" controls class="sentence-audio">
                    <source :src="item.soundFile" type="audio/mpeg" />
                  </audio>
                </div>
              </div>
            </div>
          </el-card>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            :type="row.onLine ? 'danger' : 'success'"
            @click="handleStatusChange(row)"
          >
            {{ row.onLine ? "下架" : "上架" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加字母对话框 -->
    <el-dialog
      title="添加字母"
      :visible.sync="dialogAddUnitVisible"
      width="400px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="unitForm"
        :model="unitForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="字母" prop="letter">
          <el-select
            v-model="unitForm.letter"
            placeholder="请选择字母"
            style="width: 100%"
          >
            <el-option
              v-for="letter in letterOptions"
              :key="letter"
              :label="letter"
              :value="letter"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogAddUnitVisible = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmitUnit"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <!-- 添加/编辑例词对话框 -->
    <el-dialog
      :title="isEditExample ? '编辑例词' : '添加例词'"
      :visible.sync="dialogExampleVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="handleExampleDialogClose"
    >
      <el-form
        ref="exampleForm"
        :model="exampleForm"
        :rules="exampleRules"
        label-width="80px"
      >
        <el-form-item label="例词" prop="spell">
          <el-input
            v-model="exampleForm.spell"
            placeholder="请输入例词"
          ></el-input>
        </el-form-item>
        <el-form-item label="中文词义" prop="meaningZhCN">
          <el-input
            v-model="exampleForm.meaningZhCN"
            placeholder="请输入中文词义"
          ></el-input>
        </el-form-item>
        <el-form-item label="显示顺序" prop="displayOrder">
          <el-input-number
            v-model="exampleForm.displayOrder"
            :min="1"
            :max="99"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="图片" prop="imgFile">
          <FileUpload
            v-if="dialogExampleVisible"
            accept=".jpg,.png"
            uploadType="1"
            :formData="{
              unitId,
            }"
            :value="exampleForm.imgFile"
            @onSuccess="handleImageUploadSuccess"
          />
          <el-image
            v-if="exampleForm.imgFile"
            :src="exampleForm.imgFile"
            class="preview-image"
            fit="contain"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
        <el-form-item label="音频" prop="soundFile">
          <FileUpload
            v-if="dialogExampleVisible"
            accept=".mp3,.wav"
            uploadType="2"
            :formData="{
              unitId,
            }"
            :value="exampleForm.soundFile"
            @onSuccess="handleAudioUploadSuccess"
          />
          <audio v-if="exampleForm.soundFile" controls class="preview-audio">
            <source :src="exampleForm.soundFile" type="audio/mpeg" />
          </audio>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogExampleVisible = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmitExample"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <!-- 儿歌预览对话框 -->
    <el-dialog title="儿歌预览" :visible.sync="songVisible" width="640px" :close-on-click-modal="false" @close="handleSongDialogClose">
      <div class="video-container">
        <video v-if="songUrl" width="100%" controls :src="songUrl" @error="handleVideoError"></video>
      </div>
      <div slot="footer" style="display: flex;">
        <FileUpload btnText="更换视频" size="medium" accept=".mp4,.mov" uploadType="3" :formData="{ unitId: songData && songData.unitId }"  @onSuccess="replaceVideoHandle" />
        <el-button style="margin-left: 20px;" type="danger" @click="removeVideoHandle">删除视频</el-button>
        <el-button style="margin-left: auto;" @click="handleSongDialogClose">关 闭</el-button>
      </div>
    </el-dialog>


    <!-- 上传字母图片 -->
    <el-dialog title="批量上传文件" :visible.sync="batchUploadisShow" width="40%">
      <batchUploadLetter />
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchUploadisShow = false">取 消</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>


import {
  letterUnitList,
  letterUnitAdd,
  letterUnitUpdate,
  letterExampleAdd,
  letterExampleUpdate,
  letterExampleDelete,
  unitSetStatus,

  addVideo,
  updateVideo,
  deleteVideo,
} from "@/api/xhb/lollipop";
import FileUpload from "./components/FileUpload.vue";
import batchUploadLetter from "./components/batchUploadletter.vue";

export default {
  name: "RedbookResourceUnit",
  components: {
    FileUpload,
    batchUploadLetter
  },
  data() {
    return {
      batchUploadisShow: false,
      dialogAddUnitVisible: false,
      unitList: [],
      isEditUnit: false,
      songUrl: null,
      songData: null,
      loading: false,
      submitting: false,
      courseId: "",
      songVisible:false,
      letterOptions: "ABCDEFGHIJKLMNOPQRSTUVWXYZ".toLowerCase().split(""),
      unitForm: this.getInitialUnitForm(),
      rules: {
        letter: [{ required: true, message: "请选择字母", trigger: "change" }],
      },
      dialogExampleVisible: false,
      isEditExample: false,
      unitId: "",
      currentLetterId: null,
      exampleForm: this.getInitialExampleForm(),
      exampleRules: {
        spell: [{ required: true, message: "请输入例词", trigger: "blur" }],
        meaningZhCN: [
          { required: true, message: "请输入中文词义", trigger: "blur" },
        ],
        displayOrder: [
          { required: true, message: "请输入显示顺序", trigger: "blur" },
        ],
      },
    };
  },

  watch: {
    $route: {
      handler(route) {
        this.courseId = route.query.courseId;
        this.fetchUnitList(route.query.courseId);
      },
      immediate: true,
    },
  },

  methods: {
    handleUploadLetter(){
        this.batchUploadisShow = true
    },

    // 儿歌相关方法
    handlePreviewSong(row) {
      console.log('%c [  ]-472', 'font-size:13px; background:pink; color:#bf2c9f;', row)
      this.songData = row
      if (!row.song?.videoUrl) {
        this.$message.warning('视频地址无效')
        return
      }
      this.songUrl = row.song.videoUrl
      this.songVisible = true
    },
      // 上传相关方法
    async handleUploadSuccess(url, row) {
        const query = {
          unitId: row.unitId,
          videoUrl: url,
          subtitleCnUrl: null,
          subtitleEnUrl: null
        }
        await addVideo(query)
        await this.fetchUnitList(this.unitForm.courseId)
    },
    async replaceVideoHandle(url){
      try {
        const query = {
          unitId: this.songData.unitId,
          videoUrl: url,
          subtitleCnUrl: null,
          subtitleEnUrl: null
        }
        await updateVideo(query)
        this.$message.success('更新成功')
        this.songVisible = false
        await this.fetchUnitList(this.unitForm.courseId)
      } catch (error) {
        this.$message.error('更新成功' + (error.message || '未知错误'))
      }
    },
    async removeVideoHandle() {
      try {
        if (!this.songData?.song.id) {
          this.$message.warning('无效的视频数据')
          return
        }
        const resp = await deleteVideo({ id:this.songData.song.id})
        if (resp.code === 200) {
          this.$message.success('删除成功')
          this.songVisible = false
          await this.fetchUnitList(this.courseId);
        } else {
          this.$message.error(resp.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败：' + (error.message || '未知错误'))
      }
    },


    handleSongDialogClose() {
      this.songVisible = false
      this.songUrl = null
      this.songData = null
    },
    handleVideoError() {
      this.$message.error('视频加载失败')
    },



    // 发布相关方法
    async handleStatusChange(row) {
      await unitSetStatus({
        unitId: row.unitId,
        onLine: !row.onLine,
      });
      this.$message.success(row.onLine ? "上架成功" : "下架成功");

      await this.fetchUnitList(this.courseId);
    },

    // 初始化表单数据
    getInitialUnitForm() {
      return {
        letter: "",
        courseId: this.$route.query.courseId,
      };
    },

    async fetchUnitList(courseId) {
      if (!courseId) return;
      try {
        this.loading = true;
        const response = await letterUnitList({ courseId });
        this.unitList = response.data;
      } catch (error) {
        this.$message.error(error.message || "获取字母列表失败");
      } finally {
        this.loading = false;
      }
    },

    handleAddUnit() {
      this.isEditUnit = false;
      this.dialogAddUnitVisible = true;
    },

    async handleSubmitUnit() {
      try {
        await this.$refs.unitForm.validate();
        this.submitting = true;

        const params = {
          ...this.unitForm,
        };

        if (this.isEditUnit) {
          await letterUnitUpdate(params);
          this.$message.success("更新成功");
        } else {
          await letterUnitAdd(params);
          this.$message.success("添加成功");
        }

        await this.fetchUnitList(this.courseId);
        this.dialogAddUnitVisible = false;
        this.resetUnitForm();
      } catch (error) {
        this.$message.error(error.message || "操作失败");
      } finally {
        this.submitting = false;
      }
    },
    handleEditUnit(row) {
      this.unitForm = {
        ...this.unitForm,
        unitId: row.letter.unitId,
        letter: row.letter.letter,
      };
      this.isEditUnit = true;
      this.dialogAddUnitVisible = true;
    },

    handleDialogClose() {
      this.$refs.unitForm.resetFields();
      this.resetUnitForm();
    },

    resetUnitForm() {
      this.unitForm = this.getInitialUnitForm();
      this.isEditUnit = false;
    },

    // 初始化例词表单
    getInitialExampleForm() {
      return {
        spell: "",
        meaningZhCN: "",
        displayOrder: 1,
        imgFile: "",
        soundFile: "",
        letterId: "",
      };
    },

    // 打开添加例词对话框
    handleAddWord(row) {
      this.isEditExample = false;
      this.currentLetterId = row.letter.id;
      this.unitId = row.unitId;
      this.exampleForm = {
        ...this.getInitialExampleForm(),
        letterId: row.letter.id,
        displayOrder: 1,
        spell: "",
        meaningZhCN: "",
        imgFile: "",
        soundFile: "",
      };
      this.dialogExampleVisible = true;
    },

    // 打开编辑例词对话框
    handleEditWord(row, index) {
      const example = row.letterList[index];
      this.isEditExample = true;
      this.currentLetterId = row.letter.id;
      this.unitId = row.unitId;
      this.exampleForm = {
        id: example.id,
        letterId: row.letter.id,
        displayOrder: example.displayOrder,
        spell: example.spell,
        meaningZhCN: example.meaningZhCN,
        imgFile: example.imgFile || "",
        soundFile: example.soundFile || "",
      };
      this.dialogExampleVisible = true;
    },

    // 删除例词
    async handleDeleteWord(row, index) {
      try {
        await this.$confirm("确认删除该例词吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const example = row.letterList[index];
        await letterExampleDelete({ id: example.id });
        this.$message.success("删除成功");
        await this.fetchUnitList(this.courseId);
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(error.message || "删除失败");
        }
      }
    },

    // 提交例词表单
    async handleSubmitExample() {
      try {
        await this.$refs.exampleForm.validate();
        this.submitting = true;

        const params = {
          displayOrder: this.exampleForm.displayOrder,
          imgFile: this.exampleForm.imgFile,
          letterId: this.exampleForm.letterId,
          meaningZhCN: this.exampleForm.meaningZhCN,
          soundFile: this.exampleForm.soundFile,
          spell: this.exampleForm.spell,
        };

        if (this.isEditExample) {
          params.id = this.exampleForm.id;
          await letterExampleUpdate(params);
          this.$message.success("更新成功");
        } else {
          await letterExampleAdd(params);
          this.$message.success("添加成功");
        }

        await this.fetchUnitList(this.courseId);
        this.dialogExampleVisible = false;
        this.resetExampleForm();
      } catch (error) {
        this.$message.error(error.message || "操作失败");
      } finally {
        this.submitting = false;
      }
    },

    // 关闭例词对话框
    handleExampleDialogClose() {
      this.$refs.exampleForm.resetFields();
      this.resetExampleForm();
    },

    // 重置例词表单
    resetExampleForm() {
      this.exampleForm = this.getInitialExampleForm();
      this.isEditExample = false;
      this.currentLetterId = null;
      this.unitId = "";
    },

    // 处理图片上传成功
    handleImageUploadSuccess(url) {
      this.exampleForm.imgFile = url;
    },

    // 处理音频上传成功
    handleAudioUploadSuccess(url) {
      this.exampleForm.soundFile = url;
    },
  },
};
</script>

<style lang="scss" scoped>

.sentence-list{
  display: flex;
    gap: 10px;
}

.redbook-resource-unit {
  padding: 20px;
  background: #f9fafc;

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;

    .el-button {
      transition: all 0.3s;
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .unit-table {
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    :deep(.el-table__header) {
      th {
        background: #f5f7fa;
        color: #606266;
        font-weight: 600;
        height: 48px;
      }
    }

    :deep(.el-table__row) {
      transition: all 0.3s;
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  .letter-card,
  .sentence-card {
    margin: 4px;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      padding-bottom: 10px;
      margin-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        font-size: 14px;
        color: #303133;
        font-weight: 600;
        position: relative;
        padding-left: 12px;

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 14px;
          background: linear-gradient(to bottom, #409eff, #53a8ff);
          border-radius: 3px;
        }
      }

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }

    :deep(.el-card__body) {
      padding: 12px;
    }
  }

  .letter-basic {
    margin-bottom: 12px;

    .letter-row {
      margin-bottom: 10px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      display: flex;
      justify-content: space-around;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .letter-item {
      padding: 0 12px;
      text-align: center;
    }

    .letter-title {
      font-size: 12px;
      margin-bottom: 8px;
      color: #909399;
      font-weight: 500;
    }

    .letter-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .letter-text {
      font-size: 26px;
      color: #303133;
      font-weight: 600;
    }

    .letter-image {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      border: 1px solid #ebeef5;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .sound-section {
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);

      .audio-player {
        width: 100%;
        height: 30px;
      }
    }
  }

  .example-section {
    .section-title {
      font-size: 13px;
      margin-bottom: 10px;
      color: #606266;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }
    }

    .example-list {
      .example-item {
        padding: 10px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        transition: all 0.3s;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);

        &:hover {
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .example-main {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
        }

        .example-order {
          font-size: 12px;
          margin-right: 8px;
          color: #409eff;
          font-weight: 600;
        }

        .example-word {
          font-size: 14px;
          margin-right: 10px;
          color: #303133;
          font-weight: 500;
        }

        .example-meaning {
          font-size: 12px;
          color: #909399;
        }

        .example-media {
          padding: 8px;
          gap: 10px;
          display: flex;
          align-items: center;
          background: #fff;
          border-radius: 4px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }

        .example-image {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .audio-player {
          height: 28px;
          width: 100%;
        }
      }
    }
  }

  .sentence-list {
    .sentence-item {
      padding: 12px;
      margin-bottom: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      transition: all 0.3s;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
      width: 360px;
      &:hover {
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .sentence-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;

        .order {
          font-size: 12px;
          margin-right: 8px;
          color: #409eff;
          font-weight: 600;
        }

        .spell {
          font-size: 14px;
          flex: 1;
          color: #303133;
          font-weight: 500;
        }

        .action-buttons {
          display: flex;
          gap: 6px;

          .el-button {
            padding: 4px 8px;
            transition: all 0.3s;

            i {
              font-size: 14px;
            }

            &.delete {
              color: #f56c6c;
              &:hover {
                background: rgba(245, 108, 108, 0.1);
              }
            }

            &:hover {
              background: rgba(64, 158, 255, 0.1);
            }
          }
        }
      }

      .sentence-meaning {
        font-size: 12px;
        color: #909399;
        margin-bottom: 10px;
        padding-left: 20px;
      }

      .media-section {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        .sentence-image {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .sentence-audio {
          flex: 1;
          height: 28px;
        }
      }
    }

    .add-button-wrapper {
      margin-top: 16px;
      text-align: center;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 6px;
      transition: all 0.3s;

      .el-button {
        font-size: 13px;
        padding: 8px 16px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    color: #909399;
    font-size: 14px;
  }

  .preview-image {
    width: 80px;
    height: 80px;
    margin-top: 10px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .preview-audio {
    width: 100%;
    margin-top: 10px;
  }

  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      padding: 15px 20px;
      margin-right: 0;
      border-bottom: 1px solid #ebeef5;
      background: #f8f9fa;
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
      background: #f8f9fa;
    }
  }
}
</style>
