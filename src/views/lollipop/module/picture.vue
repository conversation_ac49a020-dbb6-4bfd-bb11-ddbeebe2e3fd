<template>
  <div class="page-container" v-loading="isLoading"
       element-loading-text="加载中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(255, 255, 255, 0.9)">
    <!-- Header Actions -->
    <div class="action-header">
      <div class="left">
        <el-button icon="el-icon-back" @click="$router.back()">返回</el-button>
      </div>
      <div class="right">
        <span class="page-title">绘本详情</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && !mainData" class="empty-container">
      <div class="empty-content">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
        >添加绘本</el-button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="content-container" v-if="!isLoading && mainData">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="card-header" @click="toggleInfoExpand">
          <div class="header-left">
            <span class="title">基本信息</span>
            <div class="quick-info">
              <span class="info-tag">ID: {{ mainData.id }}</span>
              <span class="info-tag">排序: {{ mainData.sort }}</span>
              <span class="info-tag">Level {{ mainData.level }}</span>
            </div>
          </div>
          <div class="header-right">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-edit"
                @click.stop="handleEdit(mainData)"
                style="width: auto; margin-right: 10px;"
              >修改</el-button>
              <el-button
                type="danger"
                size="small"
                icon="el-icon-delete"
                @click.stop="handleDelete(mainData)"
                style="width: auto;"
              >删除</el-button>
            </div>
            <i :class="['expand-icon', infoExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
          </div>
        </div>

        <!-- 展开的详细信息 -->
        <div class="card-body" v-show="infoExpanded">
          <div class="profile-content">
            <!-- 左侧缩略图 -->
            <div class="thumb-section">
              <div class="thumb-wrapper" @click="handlePreviewImage(mainData.thumbnail)">
                <img v-if="mainData.thumbnail" :src="mainData.thumbnail" alt="缩略图">
                <div v-else class="no-image">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </div>
            </div>

            <!-- 右侧信息 -->
            <div class="main-info">
              <div class="info-header">
                <div class="title-section">
                  <div class="main-title">{{ mainData.title }}</div>
                  <div class="sub-title">{{ mainData.enTitle }}</div>
                </div>
                <div class="meta-info">
                  <span class="meta-item">ID: {{ mainData.id }}</span>
                  <span class="meta-item">Level {{ mainData.level }}</span>
                  <span class="meta-item">排序 {{ mainData.sort }}</span>
                </div>
              </div>

              <div class="desc-section">
                <div class="desc-item">
                  <div class="desc-content">{{ mainData.descCh }}</div>
                  <div class="desc-label">中文简介</div>
                </div>
                <div class="desc-item">
                  <div class="desc-content">{{ mainData.descEn }}</div>
                  <div class="desc-label">英文简介</div>
                </div>
                <div class="audio-wrapper" v-if="mainData.titleSound">
                  标题音频：
                  <audio controls style="height: 30px;">
                    <source :src="mainData.titleSound" type="audio/mpeg">
                  </audio>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容列表卡片 -->
      <div class="content-card">
        <div class="card-header" @click="toggleContentExpand">
          <div class="header-left">
            <span class="title">绘本内容</span>
            <div class="quick-info">
              <span class="info-tag">{{ mainData.contentList.length || 0 }}页内容</span>
            </div>
          </div>
          <div class="header-right">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click.stop="handleAddContent"
            >添加内容</el-button>
            <i :class="['expand-icon', contentExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
          </div>
        </div>

        <div class="card-body" v-show="contentExpanded">
          <div class="content-table-wrapper">
            <el-table
              border
              :data="mainData.contentList"
              style="width: 100%"
            >
              <el-table-column label="本页id" prop="id" width="100" />
              <el-table-column label="图片" width="150">
                <template slot-scope="scope">
                  <div class="table-image" @click.stop="handlePreviewImage(scope.row.picUrl)">
                    <template v-if="scope.row.picUrl">
                      <div>
                        <video  v-if="scope.row.picUrl.indexOf('.mp4') != -1 || scope.row.picUrl.indexOf('.mov') != -1" style="width: 100%;"  :src="scope.row.picUrl" ></video>
                        <img v-else  :src="scope.row.picUrl" alt="内容图片" style="height: 80px; object-fit: cover;">

                      </div>
                    </template>
                    <div v-else class="no-image">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="排序" prop="sort" width="80" align="center" />
              <el-table-column label="句子" min-width="400">
                <template slot-scope="scope">
                  <div v-if="scope.row.sentenceList && scope.row.sentenceList.length" class="sentence-list">
                    <div v-for="(sentence, index) in scope.row.sentenceList" :key="sentence.id" class="sentence-item">
                      <div class="sentence-content">
                        <span style="color: red;">{{ sentence.coreSentence==1?'核心句':'' }}</span>
                        <div class="sentence-text">
                          <div class="en-text">{{ sentence.exampleEnUS }}</div>
                          <div class="zh-text">{{ sentence.exampleZhCN }}</div>
                        </div>
                        <div class="sentence-meta">
                          <span class="speaker-tag">
                            <i class="el-icon-user"></i>
                            <span>{{ sentence.speaker }}</span>
                          </span>
                        </div>
                        <div class="sentence-actions">
                          <div class="audio-wrapper" v-if="sentence.soundFile">
                            英文：
                            <audio controls style="height: 30px;">
                              <source :src="sentence.soundFile" type="audio/mpeg">
                            </audio>
                          </div>
                          <div class="audio-wrapper" v-if="sentence.cnSoundFile">
                            中文：
                            <audio controls style="height: 30px;">
                              <source :src="sentence.cnSoundFile" type="audio/mpeg">
                            </audio>
                          </div>
                          <div class="button-group">
                            <el-button
                              type="text"
                              size="small"
                              @click.stop="handleEditSentence(scope.row, sentence)"
                            >编辑</el-button>
                            <el-button
                              type="text"
                              size="small"
                              class="delete-btn"
                              @click.stop="handleDeleteSentence(scope.row, sentence)"
                            >删除</el-button>
                          </div>
                        </div>
                      </div>
                      <!-- <el-button
                      
                        type="primary"
                        size="small"
                        icon="el-icon-plus"
                        style="margin-top: 10px;"
                        @click.stop="handleAddSentence(scope.row)"
                      >添加句子</el-button> -->
                    </div>
                  </div>
                  <div  class="empty-sentence">
                    <el-button
                      type="primary"
                      size="small"
                      icon="el-icon-plus"
                      @click.stop="handleAddSentence(scope.row)"
                    >添加句子</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template slot-scope="scope">
                  <div class="button-group">
                    <el-button
                      type="text"
                      size="small"
                      @click.stop="handleEditContent(scope.row)"
                    >修改</el-button>
                    <el-button
                      type="text"
                      size="small"
                      class="delete-btn"
                      @click.stop="handleDeleteContent(scope.row)"
                    >删除</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="!mainData.contentList || !mainData.contentList.length" class="empty-list">
              暂无内容，请点击添加
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Dialog -->
    <el-dialog
      top="6vh"
      :title="isEdit ? '修改信息' : '添加信息'"
      :visible.sync="dialogVisible"
      width="50%"
      custom-class="custom-dialog"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="中文标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入中文标题" />
        </el-form-item>
        <el-form-item label="英文标题" prop="enTitle">
          <el-input v-model="formData.enTitle" placeholder="请输入英文标题" />
        </el-form-item>
        <el-form-item label="中文简介" prop="descCh">
          <el-input
            v-model="formData.descCh"
            type="textarea"
            :rows="3"
            placeholder="请输入中文简介"
          />
        </el-form-item>
        <el-form-item label="英文简介" prop="descEn">
          <el-input
            v-model="formData.descEn"
            type="textarea"
            :rows="3"
            placeholder="请输入英文简介"
          />
        </el-form-item>
        <el-form-item label="排序值" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="Level" prop="level">
          <el-input-number
            v-model="formData.level"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="缩略图" prop="thumbnail">
          <div class="upload-preview">
            <!-- <el-input placeholder="请输入图片名称" v-model="formData.thumbnail">
            </el-input> -->
            <FileUpload
              accept=".jpg,.png"
              uploadType="1"
              :formData="{ unitId: formData.unitId }"
              @onSuccess="handleImageSuccess"
            />
            <!-- 图片预览 -->
            <div v-if="formData.thumbnail" class="image-preview">
              <!-- <img width="200"  :src="'https://kids-cdn.xiaohongben888.com/resource/picture/'+formData.thumbnail" alt="预览图"> -->
              <img width="200"  :src="formData.thumbnail" alt="预览图">
              <div class="preview-mask">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleRemoveImage"
                >删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="音频文件" prop="titleSound">
          <div class="upload-preview">
            <!-- <el-input placeholder="请输入音频文件名称" v-model="sentenceFormData.soundFile"></el-input> -->
            <FileUpload
              accept=".mp3,.wav"
              uploadType="2"
              :formData="{ unitId: formData.unitId }"
              @onSuccess="handleSoundFileSuccessFm"
            />
            <!-- 音频预览 -->
            <div v-if="formData.titleSound " class="audio-preview">
              <audio controls>
                <!-- <source :src="'https://kids-cdn.xiaohongben888.com/resource/sound/'+sentenceFormData.soundFile" type="audio/mpeg"> -->\
                <source :src="formData.titleSound" type="audio/mpeg">
              </audio>
              <div class="file-name">{{ getFileName(formData.titleSound) }}</div>
              <el-button
                type="text"
                icon="el-icon-delete"
                class="delete-btn"
                @click="handleRemoveSoundFileFm"
              >删除</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 添加图片预览弹窗 -->
    <el-dialog :visible.sync="imagePreviewVisible" append-to-body>
      <div>
        <video  v-if="previewImageUrl.indexOf('.mp4') != -1 || previewImageUrl.indexOf('.mov') != -1" style=" width:100%;height:100%; object-fit: cover;" :src="previewImageUrl" controls></video>
        <img v-else  width="100%" :src="previewImageUrl" alt="预览图">

      </div>
    </el-dialog>

    <!-- 内容添加/编辑弹窗 -->
    <el-dialog
      :title="isEditContent ? '编辑内容' : '添加内容'"
      :visible.sync="contentDialogVisible"
      width="40%"
      custom-class="custom-dialog"
    >
      <el-form
        ref="contentForm"
        :model="contentFormData"
        :rules="contentRules"
        label-width="100px"
      >
        <el-form-item label="图片/视频" prop="picUrl">
          <div class="upload-preview">
            <!-- <el-input placeholder="请输入图片/视频名称" v-model="contentFormData.picUrl">
            </el-input> -->
            <FileUpload
              accept=".jpg,.png,.gif,.mov,.mp4"
              uploadType="1"
              :formData="{ unitId: formData.unitId }"
              @onSuccess="handleContentImageSuccess"
            />
            <!-- 图片预览 -->
            <div v-if="contentFormData.picUrl" class="image-preview">
              <div>
                <video  controls v-if="contentFormData.picUrl.indexOf('.mp4') != -1 || contentFormData.picUrl.indexOf('.mov') != -1"  style="width: 100%;" :src="contentFormData.picUrl" ></video>
                <img v-else :src="contentFormData.picUrl" alt="内容图片" style="height: 80px; object-fit: cover;">
                <!-- <video  controls v-if="contentFormData.picUrl.indexOf('.mp4') != -1 || contentFormData.picUrl.indexOf('.mov') != -1"  style="width: 100%;" :src="'https://kids-cdn.xiaohongben888.com/resource/picture/'+contentFormData.picUrl" ></video>
                <img v-else :src="'https://kids-cdn.xiaohongben888.com/resource/picture/'+contentFormData.picUrl" alt="内容图片" style="height: 80px; object-fit: cover;"> -->
              </div>

              <div class="preview-mask">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleRemoveContentImage"
                >删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="句子数量" prop="sentenceNum">
          <el-input-number
            v-model="contentFormData.sentenceNum"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="contentFormData.sort"
            :min="0"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="contentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleContentSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 句子添加/编辑弹窗 -->
    <el-dialog
      :title="isEditSentence ? '编辑句子' : '添加句子'"
      :visible.sync="sentenceDialogVisible"
      width="50%"
      custom-class="custom-dialog"
    >
      <el-form
        ref="sentenceForm"
        :model="sentenceFormData"
        :rules="sentenceRules"
        label-width="120px"
      >
        <el-form-item label="例句英文" prop="exampleEnUS">
          <el-input
            v-model="sentenceFormData.exampleEnUS"
            type="textarea"
            :rows="2"
            placeholder="请输入例句英文"
          />
        </el-form-item>
        <el-form-item label="例句中文" prop="exampleZhCN">
          <el-input
            v-model="sentenceFormData.exampleZhCN"
            type="textarea"
            :rows="2"
            placeholder="请输入例句中文"
          />
        </el-form-item>

        <el-form-item label="发言人" prop="speaker">
          <el-input
            v-model="sentenceFormData.speaker"
            placeholder="请输入发言人"
          />
        </el-form-item>
        <el-form-item label="是否为核心句子" prop="coreSentence">
          <el-radio-group v-model="sentenceFormData.coreSentence">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="sentenceFormData.sort"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="音频文件" prop="soundFile">
          <div class="upload-preview">
            <!-- <el-input placeholder="请输入音频文件名称" v-model="sentenceFormData.soundFile"></el-input> -->
            <FileUpload
              accept=".mp3,.wav"
              uploadType="2"
              :formData="{ unitId: formData.unitId }"
              @onSuccess="handleSoundFileSuccess"
            />
            <!-- 音频预览 -->
            <div v-if="sentenceFormData.soundFile " class="audio-preview">
              <audio controls>
                <!-- <source :src="'https://kids-cdn.xiaohongben888.com/resource/sound/'+sentenceFormData.soundFile" type="audio/mpeg"> -->\
                <source :src="sentenceFormData.soundFile" type="audio/mpeg">
              </audio>
              <div class="file-name">{{ getFileName(sentenceFormData.soundFile) }}</div>
              <el-button
                type="text"
                icon="el-icon-delete"
                class="delete-btn"
                @click="handleRemoveSoundFile"
              >删除</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="sentenceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSentenceSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPictureBook,
  addPictureBook,
  updatePictureBook,
  deletePictureBook,
  //插入绘本内容
  pictureBookContentAdd,
  pictureBookContentUpdate,
  pictureBookContentDelete,
  //插入内容的句子的接口
  pictureBookSentenceAdd,
  pictureBookSentenceDelete,
  pictureBookSentenceUpdate
} from "@/api/xhb/lollipop";
import FileUpload from '../components/FileUpload.vue'

export default {
  name: 'PictureBookManager',
  components: {
    FileUpload
  },

  data() {
    return {
      isLoading: true,  // 添加loading状态
      // ===== 绘本层级数据 =====
      mainData: null,
      dialogVisible: false,
      isEdit: false,
      formData: this.getInitialFormData(),
      rules: {
        title: [{ required: true, message: '请输入中文标题', trigger: 'blur' }],
        enTitle: [{ required: true, message: '请输入英文标题', trigger: 'blur' }],
        descCh: [{ required: false, message: '请输入中文简介', trigger: 'blur' }],
        descEn: [{ required: false, message: '请输入英文简介', trigger: 'blur' }],
        sort: [{ required: false, message: '请输入排序值', trigger: 'blur' }],
        level: [{ required: false, message: '请输入level值', trigger: 'blur' }],
        // thumbnail: [{ required: true, message: '请上传缩略图', trigger: 'change' }]
      },
      imagePreviewVisible: false,
      previewImageUrl: '',
      infoExpanded: true,




      // ===== 内容层级数据 =====
      contentExpanded: true,
      contentDialogVisible: false,
      isEditContent: false,
      contentFormData: this.getInitialContentFormData(),
      contentRules: {
        // picUrl: [{ required: true, message: '请上传图片', trigger: 'change' }],
        sentenceNum: [{ required: true, message: '请输入句子序号', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序值', trigger: 'blur' }]
      },




      // ===== 句子层级数据 =====
      sentenceDialogVisible: false,
      isEditSentence: false,
      currentContentRow: null,
      sentenceFormData: this.getInitialSentenceFormData(),
      sentenceRules: {
        exampleEnUS: [{ required: true, message: '请输入例句英文', trigger: 'blur' }],
        exampleZhCN: [{ required: true, message: '请输入例句中文', trigger: 'blur' }],
        // speaker: [{ required: true, message: '请输入发言人', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序值', trigger: 'blur' }],
        // soundFile: [{ required: true, message: '请上传音频文件', trigger: 'change' }],
        coreSentence: [
            { required: true, message: '请选择是否为核心句子', trigger: 'change' }
          ],
      },
      currentContent: null,
      currentLayout: 'default',
      layoutOptions: [
        { value: 'default', label: '默认布局' },
        { value: 'grid', label: '网格布局' },
        { value: 'list', label: '列表布局' }
      ],
      currentContentId: localStorage.getItem('currentContentId') || null,
    }
  },

  created() {
    this.checkExistingData()
  },

  methods: {
    // ===== 绘本层级方法 =====
    getInitialFormData() {
      return {
        title: '',
        enTitle: '',
        descCh: '',
        descEn: '',
        sort: 0,
        level: 1,
        thumbnail: '',
        unitId: this.$route.query.unitID
      }
    },

    async checkExistingData() {
      try {
        this.isLoading = true
        const res = await getPictureBook({ unitId: this.$route.query.unitID })
        if (res.code === 200) {
          this.mainData = res.data
          if (this.mainData && this.mainData.contentList && this.mainData.contentList.length > 0) {
            if (this.currentContentId) {
              const savedContent = this.mainData.contentList.find(item => item.id === Number(this.currentContentId));
              this.currentContent = savedContent || this.mainData.contentList[0];
            } else {
              this.currentContent = this.mainData.contentList[0];
            }
          }
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.isLoading = false
      }
    },

    // 绘本CRUD操作
    handleAdd() {
      this.isEdit = false
      this.formData = this.getInitialFormData()
      this.dialogVisible = true
    },

    handleEdit(data) {
      this.isEdit = true
      this.formData = {
        ...data,
        unitId: this.$route.query.unitID
      }
      // this.formData.thumbnail = this.formData.thumbnail.replace('https://kids-cdn.xiaohongben888.com/resource/picture/', '')
      this.dialogVisible = true
    },

    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        const api = this.isEdit ? updatePictureBook : addPictureBook
        // if(this.formData.thumbnail.trim()){
        //   this.formData.thumbnail = 'https://kids-cdn.xiaohongben888.com/resource/picture/'+this.formData.thumbnail.trim()
        // }
        const res = await api(this.formData)
        if (res.code === 200) {
          this.$message.success(this.isEdit ? '修改成功' : '添加成功')
          this.dialogVisible = false
          this.checkExistingData()
        }
      } catch (error) {
        this.$message.error(this.isEdit ? '修改失败' : '添加失败')
      }
    },

    async handleDelete(data) {
      try {
        await this.$confirm('确认删除该绘本?', '提示', {
          type: 'warning'
        })
        const res = await deletePictureBook({ id: data.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.mainData = null
          this.checkExistingData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 绘本图片处理
    handleImageSuccess(url) {
      this.formData.thumbnail = url
    },

    handleRemoveImage() {
      this.formData.thumbnail = ''
      this.$refs.form.clearValidate('thumbnail')
    },

    handlePreviewImage(url) {
      this.previewImageUrl = url
      this.imagePreviewVisible = true
    },

    // ===== 内容层级方法 =====
    getInitialContentFormData() {
      return {
        picUrl: '',
        sentenceNum: 1,
        sort: 0,
        pictureBookId: this.mainData?.id,
        unitId: this.$route.query.unitID
      }
    },

    // 内容CRUD操作
    handleAddContent() {
      this.isEditContent = false
      this.contentFormData = this.getInitialContentFormData()
      this.contentDialogVisible = true
    },

    handleEditContent(row) {
      this.isEditContent = true
      this.contentFormData = {
        ...this.getInitialContentFormData(),
        ...row
      }
      // this.contentFormData.picUrl = this.contentFormData.picUrl.replace('https://kids-cdn.xiaohongben888.com/resource/picture/', '')
      this.contentDialogVisible = true
    },

    async handleContentSubmit() {
      try {
        await this.$refs.contentForm.validate()
        const api = this.isEditContent ? pictureBookContentUpdate : pictureBookContentAdd
        // if(this.contentFormData.picUrl.trim()){
        //   this.contentFormData.picUrl = 'https://kids-cdn.xiaohongben888.com/resource/picture/'+this.contentFormData.picUrl.trim()
        // }
        const res = await api(this.contentFormData)
        if (res.code === 200) {
          this.$message.success(this.isEditContent ? '修改成功' : '添加成功')
          this.contentDialogVisible = false
          this.checkExistingData()
        }
      } catch (error) {
        this.$message.error('提交失败')
      }
    },

    async handleDeleteContent(row) {
      try {
        await this.$confirm('确认删除该内容?', '提示', {
          type: 'warning'
        })
        const res = await pictureBookContentDelete({ id: row.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.checkExistingData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 内容图片处理
    handleContentImageSuccess(url) {
      this.contentFormData.picUrl = url
    },

    handleRemoveContentImage() {
      this.contentFormData.picUrl = ''
      this.$refs.contentForm.clearValidate('picUrl')
    },

    // ===== 句子层级方法 =====
    getInitialSentenceFormData() {
      return {
        exampleEnUS: '',
        exampleZhCN: '',
        speaker: '',
        sort: 0,
        soundFile: '',
        contentId: '',
        unitId: this.$route.query.unitID,
        lastUpdateTime: new Date(),
        coreSentence:0
      }
    },

    // 句子CRUD操作
    handleAddSentence(row) {
      // 判断是否可以添加句子
      // if (row.sentenceList && row.sentenceList.length >= row.sentenceNum) {
      //   this.$message.warning(`当前内容最多允许添加${row.sentenceNum}个句子`)
      //   return
      // }

      this.isEditSentence = false
      this.sentenceFormData = {
        ...this.getInitialSentenceFormData(),
        contentId: row.id
      }
      this.sentenceDialogVisible = true
    },

    handleEditSentence(row, sentence) {
      this.isEditSentence = true
      this.sentenceFormData = {
        ...this.getInitialSentenceFormData(),
        ...sentence
      }
      // this.sentenceFormData.soundFile = this.sentenceFormData.soundFile.replace('https://kids-cdn.xiaohongben888.com/resource/sound/', '')
      this.sentenceDialogVisible = true
    },

    async handleSentenceSubmit() {
      try {
        await this.$refs.sentenceForm.validate()
        const api = this.isEditSentence ? pictureBookSentenceUpdate : pictureBookSentenceAdd
        // if(this.sentenceFormData.soundFile.trim()){
        //   this.sentenceFormData.soundFile = 'https://kids-cdn.xiaohongben888.com/resource/sound/'+this.sentenceFormData.soundFile.trim()
        // }
        const res = await api(this.sentenceFormData)
        if (res.code === 200) {
          this.$message.success(this.isEditSentence ? '修改成功' : '添加成功')
          this.sentenceDialogVisible = false
          if (this.currentContent) {
            this.currentContentId = this.currentContent.id;
            localStorage.setItem('currentContentId', this.currentContent.id);
          }
          this.checkExistingData()
        }
      } catch (error) {
        this.$message.error('提交失败')
      }
    },

    async handleDeleteSentence(row, sentence) {
      try {
        await this.$confirm('确认删除该句子?', '提示', {
          type: 'warning'
        })
        const res = await pictureBookSentenceDelete({ id: sentence.id })
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.checkExistingData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 句子音频处理
    handleSoundFileSuccess(url) {
      this.sentenceFormData.soundFile = url
    },
    handleSoundFileSuccessFm(url){
      this.formData.titleSound = url
    },

    handleRemoveSoundFile() {
      this.sentenceFormData.soundFile = ''
      this.$refs.sentenceForm.clearValidate('soundFile')
    },
    handleRemoveSoundFileFm() {
      this.formData.titleSound = ''
      this.$refs.form.clearValidate('titleSound')
    },

    getFileName(url) {
      if (!url) return ''
      return url.split('/').pop()
    },




    // ===== UI交互方法 =====
    toggleInfoExpand() {
      this.infoExpanded = !this.infoExpanded
    },

    toggleContentExpand() {
      this.contentExpanded = !this.contentExpanded
    },

    // 处理内容选择
    handleSelectContent(item) {
      this.currentContent = item;
      this.currentContentId = item.id;
      localStorage.setItem('currentContentId', item.id);
    },
  },

  watch: {
    'mainData.id': {
      handler(val) {
        if (val) {
          this.checkExistingData()
        }
      },
      immediate: true
    },
    currentLayout(newVal) {
      // 可以在这里添加布局切换的过渡动画
      console.log('Layout changed to:', newVal)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-button--small{
  width: 100%;
}

.page-container {
  padding: 32px;
  background-color: #f6f8fb;
  min-height: calc(100vh - 64px);

  .action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .left {
      .el-button {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;

        i {
          margin-right: 8px;
        }
      }
    }

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-bottom: 4px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40%;
        height: 3px;
        background: linear-gradient(90deg, #409eff, transparent);
        border-radius: 3px;
      }
    }
  }

  .empty-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(31, 35, 39, 0.05);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-content {
      text-align: center;

      .el-button {
        padding: 16px 32px;
        font-size: 16px;
        border-radius: 10px;
        transition: transform 0.3s;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }

  .content-container {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .info-card,
    .content-card {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 4px 24px rgba(31, 35, 39, 0.05);
      overflow: hidden;

      .card-header {
        padding: 12px 16px !important;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;
        border-bottom: 1px solid #eef1f6;

        &:hover {
          background-color: #f8fafd;
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 16px;

          .title {
            font-size: 24px !important;
            font-weight: 600;
            color: #1a1f36;
            position: relative;
            padding-left: 12px;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 3px;
              height: 16px;
              background: linear-gradient(180deg, #409eff, #79bbff);
              border-radius: 2px;
            }
          }

          .quick-info {
            display: flex;
            gap: 12px;

            .info-tag {
              padding: 2px 8px !important;
              background: #f0f7ff;
              color: #409eff;
              border-radius: 6px;
              font-size: 12px !important;
              font-weight: 500;
            }
          }
        }

        .header-right {
          display: flex;
          align-items: center;
          gap: 16px;

          .expand-icon {
            font-size: 16px;
            color: #909399;
            transition: transform 0.3s;
          }
        }
      }

      .card-body {
        padding: 16px;
      }
    }

    .info-card {
      background: #f0f7ff !important;

      .card-body {
        background: #f0f7ff;
      }
    }

    .content-card {
      background: #fff !important;
    }
  }
}

// 弹窗样式优化
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(31, 35, 39, 0.1);

  .el-dialog__header {
    padding: 24px 32px;
    border-bottom: 1px solid #eef1f6;
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1f36;
    }
  }

  .el-dialog__body {
    padding: 32px;
  }

  .el-dialog__footer {
    padding: 24px 32px;
    border-top: 1px solid #eef1f6;
  }

  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-form-item__label {
    font-weight: 500;
    color: #1a1f36;
  }

  .el-input__inner {
    border-radius: 8px;
  }

  .el-textarea__inner {
    border-radius: 8px;
  }

  .el-input-number {
    width: 180px;
  }

  .upload-preview {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    max-width: 400px; // 限制整个上传区域的最大宽度

    .image-preview {
      position: relative;
      width: 80px;  // 减小预览图尺寸
      height: 80px;
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid #ebeef5;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .preview-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.6);
        opacity: 0;
        transition: opacity 0.2s;

        .el-button {
          padding: 0;
          font-size: 12px;
          color: #fff;

          i {
            margin-right: 4px;
            font-size: 12px;
          }

          &:hover {
            color: #f56c6c;
          }
        }
      }

      &:hover .preview-mask {
        opacity: 1;
      }
    }

    :deep(.el-upload) {
      .el-button {
        padding: 8px 16px;
        font-size: 12px;
      }
    }
  }
}

.profile-content {
  display: flex;
  gap: 32px;
  background: #fff;
  border-radius: 12px;
  position: relative;
  padding: 24px;
  border: 1px solid #e6effd;

  // 左侧缩略图
  .thumb-section {
    width: 280px;
    flex-shrink: 0;

    .thumb-wrapper {
      width: 100%;
      height: 380px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      background: #f0f7ff;
      border: 1px solid #e6effd;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #409eff;
        font-size: 40px;
        background: linear-gradient(135deg, #f0f7ff 0%, #e6effd 100%);
      }
    }
  }

  // 右侧信息
  .main-info {
    flex: 1;
    min-width: 0;

    .info-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e6effd;

      .title-section {
        margin-bottom: 16px;

        .main-title {
          font-size: 24px;
          font-weight: 600;
          color: #1a1f36;
          margin-bottom: 8px;
          line-height: 1.2;
        }

        .sub-title {
          font-size: 16px;
          color: #4e5969;
          font-weight: 500;
        }
      }

      .meta-info {
        display: flex;
        gap: 12px;

        .meta-item {
          padding: 4px 12px;
          background: #f0f7ff;
          color: #409eff;
          border-radius: 6px;
          font-size: 13px;
          font-weight: 500;
          border: 1px solid #e6effd;
        }
      }
    }

    .desc-section {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;

      .desc-item {
        position: relative;

        .desc-content {
          padding: 20px;
          background: #f8fafd;
          border-radius: 8px;
          color: #1a1f36;
          font-size: 14px;
          line-height: 1.6;
          min-height: 120px;
          max-height: 200px;
          overflow-y: auto;
          border: 1px solid #e6effd;
          transition: all 0.3s;

          &:hover {
            background: #f0f7ff;
            border-color: #409eff;
          }

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background: #409eff;
            border-radius: 2px;
          }
        }

        .desc-label {
          position: absolute;
          top: -8px;
          left: 12px;
          padding: 2px 8px;
          background: #409eff;
          color: #fff;
          font-size: 12px;
          border-radius: 4px;
          z-index: 1;
        }
      }
    }
  }
}
.empty-list{
  text-align: center;
    height: 100px;
    line-height: 100px;
}

// 添加一些动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-content {
  animation: fadeInUp 0.6s ease-out;
}

.info-row {
  .info-item {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;

    @for $i from 1 through 4 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }
}

.sentence-expand {
  padding: 24px;
  background: #f8fafd;
  margin: 0 24px;
  border-radius: 8px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);

  .sentence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e6effd;

    h4 {
      margin: 0;
      font-size: 16px;
      color: #1a1f36;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: linear-gradient(180deg, #409eff, #79bbff);
        border-radius: 2px;
      }
    }

    .el-button {
      padding: 8px 16px;
      border-radius: 6px;

      i {
        margin-right: 4px;
      }
    }
  }

  .sentence-list {
    .el-table {
      border-radius: 8px;
      overflow: hidden;

      // 自定义表格样式
      ::v-deep {
        .el-table__header-wrapper {
          th {
            background: #f0f7ff;
            color: #1a1f36;
            font-weight: 600;
          }
        }

        .el-table__row {
          td {
            padding: 12px 0;
          }

          &:hover {
            background-color: #f0f7ff !important;
          }
        }

        .el-button--text {
          padding: 4px 8px;

          &.delete-btn {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .empty-sentence {
    text-align: center;
    padding: 32px;
    background: #fff;
    border-radius: 8px;
    color: #909399;
    font-size: 14px;
    border: 1px dashed #e6effd;
  }
}

// 优化主表格样式
::v-deep .el-table {
  .el-table__expand-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: #f0f7ff;
    margin-right: 8px;
    transition: all 0.3s;

    &--expanded {
      transform: rotate(90deg);
      background: #409eff;

      .el-icon-arrow-right {
        color: #fff;
      }
    }
  }

  // 图片列样式优化
  .el-table__row {
    img {
      border-radius: 6px;
      border: 2px solid #e6effd;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

.audio-preview {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafd;
  border-radius: 8px;
  border: 1px solid #e6effd;

  audio {
    width: 100%;
    margin-bottom: 8px;
  }

  .file-name {
    font-size: 12px;
    color: #606266;
    margin-bottom: 8px;
    word-break: break-all;
  }

  .delete-btn {
    color: #f56c6c;

    &:hover {
      color: #f78989;
    }
  }
}

.content-layout {
  display: flex;
  gap: 32px;
  min-height: 600px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.content-sidebar {
  width: 280px;
  flex-shrink: 0;
  background: #f8fafd;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: inset 0 0 0 1px #e6effd;
}

.content-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #e6effd;
  position: relative;
  transition: background-color 0.2s ease;

  &:hover {
    background: #fff;
  }

  &.active {
    background: #fff;
    border-left: 3px solid #409eff;
    padding-left: 9px;

    .preview-image {
      border-color: #409eff;
    }

    .preview-info .info-row .value {
      color: #409eff;
    }
  }

  .content-preview {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;

    .preview-image {
      width: 70px;
      height: 70px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;
      background: #fff;
      border: 1px solid #e6effd;
      transition: border-color 0.2s ease;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #409eff;
        font-size: 24px;
      }
    }

    .preview-info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .label {
          color: #909399;
          width: 60px;
          font-size: 12px;
        }

        .value {
          color: #1a1f36;
          font-weight: 500;
          font-size: 12px;
          transition: color 0.2s ease;
        }
      }
    }
  }

  .item-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;

    .el-button {
      padding: 4px;
      border-radius: 4px;
      background: #f0f7ff;

      i {
        font-size: 14px;
      }

      &:hover {
        background: #e6effd;
      }

      &.delete-btn {
        color: #f56c6c;
        background: #fff0f0;

        &:hover {
          background: #fde6e6;
        }
      }
    }
  }

  &:hover .item-actions {
    opacity: 1;
  }
}

.sentence-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: inset 0 0 0 1px #e6effd;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 24px;
    border-bottom: 1px solid #e6effd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #f8fafd, #fff);

    .header-info {
      display: flex;
      align-items: center;
      gap: 24px;

      .preview-image {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid #e6effd;
        background: #fff;
        transition: border-color 0.2s ease;

        &:hover {
          border-color: #409eff;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .no-image {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #409eff;
          font-size: 28px;
        }
      }

      .content-meta {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        .meta-item {
          padding: 6px 12px;
          background: #f0f7ff;
          color: #409eff;
          border-radius: 8px;
          font-size: 13px;
          font-weight: 500;
          border: 1px solid #e6effd;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .el-select {
        width: 120px;
      }
    }
  }

  .sentence-list {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: #f6f8fb;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e6effd;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .sentence-cards {
      display: grid;
      gap: 24px;
      padding: 4px;
      max-width: 100%;

      &.default-layout {
        grid-template-columns: repeat(2, 1fr);
      }

      &.grid-layout {
        grid-template-columns: repeat(3, 1fr);
      }

      &.list-layout {
        grid-template-columns: 1fr;
      }
    }

    .sentence-card {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .sentence-content {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        background: #f8fafd;
        border-radius: 8px;
        border: 1px solid #e6effd;

        .sentence-text {
          flex: 0.8;
          min-width: 200px;
          line-height: 1.5;

          .en-text {
            color: #1a1f36;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            padding: 8px 12px;
          }

          .zh-text {
            color: #606266;
            font-size: 14px;
            padding: 8px 12px;

          }
        }

        .sentence-meta {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16px;
          white-space: nowrap;
          margin: 0 16px;
          flex: 0.3;

          .speaker-tag {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            width: 80px;
            height: 80px;
            padding: 12px 8px;
            background: #ecf5ff;
            color: #409eff;
            border-radius: 12px;
            font-weight: 500;
            border: 1px solid #d9ecff;

            i {
              font-size: 28px;
              margin-top: 3px;
            }

            span {
              font-size: 13px;
              width: 100%;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 3px;
            }
          }
        }

        .sentence-actions {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          padding-left: 16px;
          border-left: 1px dashed #e6effd;
          flex: 0.5;
          min-width: 240px;

          .audio-wrapper {
            width: 100%;

            audio {
              width: 100%;
              height: 32px;
              border-radius: 4px;
            }
          }

          .button-group {
            display: flex;
            justify-content: center;
            gap: 16px;
            padding-top: 8px;
            border-top: 1px dashed #e6effd;
            width: 100%;

            .el-button {
              padding: 6px 12px;
              border-radius: 4px;
              font-size: 12px;

              &.delete-btn {
                color: #f56c6c;
                &:hover {
                  background: #fef0f0;
                }
              }

              &:hover {
                background: #ecf5ff;
              }
            }
          }
        }
      }
    }
  }
}

.empty-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: inset 0 0 0 1px #e6effd;
  color: #909399;
  font-size: 15px;
}

.empty-sentence {
  text-align: center;
  padding: 32px;
  color: #909399;
  background: #f8fafd;
  border-radius: 12px;
  border: 1px dashed #e6effd;
  font-size: 15px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;

  .el-select {
    width: 120px;
  }
}

.content-table-wrapper {
  .el-table {
    background: transparent;

    ::v-deep {
      .sentence-list {
        padding: 8px 0;
      }

      .sentence-item {
        &:not(:last-child) {
          margin-bottom: 12px;
          padding-bottom: 12px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .sentence-content {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        background: #f8fafd;
        border-radius: 8px;
        border: 1px solid #e6effd;

        .sentence-text {
          flex: 0.8;
          min-width: 200px;
          line-height: 1.5;

          .en-text {
            color: #1a1f36;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            padding: 8px 12px;
          }

          .zh-text {
            color: #606266;
            font-size: 14px;
            padding: 8px 12px;

          }
        }

        .sentence-meta {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16px;
          white-space: nowrap;
          margin: 0 16px;
          flex: 0.3;

          .speaker-tag {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            width: 80px;
            height: 80px;
            padding: 12px 8px;
            background: #ecf5ff;
            color: #409eff;
            border-radius: 12px;
            font-weight: 500;
            border: 1px solid #d9ecff;

            i {
              font-size: 28px;
              margin-top: 3px;
            }

            span {
              font-size: 13px;
              width: 100%;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 3px;
            }
          }
        }

        .sentence-actions {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          padding-left: 16px;
          border-left: 1px dashed #e6effd;
          flex: 0.5;
          min-width: 240px;

          .audio-wrapper {
            width: 100%;

            audio {
              width: 100%;
              height: 32px;
              border-radius: 4px;
            }
          }

          .button-group {
            display: flex;
            justify-content: center;
            gap: 16px;
            padding-top: 8px;
            border-top: 1px dashed #e6effd;
            width: 100%;

            .el-button {
              padding: 6px 12px;
              border-radius: 4px;
              font-size: 12px;

              &.delete-btn {
                color: #f56c6c;
                &:hover {
                  background: #fef0f0;
                }
              }

              &:hover {
                background: #ecf5ff;
              }
            }
          }
        }
      }

      .empty-sentence {
        padding: 16px 0;
        text-align: center;
      }

      .table-image {
        img {
          border-radius: 4px;
          border: 1px solid #ebeef5;
        }
      }
    }
  }
}

.empty-list {
  text-align: center;
  padding: 32px;
  color: #909399;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-right: 16px;

  .el-button {
    margin: 0;
    padding: 8px 15px;
  }
}
</style>
