<template>
  <div class="picture_container">
    <div class="operation-bar">
      <!-- <el-button type="primary" @click="handleAddUnit">添加单元</el-button> -->
      <el-button @click="$router.back()">返回</el-button>
      <el-button style="margin-left: auto;" type="primary" @click="handleAdd">添加单词</el-button>
      <!-- <el-button style="margin-left: auto;" type="primary" @click="fileDialogVisible = true">上传文件</el-button> -->
     
    </div>
    <el-table border v-loading="loading" :data="datalist" style="width: 100%" class="unit-table">
        <el-table-column prop="id" align="center" label="ID"></el-table-column>
        <el-table-column prop="displayOrder"  align="center" label="单元内学习顺序"></el-table-column>
        <el-table-column prop="spelling"  align="center" label="拼写"></el-table-column>
        <el-table-column prop="syllable"  align="center" label="音标"></el-table-column>
        <el-table-column prop="meaningEnUS" align="center" label="英文解释"></el-table-column>
        <el-table-column prop="meaningZhCN" align="center" label="中文词义"></el-table-column>
        <el-table-column align="center" label="缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.imgFile" alt="" style="width: 100px;height: 100px;">
          </template>
        </el-table-column>
        <el-table-column align="center" label="音频" width="400">
          <template slot-scope="scope">
            <!-- <el-button size="mini" type="success" icon="el-icon-video-play" @click="handlePreviewSong(row)"> 预览</el-button> -->
            <audio controls :src="scope.row.soundFile"></audio>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="200">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
    </el-table>

    <!-- 添加单词弹窗 -->
    <el-dialog
      :title="isEdit ? '编辑单词' : '添加单词'"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form
        :model="wordForm"
        :rules="rules"
        ref="wordFormRef"
        label-width="120px"
      >
        <el-form-item label="拼写" prop="spelling">
          <el-input v-model="wordForm.spelling" placeholder="请输入单词拼写"></el-input>
        </el-form-item>

        <el-form-item label="音标" prop="syllable">
          <el-input v-model="wordForm.syllable" placeholder="请输入音标"></el-input>
        </el-form-item>

        <el-form-item label="中文词义" prop="meaningZhCN">
          <el-input v-model="wordForm.meaningZhCN" placeholder="请输入中文词义"></el-input>
        </el-form-item>

        <el-form-item label="英文解释" prop="meaningEnUS">
          <el-input v-model="wordForm.meaningEnUS" placeholder="请输入英文解释"></el-input>
        </el-form-item>

        <el-form-item label="图片" prop="imgFile">
          <div class="image-upload-preview">
            <FileUpload
              accept=".jpg,.png,.gif,.jpeg"
              uploadType="1"
              :formData="{ unitId }"
              @onSuccess="handleImageSuccess"
            />
            <!-- <el-input v-model="wordForm.imgFile" placeholder="请输入图片名称"></el-input> -->
            <!-- 图片预览 -->
            <!-- <el-image
                v-if="wordForm.imgFile"
                :src="'https://kids-cdn.xiaohongben888.com/resource/picture/'+wordForm.imgFile"
                class="preview-image"
                :preview-src-list="['https://kids-cdn.xiaohongben888.com/resource/picture/'+wordForm.imgFile]"
                lazy
            > -->
            <el-image
                v-if="wordForm.imgFile"
                :src="wordForm.imgFile"
                class="preview-image"
                :preview-src-list="[wordForm.imgFile]"
                lazy
            >
            </el-image>
          </div>
        </el-form-item>

        <el-form-item label="音频" prop="soundFile">
          <div class="audio-upload-preview">
            <FileUpload
              accept=".mp3,.wav,.ogg"
              uploadType="2"
              :formData="{ unitId }"
              @onSuccess="handleSoundSuccess"
            />
            <!-- <el-input v-model="wordForm.soundFile" placeholder="请输入音频名称"></el-input> -->
            <!-- 添加音频预览 -->
            <audio v-if="wordForm.soundFile" controls style="margin-top: 10px;">
              <!-- <source :src="'https://kids-cdn.xiaohongben888.com/resource/sound/'+wordForm.soundFile" type="audio/mpeg"> -->
              <source :src="wordForm.soundFile" type="audio/mpeg">
              您的浏览器不支持音频播放
            </audio>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="文件上传"
      :visible.sync="fileDialogVisible"
      width="50%"
    >
    <FileUploadMany
        v-if="fileDialogVisible"
        :accept="'.jpg, .png, .jpeg'"
        :maxSize="5"
        :limit="50"
        tipText="支持jpg、png格式，单个文件不超过5M"
       
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {getUnitWordList,addWord,deleteWord,updateWord} from '@/api/xhb/lollipop'
import FileUpload from '@/views/lollipop/components/FileUpload'
import FileUploadMany from '../components/FileUploadMany.vue'
export default {
  name: 'picture_container',
  components: {
    FileUpload,
    FileUploadMany
  },
  data() {
    return {
      fileDialogVisible:false,
      loading: false,
      datalist:[],
      unitId:"",
      dialogVisible: false,
      wordForm: {
        spelling: '',
        syllable: '',
        meaningZhCN: '',
        meaningEnUS: '',
        soundFile: '',
        imgFile: '',
      },
      rules: {
        spelling: [{ required: true, message: '请输入单词拼写', trigger: 'blur' }],
        syllable: [{ required: false, message: '请输入音标', trigger: 'blur' }],
        meaningZhCN: [{ required: true, message: '请输入中文词义', trigger: 'blur' }],
        meaningEnUS: [{ required: false, message: '请输入英文解释', trigger: 'blur' }],
        // soundFile: [{ required: true, message: '请上传音频文件', trigger: 'change' }],
        // imgFile: [{ required: true, message: '请上传图片', trigger: 'change' }]
      },
      isEdit: false,
    }
  },
  created() {
    this.fetchWordList()
  },
  watch: {
    '$route': {
      handler(route) {
        this.unitId = route.query.unitID
      },
      immediate: true
    }
  },

  methods: {
    async fetchWordList(){
      this.loading = true
      const resp = await getUnitWordList(this.unitId)
      this.datalist=resp.data
      this.loading = false
    },
    handleUploadSuccess(url){
      console.log('%c [ url ]-61', 'font-size:13px; background:pink; color:#bf2c9f;', url)

    },
    handleSoundSuccess(url) {
      this.wordForm.soundFile = url
    },
    handleImageSuccess(url) {
      this.wordForm.imgFile = url
    },
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },
    handleEdit(row) {
      this.isEdit = true
      this.dialogVisible = true
      this.wordForm = {
        spelling: row.spelling,
        syllable: row.syllable,
        meaningZhCN: row.meaningZhCN,
        meaningEnUS: row.meaningEnUS,
        soundFile: row.soundFile,
        imgFile: row.imgFile,
        id: row.id
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该单词吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deleteWord({id:row.id})
        this.$message.success('删除成功')
        this.fetchWordList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },
    resetForm() {
      this.wordForm = {
        spelling: '',
        syllable: '',
        meaningZhCN: '',
        meaningEnUS: '',
        soundFile: '',
        imgFile: ''
      }
      if (this.$refs.wordFormRef) {
        this.$refs.wordFormRef.resetFields()
      }
    },
    async submitForm() {
      this.$refs.wordFormRef.validate(async (valid) => {
        if (valid) {
          try {
            const params = {
              ...this.wordForm,
              unitId: this.unitId,
              // utime: new Date()
            }
            // if(params.soundFile.trim()){
            //   params.soundFile = 'https://kids-cdn.xiaohongben888.com/resource/sound/'+params.soundFile.trim()
            // }
            // if(params.imgFile.trim()){
            //   params.imgFile = 'https://kids-cdn.xiaohongben888.com/resource/picture/'+params.imgFile.trim()
            // }
            if (this.isEdit) {
              await updateWord(params)
              this.$message.success('编辑成功')
            } else {
              await addWord(params)
              this.$message.success('添加成功')
            }

            this.dialogVisible = false
            this.fetchWordList()
          } catch (error) {
            this.$message.error(`${this.isEdit ? '编辑' : '添加'}失败：${error.message}`)
          }
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.picture_container {
  padding: 20px;

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
  }

  .unit-table {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.audio-upload-preview {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.image-upload-preview {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .preview-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
  }
}
</style>
