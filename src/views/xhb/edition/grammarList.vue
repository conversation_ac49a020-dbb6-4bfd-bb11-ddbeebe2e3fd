<template>
    <div class="grammarList-page">
        <div class="table-content">
            <el-table :data="tableData" border style="width: 100%" :span-method="objectSpanMethod">
                <el-table-column prop="unitName" label="单元" width="180">
                </el-table-column>
                <el-table-column prop="knowledgeCount" label="知识点数量">
                    <template slot-scope="scope">
                        <template v-if="scope.row.grammarUnitId != -1">
                            <template v-if="!scope.row.editFlag">
                                <span v-if="scope.row.knowledgeCount">{{scope.row.knowledgeCount}}</span>
                                <span v-else>0</span>
                            </template>
                            <template v-else>
                                <el-input v-model="scope.row.editKnowledgeNum" type="number" placeholder="请输入内容" style="width: 80px;"></el-input>
                                <el-button type="text" @click="saveKnowledgeCount(scope.row)">保存</el-button>
                                <el-button type="text" @click="scope.row.editFlag = false">取消</el-button>
                            </template>
                            <el-button type="text" @click="scope.row.editFlag = true" v-if="!scope.row.editFlag" style="margin-left: 8px;">修改</el-button>
                        </template>
                        <div v-else class="btn-content">
                            <el-button type="text" @click="addgrammar(scope.row)">添加</el-button>
                        <el-button type="text" @click="paste(scope.row.unitId)">粘贴</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="date" label="卡片管理">
                    <template slot-scope="scope">
                        <el-button type="text" @click="jump(1,scope.row.unitId)">预览</el-button>
                        <el-button type="text" @click="jump(2,scope.row.unitId)">编辑</el-button>
                        <el-button type="text" @click="jump(3,scope.row.unitId)">教学</el-button>
                        <el-button type="text" @click="jump(4,scope.row.unitId)">学习</el-button>
                        <el-button type="text" @click="copy(scope.row.unitId)">复制</el-button>
                        <el-popover
                            placement="right-start"
                            title="详细"
                            width="460"
                            trigger="hover"
                          >
                            <ul style="height: 60vh;overflow-y: auto;">
                                <li v-for="title1 in scope.row.grammarNewUnitBean.titleList" :key="title1.id" :id="'t_' + title1.id" class="knowledge-item knowledge-not-learn">
                                    <h2 class="title-text knowledge-title">{{ title1.name }}</h2>
                                    <i v-if="title1.basic" :id="'kDrop_' + title1.id" class="water-drop water-drop-pass"></i>
                                    <ul class="knowledge-map-sm">
                                    <li v-for="title2 in title1.titleList" :key="title2.id" :id="'t_' + title2.id" class="knowledge-item-sm knowledge-not-learn">
                                        <h2 class="title-text knowledge-title-sm">{{ title2.name }}</h2>
                                        <i v-if="title2.basic" :id="'kDrop_' + title2.id" class="water-drop water-drop-pass"></i>
                                        <ul v-if="title2.titleList">
                                        <li v-for="title3 in title2.titleList" :key="title3.id" :id="'t_' + title3.id" class="knowledge-item-xs knowledge-not-learn clear">
                                            <strong class="title-text">{{ title3.name }}</strong>
                                            <i v-if="title3.basic" :id="'kDrop_' + title3.id" class="water-drop water-drop-pass"></i>
                                        </li>
                                        </ul>
                                    </li>
                                    </ul>
                                </li>
                            </ul>
                            <el-button type="text" slot="reference" style="margin-left: 8px;" v-if="scope.row.grammarNewUnitBean && scope.row.grammarNewUnitBean.titleList">知识点预览</el-button>
                        </el-popover>
                       
                    </template>
                </el-table-column>
                <el-table-column prop="date" label="本节检测题量（易/中/难）">
                    <template slot-scope="scope">
                        {{getNum(scope.row.questionTotalNum)}}题({{getNum(scope.row.questionLevel1Num)}}/{{getNum(scope.row.questionLevel2Num)}}/{{getNum(scope.row.questionLevel3Num)}})
                    </template>
                </el-table-column>
                <el-table-column prop="date" label="本节检测管理">
                    <template slot-scope="scope">
                        <el-button type="text" @click="jump(5,scope.row.unitId)">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <pagination :pager-count="21" v-show="total>0" :total="total" :page.sync="listQuery.pageNum" :key="2" :limit.sync="listQuery.pageSize" @pagination="getList" /> -->
        </div>
    </div>
</template>

<script>
import {
    newGrammarManagerGetUnitList,
    newGrammarManagerAddGrammarUnit,
    saveCopiedUnitIdArr,
    pasteUnit,
    updateUnitKnowledgeCount
} from "@/api/xhb/resourceCourse";
import Pagination from "@/components/Pagination";
export default {
    components: { Pagination },
    data() {
        return {
            //请求接口用到的参数
            listQuery: {
                // pageNum: 1,
                // pageSize: 10,
                courseId: "",
            },
            total: 0,
            tableData: [],
            contentType:null
        };
    },
    mounted() {
        if (this.$route.query.courseId) {
            this.listQuery.courseId = this.$route.query.courseId;
            this.getList();
        }
        if (this.$route.query.contentType) {
           this.contentType = this.$route.query.contentType;
        }
    },
    computed:{
        getNum(){
            return function(num){
                return num ? num : 0
            }
        }
    },
    methods: {
        saveKnowledgeCount(row){
            let query = {
                unitId: row.unitId,
                count:row.editKnowledgeNum
            }
            updateUnitKnowledgeCount(query).then(res=>{
                if(res){
                    this.$message({
                        type: "success",
                        message: "保存成功",
                    });
                    this.getList();
                }else{
                    this.$message({
                        type: "error",
                        message: "保存失败",
                    });

                }
            })
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (row.grammarUnitId === -1) {
                if (columnIndex === 1) {
                    return {
                        rowspan: 1,
                        colspan: 4,
                    };
                } else if (columnIndex != 0) {
                    return {
                        rowspan: 0,
                        colspan: 0,
                    };
                }
            }
        },
        addgrammar(row) {
            newGrammarManagerAddGrammarUnit({ unitId: row.unitId }).then(
                (res) => {
                    if (res.code == 200) {
                        this.getList();
                    }
                }
            );
        },
        getList() {
            newGrammarManagerGetUnitList(this.listQuery).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.data;
                    this.tableData.forEach(item=>{
                        this.$set(item,'editFlag',false)
                        this.$set(item,'editKnowledgeNum',0)
                    })
                    // this.total = res.count;
                    this.total = res.data.length;
                }
            });
        },
        jump(type, id) {
            let name = "";
            switch (type) {
                case 1:
                    name = "cardPreview";
                    break;
                case 2:
                    name = "cardEdit";
                    break;
                case 3:
                    name = "teach";
                    break;
                case 4:
                    name = "interactiveLearning";
                    break;
                case 5:
                    name = "seeGrammar";
                    break;
                default:
                    name = "";
                    break;
            }
            if(this.contentType == 21 && type == 5){
                name = 'jnjl'
            }
            const { href } = this.$router.resolve({
                name: name,
                query: {
                    unitId: id,
                    courseId: this.listQuery.courseId,
                    contentType:this.contentType
                },
            });
            window.open(href, "_blank");
        },
        copy(unitId){
            saveCopiedUnitIdArr({unitId:unitId}).then(res=>{
                if(res == true){
                    this.$message({
                    message: '复制成功',
                    type: 'success'
                    });
                }else {
                  this.$message({
                    message: '复制失败',
                    type: 'warning'
                    }); 
                }
            })
        },
        paste(unitId){
            pasteUnit({
                courseId:this.listQuery.courseId,
                unitId:unitId
            }).then(res=>{
                if(res == true){
                    this.$message({
                    message: '粘贴成功',
                    type: 'success'
                    });
                    this.getList();
                }else {
                  this.$message({
                    message: '粘贴失败',
                    type: 'warning'
                    }); 
                }
            })
        },
    },
};
</script>

<style lang='scss' scoped>
.grammarList-page {
    padding: 20px;
    .table-content {
        padding: 20px 0;
    }
    .btn-content {
        text-align: center;
    }
}
</style>
