<template>
  <div class="training-system">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="选择类型">
        <el-select v-model="searchForm.moduleType" placeholder="请选择">
          <el-option label="助记" value="resourceWord"></el-option>
          <el-option label="AI记法" value="word"></el-option>
          <el-option label="AI句子学法" value="sentence"></el-option>
          <el-option label="AI写作辅助" value="write"></el-option>
          <el-option label="AI课文背法" value="article"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联内容">
        <el-input v-model="searchForm.relationContent" placeholder="请输入关联内容"></el-input>
      </el-form-item>
      <el-form-item label="课程">
        <el-input v-model="searchForm.courseDetails" placeholder="请输入课程"></el-input>
      </el-form-item>
      <el-form-item label="处理状态">
        <el-select v-model="searchForm.status" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="未处理" value="0"></el-option>
          <el-option label="已处理" value="1"></el-option>
          <el-option label="忽略" value="2"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="排序">
        <el-select v-model="searchForm.order" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="点赞" value="likeNum"></el-option>
          <el-option label="点踩" value="dislikeNum"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="reloadHandle">加载助记反馈缓存</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" border style="width: 100%" @sort-change="handleSortChange">
      <el-table-column type="index" label="序号" width="70"></el-table-column>
      <el-table-column prop="moduleType" label="类型" width="120">
        <template slot-scope="scope">
          <p>{{ getModuleType(scope.row.moduleType) }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="feedContent" label="反馈内容" width="500">
        <template slot-scope="scope">
          <div v-if="scope.row.feedContent">
            <p class="feedContent-content " v-html="scope.row.feedContent"></p>
            <el-button v-if="scope.row.moduleType != 'resourceWord' && scope.row.status == 0" type="primary"
              @click="handleEdit(scope.row)">修改</el-button>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="relationContent" label="关联内容">
        <template slot-scope="scope">
          <p v-if="scope.row.relationContent">{{ scope.row.relationContent }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="courseDetails" label="课程信息">
        <template slot-scope="scope">
          <p v-if="scope.row.courseDetails">{{ scope.row.courseDetails }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单元" width="100">
        <template slot-scope="scope">
          <p v-if="scope.row.unitName">{{ scope.row.unitName }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="dislikeNum" label="点踩次数" width="110" sortable>
        <template slot-scope="scope">
          <p v-if="scope.row.dislikeNum !== null">{{ scope.row.dislikeNum }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="likeNum" label="点赞次数" width="110" sortable>
        <template slot-scope="scope">
          <p v-if="scope.row.likeNum !== null">{{ scope.row.likeNum }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="反馈时间" width="160">
        <template slot-scope="scope">
          <p v-if="scope.row.updateTime">{{ scope.row.updateTime }}</p>
          <p v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="处理状态" width="80">
        <template slot-scope="scope">
          <p>{{ getStatus(scope.row.status, scope.row.moduleType) }}</p>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchForm.pageNum" :page-sizes="[1, 5, 10, 20, 30, 50]" :page-size="searchForm.pageSize"
        :total="total" layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </div>
    <!-- 修改弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">

      <template v-if="editForm.moduleType == 'word'">
        <el-form :model="editForm" label-width="100px">
          <el-form-item label="内容">
            <el-input type="textarea" :rows="4" v-model="editForm.feedContent" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template v-else>
        <p>1. AI 生成思路</p>
        <el-input class="pre" type="textarea" :autosize="{ minRows: 2, maxRows: 8}" placeholder="请输入内容" v-model="currentHtml[0]"> </el-input>
        <!-- <p class="pre" v-html="currentHtml[0]"></p> -->
        <p>2. AI 生成结果</p>
         <el-input class="pre" type="textarea" :autosize="{ minRows: 2, maxRows: 8}" placeholder="请输入内容" v-model="currentHtml[1]"> </el-input>
        <!-- <div class="pre">{{ currentHtml[1] }} </div> -->
      </template>


      <span slot="footer" class="dialog-footer">
        <el-button @click="getHandleIgnore" :loading="isLoading">忽 略</el-button>
        <el-button @click="getExecuteHandle" :loading="isLoading">重新生成</el-button>
        <el-button @click="dialogVisible = false" :loading="isLoading">取 消</el-button>
        <el-button type="primary" :disabled="isFixContent" @click="submitEdit" :loading="isLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAiGenerateList,
  getAiGenerateExecute,
  getAiGenerateExecuteWord,
  getAiGenerateHandleIgnore,
  getAiGenerateReload,
  getAiGenerateUpdate
} from '@/api/xhb/AIGeneratedContent'
import Cookies from 'js-cookie'
// import { fetchEventSource } from '@microsoft/fetch-event-source';

export default {
  name: 'TrainingSystem',
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        courseDetails: '',
        moduleType: 'word',
        pageNum: 1,
        pageSize: 10,
        relationContent: '',
        status: '',
        order: '',
        sort: 'asc'
      },
      // 表格数据
      tableData: [],
      dialogVisible: false,
      editForm: '',
      total: 0,
      currentFeedContent: '',
      title: '加载中',
      currentHtml: [],
      isLoading: false,
      errorMessage: '',
      eventSource: null
    }
  },
  computed: {
    isFixContent() {
      return this.editForm.feedContent == this.currentFeedContent
    },
  },
  created() {
    // 初始化时加载数据
    this.getAiGenerateListAsync()
  },
  methods: {
    handleSortChange({ column, prop, order }) {
      this.searchForm.order = order === 'ascending' ? prop : order === 'descending' ? prop : '';;
      this.searchForm.sort = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
      this.getAiGenerateListAsync();
    },
    // 处理搜索
    async handleSearch() {
      // const query={
      //   courseDetails: this.searchForm.courseDetails,
      //   moduleType: this.searchForm.moduleType,
      //   pageNum: this.searchForm.pageNum,
      //   pageSize: this.searchForm.pageSize,
      //   relationContent: this.searchForm.relationContent,
      //   status: this.searchForm.status,
      //   order:this.searchForm.order,
      //   sort:this.searchForm.sort
      // }
      // // 这里添加搜索逻辑
      // const {data} =  await getAiGenerateList(query)
      // this.tableData=data.list || []
      // this.total=data.total
      this.searchForm.pageNum = 1
      await this.getAiGenerateListAsync()
    },

    async getAiGenerateListAsync(){
      const query={
        courseDetails: this.searchForm.courseDetails,
        moduleType: this.searchForm.moduleType,
        pageNum: this.searchForm.pageNum,
        pageSize: this.searchForm.pageSize,
        relationContent: this.searchForm.relationContent,
        status: this.searchForm.status,
        order: this.searchForm.order,
        sort: this.searchForm.sort
      }
      const {data} =  await getAiGenerateList(query)
      this.tableData=data.list || []
      this.total=data.total
    },

    getModuleType(value) {
      switch (value) {
        case 'resourceWord':
          return '助记'
          break;
        case 'word':
          return 'AI记法'
          break;
        case 'sentence':
          return 'AI句子学法'
          break;
        case 'write':
          return 'AI写作辅助'
          break;
        case 'article':
          return 'AI课文背法'
          break;
        default:
          return '-'
          break;
      }

    },
    async reloadHandle(){
        await getAiGenerateReload()
        this.$message.success('加载成功')
        this.getAiGenerateListAsync()
    },
    getStatus(value, moduleType) {
      if (moduleType == 'resourceWord') {
        return '-'
      }
      switch (Number(value)) {
        case 0:
          return '未处理'
          break;
        case 1:
          return '已处理'
          break;
        case 2:
          return '忽略'
        default:
          return '-'
          break;
      }
    },
    // 处理分页大小改变
    handleSizeChange(val) {
      this.searchForm.pageNum = 1
      this.searchForm.pageSize = val
      // 重新加载数据
      this.getAiGenerateListAsync()
    },
    // 处理页码改变
    handleCurrentChange(val) {
      this.searchForm.pageNum = val
      // 重新加载数据
      this.getAiGenerateListAsync()
    },
    // 打开修改弹窗
    handleEdit(row) {
      this.dialogVisible = true
      this.editForm = row
      this.title = this.getModuleType(row.moduleType)

      if (['sentence', 'write', 'article'].includes(this.editForm.moduleType)) {
        this.currentHtml = row.feedContent.replace('</think>', '</think>%$').split('%$')
      } else {
        this.currentFeedContent = row.feedContent
      }

    },

    // 关闭弹窗前的确认
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
          this.resetForm()
        })
        .catch(_ => { })
    },
    // 重置表单
    resetForm() {
      this.editForm = {}
    },
    // 忽略
    getHandleIgnore() {
      const query = {
        id: this.editForm.id,
        moduleType: this.editForm.moduleType
      }
      getAiGenerateHandleIgnore(query).then(res => {
        if (res.code === 200) {
          this.$message.success('忽略成功')
          this.dialogVisible = false
          this.resetForm()
          this.getAiGenerateListAsync() // 刷新列表
        }
      })
    },
    // 重新生成
    getExecuteHandle() {
      this.isLoading = true
      if (['sentence', 'write', 'article'].includes(this.editForm.moduleType)) {
            const query = {
              id: this.editForm.id,
              moduleType: this.editForm.moduleType
            }
            getAiGenerateExecute(query).then(res => {
              this.$message.success('重新生成成功')
              this.currentHtml = res.replace('</think>', '</think>%$').split('%$')
              this.getAiGenerateListAsync() // 刷新列表
              this.isLoading = false
            })
      }else{
            const query = {
              id: this.editForm.id,
            }
            getAiGenerateExecuteWord(query).then(res => {
              if (res.code === 200) {
                this.$message.success('重新生成成功')
                this.editForm.feedContent = res.data
                // this.dialogVisible = false
                // this.resetForm()
                this.getAiGenerateListAsync() // 刷新列表
                this.isLoading = false
              }
            })
      }

    },
    // 提交修改
    submitEdit() {
      if (!this.editForm.feedContent) {
        this.$message.error('请输入内容')
        return
      }

      let currentValue

      if (['sentence', 'write', 'article'].includes(this.editForm.moduleType)) {
        currentValue = this.currentHtml.join(',')
      } else {
        currentValue = this.editForm.feedContent
      }

      const query = {
        id: this.editForm.id,
        moduleType: this.editForm.moduleType,
        feedContent: currentValue
      }
      getAiGenerateUpdate(query).then(res => {
        if (res.code === 200) {
          this.$message.success('修改成功')
          this.dialogVisible = false
          this.resetForm()
          this.getAiGenerateListAsync() // 刷新列表
        } else {
          this.$message.error(res.msg || '修改失败')
        }
      }).catch(err => {
        this.$message.error('修改失败')
      })
    }
  }
}
</script>

<style scoped>
.pre {
  overflow-y: auto;
  white-space: pre-wrap
}

.training-system {
  padding: 20px;
}

.feedContent-content {
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap
}

.search-form {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-dialog__body {
  padding: 20px 30px;
}
</style>
