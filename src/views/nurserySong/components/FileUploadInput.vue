<template>
  <div class="file-upload-input">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      @input="handleInput"
    >
      <el-button slot="append" @click="handleUpload" :loading="uploading">
        <i class="el-icon-upload"></i> 上传
      </el-button>
    </el-input>
    
    <!-- 文件上传组件（隐藏） -->
    <el-upload
      ref="upload"
      class="hidden-upload"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :accept="accept"
      :before-upload="beforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :show-file-list="false"
      :auto-upload="false"
    >
    </el-upload>
    
    <!-- 文件选择器 -->
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      @change="handleFileSelect"
      style="display: none"
    />
    
    <!-- 上传进度 -->
    <el-progress
      v-if="uploading"
      :percentage="uploadProgress"
      :show-text="false"
      style="margin-top: 5px"
    />
    
    <!-- 文件信息显示 -->
    <div v-if="fileInfo" class="file-info">
      <i :class="getFileIcon(fileInfo.type)"></i>
      <span class="file-name">{{ fileInfo.name }}</span>
      <span class="file-size">({{ formatFileSize(fileInfo.size) }})</span>
      <el-button type="text" size="mini" @click="clearFile">
        <i class="el-icon-close"></i>
      </el-button>
    </div>
  </div>
</template>

<script>
import { uploadNurserySongFile } from '@/api/xhb/nurserySong'
import { getUserToken } from '@/utils/auth'

export default {
  name: 'FileUploadInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    fileType: {
      type: Number,
      required: true
    },
    accept: {
      type: String,
      default: '*'
    },
    placeholder: {
      type: String,
      default: '请输入文件URL或点击上传'
    },
    songId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      inputValue: '',
      uploading: false,
      uploadProgress: 0,
      fileInfo: null
    }
  },
  computed: {
    uploadAction() {
      return process.env.VUE_APP_AXIOS_BASE_URL + '/nursery-song/upload'
    },
    uploadHeaders() {
      return {
        'authorization': getUserToken()
      }
    },
    uploadData() {
      return {
        type: this.fileType,
        songId: this.songId
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.inputValue = newVal
      },
      immediate: true
    }
  },
  methods: {
    // 输入框变化
    handleInput(value) {
      this.$emit('input', value)
      this.fileInfo = null
    },
    
    // 点击上传按钮
    handleUpload() {
      this.$refs.fileInput.click()
    },
    
    // 文件选择
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return
      
      // 验证文件类型
      if (!this.validateFileType(file)) {
        return
      }
      
      // 验证文件大小
      if (!this.validateFileSize(file)) {
        return
      }
      
      // 设置文件信息
      this.fileInfo = {
        name: file.name,
        size: file.size,
        type: file.type
      }
      
      // 开始上传
      this.uploadFile(file)
    },
    
    // 上传文件
    async uploadFile(file) {
      try {
        this.uploading = true
        this.uploadProgress = 0
        
        const uploadData = {
          file: file,
          type: this.fileType
        }
        if (this.songId) {
          uploadData.songId = this.songId
        }
        
        const response = await uploadNurserySongFile(uploadData)
        
        if (response.code === 200) {
          this.inputValue = response.data
          this.$emit('input', response.data)
          this.$emit('file-uploaded', response.data, file)
          this.$message.success('文件上传成功')
        } else {
          this.$message.error(response.msg || '文件上传失败')
          this.fileInfo = null
        }
      } catch (error) {
        this.$message.error('文件上传失败')
        this.fileInfo = null
        console.error('Upload error:', error)
      } finally {
        this.uploading = false
        this.uploadProgress = 0
        // 清空文件选择器
        this.$refs.fileInput.value = ''
      }
    },
    
    // 验证文件类型
    validateFileType(file) {
      if (this.accept === '*') return true
      
      const acceptTypes = this.accept.split(',').map(type => type.trim())
      const fileName = file.name.toLowerCase()
      const fileExtension = '.' + fileName.split('.').pop()
      
      const isValid = acceptTypes.some(type => {
        if (type.startsWith('.')) {
          return fileName.endsWith(type)
        } else if (type.includes('/')) {
          return file.type.match(type.replace('*', '.*'))
        }
        return false
      })
      
      if (!isValid) {
        this.$message.error(`文件类型不支持，请选择 ${this.accept} 格式的文件`)
        return false
      }
      
      return true
    },
    
    // 验证文件大小
    validateFileSize(file) {
      const maxSize = this.getMaxFileSize()
      if (file.size > maxSize) {
        this.$message.error(`文件大小不能超过 ${this.formatFileSize(maxSize)}`)
        return false
      }
      return true
    },
    
    // 获取最大文件大小
    getMaxFileSize() {
      switch (this.fileType) {
        case 1: // 视频
          return 100 * 1024 * 1024 // 100MB
        case 2: // 原唱音频
        case 3: // 伴奏音频
          return 50 * 1024 * 1024 // 50MB
        case 4: // 字幕文件
          return 1 * 1024 * 1024 // 1MB
        default:
          return 10 * 1024 * 1024 // 10MB
      }
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 获取文件图标
    getFileIcon(fileType) {
      if (fileType.startsWith('video/')) {
        return 'el-icon-video-camera'
      } else if (fileType.startsWith('audio/')) {
        return 'el-icon-headset'
      } else if (fileType.startsWith('text/') || fileType.includes('subtitle')) {
        return 'el-icon-document'
      } else {
        return 'el-icon-document'
      }
    },
    
    // 清除文件
    clearFile() {
      this.fileInfo = null
      this.inputValue = ''
      this.$emit('input', '')
    },
    
    // 上传前检查
    beforeUpload(file) {
      return this.validateFileType(file) && this.validateFileSize(file)
    },
    
    // 上传成功
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.inputValue = response.data
        this.$emit('input', response.data)
        this.$emit('file-uploaded', response.data)
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
      this.uploading = false
    },
    
    // 上传失败
    handleUploadError() {
      this.$message.error('文件上传失败')
      this.uploading = false
      this.fileInfo = null
    }
  }
}
</script>

<style scoped>
.file-upload-input {
  width: 100%;
}

.hidden-upload {
  display: none;
}

.file-info {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.file-name {
  margin-left: 8px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.el-progress {
  margin-top: 5px;
}
</style>
