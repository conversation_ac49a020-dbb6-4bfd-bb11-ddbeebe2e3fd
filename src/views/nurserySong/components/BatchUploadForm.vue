<template>
  <div class="batch-upload-form">
    <el-form :model="form" label-width="100px">
      <el-form-item label="儿歌ID">
        <el-input-number
          v-model="form.songId"
          :min="1"
          placeholder="可选，指定儿歌ID"
          style="width: 100%"
        />
        <div class="form-tip">如果指定儿歌ID，文件将关联到该儿歌</div>
      </el-form-item>
    </el-form>

    <el-divider content-position="left">文件上传</el-divider>

    <!-- 视频文件上传 -->
    <div class="upload-section">
      <h4><i class="el-icon-video-camera"></i> 视频文件</h4>
      <el-upload
        ref="videoUpload"
        class="upload-demo"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="getUploadData(1)"
        :before-upload="file => beforeUpload(file, 1)"
        :on-success="response => handleUploadSuccess(response, 'video')"
        :on-error="error => handleUploadError(error, 'video')"
        :on-progress="progress => handleUploadProgress(progress, 'video')"
        accept=".mp4,.avi,.mov,.wmv,.flv,.mkv"
        :auto-upload="false"
        :limit="1"
      >
        <el-button size="small" type="primary">选择视频文件</el-button>
        <div slot="tip" class="el-upload__tip">支持 mp4, avi, mov, wmv, flv, mkv 格式，大小不超过100MB</div>
      </el-upload>
      <el-progress
        v-if="uploadProgress.video > 0"
        :percentage="uploadProgress.video"
        :status="uploadStatus.video"
      />
    </div>

    <!-- 原唱音频上传 -->
    <div class="upload-section">
      <h4><i class="el-icon-headset"></i> 原唱音频</h4>
      <el-upload
        ref="originalAudioUpload"
        class="upload-demo"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="getUploadData(2)"
        :before-upload="file => beforeUpload(file, 2)"
        :on-success="response => handleUploadSuccess(response, 'originalAudio')"
        :on-error="error => handleUploadError(error, 'originalAudio')"
        :on-progress="progress => handleUploadProgress(progress, 'originalAudio')"
        accept=".mp3,.wav,.aac,.flac,.ogg,.m4a"
        :auto-upload="false"
        :limit="1"
      >
        <el-button size="small" type="primary">选择原唱音频</el-button>
        <div slot="tip" class="el-upload__tip">支持 mp3, wav, aac, flac, ogg, m4a 格式，大小不超过50MB</div>
      </el-upload>
      <el-progress
        v-if="uploadProgress.originalAudio > 0"
        :percentage="uploadProgress.originalAudio"
        :status="uploadStatus.originalAudio"
      />
    </div>

    <!-- 伴奏音频上传 -->
    <div class="upload-section">
      <h4><i class="el-icon-headset"></i> 伴奏音频</h4>
      <el-upload
        ref="backgroundAudioUpload"
        class="upload-demo"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="getUploadData(3)"
        :before-upload="file => beforeUpload(file, 3)"
        :on-success="response => handleUploadSuccess(response, 'backgroundAudio')"
        :on-error="error => handleUploadError(error, 'backgroundAudio')"
        :on-progress="progress => handleUploadProgress(progress, 'backgroundAudio')"
        accept=".mp3,.wav,.aac,.flac,.ogg,.m4a"
        :auto-upload="false"
        :limit="1"
      >
        <el-button size="small" type="primary">选择伴奏音频</el-button>
        <div slot="tip" class="el-upload__tip">支持 mp3, wav, aac, flac, ogg, m4a 格式，大小不超过50MB</div>
      </el-upload>
      <el-progress
        v-if="uploadProgress.backgroundAudio > 0"
        :percentage="uploadProgress.backgroundAudio"
        :status="uploadStatus.backgroundAudio"
      />
    </div>

    <!-- 字幕文件上传 -->
    <div class="upload-section">
      <h4><i class="el-icon-document"></i> 字幕文件</h4>
      <el-upload
        ref="subtitleUpload"
        class="upload-demo"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="getUploadData(4)"
        :before-upload="file => beforeUpload(file, 4)"
        :on-success="response => handleUploadSuccess(response, 'subtitle')"
        :on-error="error => handleUploadError(error, 'subtitle')"
        :on-progress="progress => handleUploadProgress(progress, 'subtitle')"
        accept=".srt,.vtt,.json,.ass,.ssa"
        :auto-upload="false"
        :limit="1"
      >
        <el-button size="small" type="primary">选择字幕文件</el-button>
        <div slot="tip" class="el-upload__tip">支持 srt, vtt, json, ass, ssa 格式，大小不超过1MB</div>
      </el-upload>
      <el-progress
        v-if="uploadProgress.subtitle > 0"
        :percentage="uploadProgress.subtitle"
        :status="uploadStatus.subtitle"
      />
    </div>

    <!-- 上传结果 -->
    <el-divider content-position="left" v-if="hasUploadResults">上传结果</el-divider>
    
    <div v-if="hasUploadResults" class="upload-results">
      <el-table :data="uploadResultsList" border size="small">
        <el-table-column prop="type" label="文件类型" width="100" />
        <el-table-column prop="fileName" label="文件名" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="文件URL" show-overflow-tooltip />
      </el-table>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleStartUpload" :loading="uploading">
        开始上传
      </el-button>
      <el-button 
        type="success" 
        @click="handleBatchUpload" 
        :disabled="!canBatchUpload"
        :loading="batchUploading"
      >
        批量上传
      </el-button>
    </div>
  </div>
</template>

<script>
import { batchUploadNurserySongFiles } from '@/api/xhb/nurserySong'
import { getUserToken } from '@/utils/auth'

export default {
  name: 'BatchUploadForm',
  data() {
    return {
      form: {
        songId: null
      },
      uploading: false,
      batchUploading: false,
      uploadProgress: {
        video: 0,
        originalAudio: 0,
        backgroundAudio: 0,
        subtitle: 0
      },
      uploadStatus: {
        video: '',
        originalAudio: '',
        backgroundAudio: '',
        subtitle: ''
      },
      uploadResults: {
        video: null,
        originalAudio: null,
        backgroundAudio: null,
        subtitle: null
      },
      fileTypeMap: {
        1: 'video',
        2: 'originalAudio',
        3: 'backgroundAudio',
        4: 'subtitle'
      },
      fileTypeNames: {
        video: '视频文件',
        originalAudio: '原唱音频',
        backgroundAudio: '伴奏音频',
        subtitle: '字幕文件'
      }
    }
  },
  computed: {
    uploadAction() {
      return process.env.VUE_APP_AXIOS_BASE_URL + '/nursery-song/upload'
    },
    uploadHeaders() {
      return {
        'authorization': getUserToken()
      }
    },
    hasUploadResults() {
      return Object.values(this.uploadResults).some(result => result !== null)
    },
    uploadResultsList() {
      const results = []
      Object.keys(this.uploadResults).forEach(key => {
        const result = this.uploadResults[key]
        if (result) {
          results.push({
            type: this.fileTypeNames[key],
            fileName: result.fileName,
            status: result.status,
            url: result.url || result.error
          })
        }
      })
      return results
    },
    canBatchUpload() {
      return Object.values(this.uploadResults).some(result => 
        result && result.status === 'success'
      )
    }
  },
  methods: {
    // 获取上传数据
    getUploadData(type) {
      return {
        type,
        songId: this.form.songId
      }
    },
    
    // 上传前检查
    beforeUpload(file, type) {
      const maxSizes = {
        1: 100 * 1024 * 1024, // 100MB for video
        2: 50 * 1024 * 1024,  // 50MB for audio
        3: 50 * 1024 * 1024,  // 50MB for audio
        4: 1 * 1024 * 1024    // 1MB for subtitle
      }
      
      if (file.size > maxSizes[type]) {
        this.$message.error(`文件大小超过限制`)
        return false
      }
      
      return true
    },
    
    // 上传成功
    handleUploadSuccess(response, fileType) {
      const fileName = response.fileName || '未知文件'
      
      if (response.code === 200) {
        this.uploadResults[fileType] = {
          fileName,
          status: 'success',
          url: response.data
        }
        this.uploadStatus[fileType] = 'success'
        this.$message.success(`${this.fileTypeNames[fileType]}上传成功`)
      } else {
        this.uploadResults[fileType] = {
          fileName,
          status: 'error',
          error: response.msg || '上传失败'
        }
        this.uploadStatus[fileType] = 'exception'
        this.$message.error(`${this.fileTypeNames[fileType]}上传失败: ${response.msg}`)
      }
    },
    
    // 上传失败
    handleUploadError(error, fileType) {
      this.uploadResults[fileType] = {
        fileName: '未知文件',
        status: 'error',
        error: '上传失败'
      }
      this.uploadStatus[fileType] = 'exception'
      this.$message.error(`${this.fileTypeNames[fileType]}上传失败`)
    },
    
    // 上传进度
    handleUploadProgress(event, fileType) {
      this.uploadProgress[fileType] = Math.round(event.percent)
    },
    
    // 开始上传
    async handleStartUpload() {
      const uploads = [
        this.$refs.videoUpload,
        this.$refs.originalAudioUpload,
        this.$refs.backgroundAudioUpload,
        this.$refs.subtitleUpload
      ]
      
      let hasFiles = false
      uploads.forEach(upload => {
        if (upload && upload.uploadFiles.length > 0) {
          hasFiles = true
          upload.submit()
        }
      })
      
      if (!hasFiles) {
        this.$message.warning('请先选择要上传的文件')
        return
      }
      
      this.uploading = true
      this.$message.info('开始上传文件...')
    },
    
    // 批量上传（使用批量接口）
    async handleBatchUpload() {
      try {
        this.batchUploading = true
        
        // 收集所有文件
        const formData = new FormData()
        const uploads = [
          { ref: this.$refs.videoUpload, param: 'videoFile' },
          { ref: this.$refs.originalAudioUpload, param: 'originalAudioFile' },
          { ref: this.$refs.backgroundAudioUpload, param: 'backgroundAudioFile' },
          { ref: this.$refs.subtitleUpload, param: 'subtitleFile' }
        ]
        
        let hasFiles = false
        uploads.forEach(({ ref, param }) => {
          if (ref && ref.uploadFiles.length > 0) {
            const file = ref.uploadFiles[0].raw
            formData.append(param, file)
            hasFiles = true
          }
        })
        
        if (!hasFiles) {
          this.$message.warning('请先选择要上传的文件')
          return
        }
        
        if (this.form.songId) {
          formData.append('songId', this.form.songId)
        }
        
        const response = await batchUploadNurserySongFiles(formData)
        
        if (response.code === 200) {
          this.$message.success('批量上传成功')
          this.$emit('success', response.data)
        } else {
          this.$message.error(response.msg || '批量上传失败')
        }
      } catch (error) {
        this.$message.error('批量上传失败')
        console.error('Batch upload error:', error)
      } finally {
        this.batchUploading = false
      }
    },
    
    // 取消
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 重置表单
    resetForm() {
      this.form.songId = null
      this.uploadProgress = {
        video: 0,
        originalAudio: 0,
        backgroundAudio: 0,
        subtitle: 0
      }
      this.uploadStatus = {
        video: '',
        originalAudio: '',
        backgroundAudio: '',
        subtitle: ''
      }
      this.uploadResults = {
        video: null,
        originalAudio: null,
        backgroundAudio: null,
        subtitle: null
      }
      
      // 清空文件列表
      const uploads = [
        this.$refs.videoUpload,
        this.$refs.originalAudioUpload,
        this.$refs.backgroundAudioUpload,
        this.$refs.subtitleUpload
      ]
      
      uploads.forEach(upload => {
        if (upload) {
          upload.clearFiles()
        }
      })
    }
  }
}
</script>

<style scoped>
.batch-upload-form {
  /* padding: 20px; */
  max-height: 660px;
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.upload-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
}

.upload-demo {
  margin-bottom: 10px;
}

.upload-results {
  margin-bottom: 20px;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-progress {
  margin-top: 10px;
}
</style>
