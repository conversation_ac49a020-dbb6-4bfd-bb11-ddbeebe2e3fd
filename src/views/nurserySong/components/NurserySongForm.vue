<template>
  <div class="nursery-song-form">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.native.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="儿歌标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入儿歌标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="难度等级" prop="difficultyLevel">
            <el-select v-model="form.difficultyLevel" placeholder="请选择难度等级">
              <el-option
                v-for="item in difficultyLevels"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="总时长(毫秒)" prop="duration">
            <el-input-number
              v-model="form.duration"
              :min="0"
              :max="600000"
              placeholder="请输入总时长"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序值" prop="sortOrder">
            <el-input-number
              v-model="form.sortOrder"
              :min="0"
              placeholder="排序值，数字越小越靠前"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="缩略图地址" prop="thumbnailUrl">
        <el-input v-model="form.thumbnailUrl" placeholder="请输入缩略图URL">
          <el-button slot="append" @click="handleThumbnailUpload">上传</el-button>
        </el-input>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入儿歌描述"
        />
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-input
          v-model="form.tags"
          placeholder="请输入标签，多个标签用逗号分隔"
        />
      </el-form-item>

      <!-- 文件上传区域 -->
      <el-divider content-position="left">文件资源</el-divider>

      <el-form-item label="无声视频" prop="videoUrl">
        <file-upload-input
          v-model="form.videoUrl"
          :file-type="1"
          accept=".mp4,.avi,.mov,.wmv,.flv,.mkv"
          placeholder="请上传或输入无声视频文件URL"
        />
      </el-form-item>

      <el-form-item label="原唱音频" prop="originalAudioUrl">
        <file-upload-input
          v-model="form.originalAudioUrl"
          :file-type="2"
          accept=".mp3,.wav,.aac,.flac,.ogg,.m4a"
          placeholder="请上传或输入原唱音频文件URL"
        />
      </el-form-item>

      <el-form-item label="伴奏音频" prop="backgroundAudioUrl">
        <file-upload-input
          v-model="form.backgroundAudioUrl"
          :file-type="3"
          accept=".mp3,.wav,.aac,.flac,.ogg,.m4a"
          placeholder="请上传或输入伴奏音频文件URL"
        />
      </el-form-item>

      <el-form-item label="字幕文件" prop="subtitleFileUrl">
        <file-upload-input
          v-model="form.subtitleFileUrl"
          :file-type="4"
          accept=".srt,.vtt,.json,.ass,.ssa"
          placeholder="请上传或输入字幕文件URL"
          @file-uploaded="handleSubtitleUploaded"
        />
      </el-form-item>

      <!-- 预览区域 -->
      <el-divider content-position="left">预览</el-divider>
      
      <el-row :gutter="20" v-if="hasPreviewContent">
        <el-col :span="12" v-if="form.thumbnailUrl">
          <div class="preview-item">
            <label>缩略图预览：</label>
            <img :src="form.thumbnailUrl" alt="缩略图" class="thumbnail-preview" />
          </div>
        </el-col>
        <el-col :span="12" v-if="form.originalAudioUrl">
          <div class="preview-item">
            <label>原唱音频预览：</label>
            <audio :src="form.originalAudioUrl" controls class="audio-preview"></audio>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.backgroundAudioUrl">
        <el-col :span="12">
          <div class="preview-item">
            <label>伴奏音频预览：</label>
            <audio :src="form.backgroundAudioUrl" controls class="audio-preview"></audio>
          </div>
        </el-col>
        <el-col :span="12" v-if="form.videoUrl">
          <div class="preview-item">
            <label>视频预览：</label>
            <video :src="form.videoUrl" controls class="video-preview"></video>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '保存' }}
      </el-button>
      <el-button 
        v-if="isEdit && form.subtitleFileUrl" 
        type="warning" 
        @click="handleProcessSubtitle"
        :loading="processing"
      >
        处理字幕
      </el-button>
    </div>

    <!-- 文件上传对话框 -->
    <el-dialog
      title="上传缩略图"
      :visible.sync="thumbnailUploadVisible"
      width="30%"
      :modal-append-to-body="false"
      :append-to-body="true"
      class="thumbnail-upload-dialog"
    >
      <el-upload
        class="upload-demo"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="{type: 5}"
        :on-success="handleThumbnailUploadSuccess"
        :on-error="handleThumbnailUploadError"
        :before-upload="beforeThumbnailUpload"
        accept="image/*"
        :limit="1"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import {
  addNurserySong,
  updateNurserySong,
  processNurserySongResources,
  getDifficultyLevels
} from '@/api/xhb/nurserySong'

import FileUploadInput from './FileUploadInput.vue'
import { getUserToken } from '@/utils/auth'

export default {
  name: 'NurserySongForm',
  components: {
    FileUploadInput
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        id: null,
        title: '',
        videoUrl: '',
        originalAudioUrl: '',
        backgroundAudioUrl: '',
        subtitleFileUrl: '',
        duration: null,
        difficultyLevel: null,
        thumbnailUrl: '',
        description: '',
        tags: '',
        sortOrder: 0
      },
      rules: {
        title: [
          { required: true, message: '请输入儿歌标题', trigger: 'blur' },
          { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
        ],
        videoUrl: [
          { required: true, message: '请上传无声视频文件', trigger: 'blur' }
        ],
        originalAudioUrl: [
          { required: true, message: '请上传原唱音频文件', trigger: 'blur' }
        ],
        backgroundAudioUrl: [
          { required: true, message: '请上传伴奏音频文件', trigger: 'blur' }
        ],
        subtitleFileUrl: [
          { required: true, message: '请上传字幕文件', trigger: 'blur' }
        ],
        duration: [
          { required: true, message: '请输入总时长', trigger: 'blur' },
          { type: 'number', min: 1, message: '时长必须大于0', trigger: 'blur' }
        ],
        difficultyLevel: [
          { required: true, message: '请选择难度等级', trigger: 'change' }
        ]
      },
      submitting: false,
      processing: false,
      thumbnailUploadVisible: false,
      difficultyLevels: getDifficultyLevels()
    }
  },
  computed: {
    hasPreviewContent() {
      return this.form.thumbnailUrl || this.form.originalAudioUrl || 
             this.form.backgroundAudioUrl || this.form.videoUrl
    },
    uploadAction() {
      return process.env.VUE_APP_AXIOS_BASE_URL + '/nursery-song/upload'
    },
    uploadHeaders() {
      return {
        'authorization': getUserToken()
      }
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        this.form = { ...this.form, ...newVal }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    resetForm() {
      this.$refs.form.resetFields()
      console.log('%c [ this.$refs.form ]-292', 'font-size:13px; background:pink; color:#bf2c9f;', this.$refs.form)
      this.form = {
        id: null,
        title: '',
        videoUrl: '',
        originalAudioUrl: '',
        backgroundAudioUrl: '',
        subtitleFileUrl: '',
        duration: null,
        difficultyLevel: null,
        thumbnailUrl: '',
        description: '',
        tags: '',
        sortOrder: 0
      }
    },
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        
        this.submitting = true
        const apiMethod = this.isEdit ? updateNurserySong : addNurserySong
        const response = await apiMethod(this.form)
        
        if (response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '添加成功')
          this.$emit('submit', response.data)
        } else {
          this.$message.error(response.msg || '操作失败')
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error('操作失败')
          console.error('Submit error:', error)
        }
      } finally {
        this.submitting = false
      }
    },
    
    // 取消
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 处理字幕
    async handleProcessSubtitle() {
      if (!this.form.id) {
        this.$message.warning('请先保存儿歌信息')
        return
      }
      
      try {
        await this.$confirm('确定要处理字幕文件吗？这将解析字幕并生成句子数据。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        
        this.processing = true
        const response = await processNurserySongResources(this.form.id)
        
        if (response.code === 200) {
          this.$message.success('字幕处理成功')
        } else {
          this.$message.error(response.msg || '字幕处理失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('字幕处理失败')
          console.error('Process subtitle error:', error)
        }
      } finally {
        this.processing = false
      }
    },
    
    // 字幕文件上传成功回调
    handleSubtitleUploaded(url) {
      this.$message.success('字幕文件上传成功')
      // 如果是编辑模式且已保存，可以自动处理字幕
      if (this.isEdit && this.form.id) {
        this.$confirm('是否立即处理字幕文件？', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'info'
        }).then(() => {
          this.handleProcessSubtitle()
        }).catch(() => {})
      }
    },
    
    // 缩略图上传
    handleThumbnailUpload() {
      this.thumbnailUploadVisible = true
    },
    
    // 缩略图上传成功
    handleThumbnailUploadSuccess(response) {
      if (response.code === 200) {
        this.form.thumbnailUrl = response.data
        this.$message.success('缩略图上传成功')
        this.thumbnailUploadVisible = false
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },
    
    // 缩略图上传失败
    handleThumbnailUploadError() {
      this.$message.error('缩略图上传失败')
    },
    
    // 缩略图上传前检查
    beforeThumbnailUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    }
  }
}
</script>

<style scoped>
.nursery-song-form {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.form-actions {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.preview-item {
  margin-bottom: 15px;
}

.preview-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #606266;
}

.thumbnail-preview {
  max-width: 200px;
  max-height: 150px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.audio-preview,
.video-preview {
  width: 100%;
  max-width: 300px;
}

.video-preview {
  max-height: 200px;
}

.upload-demo {
  text-align: center;
}

/* 确保缩略图上传对话框显示在最顶层 */
.thumbnail-upload-dialog {
  z-index: 3000 !important;
}

.thumbnail-upload-dialog .el-dialog {
  z-index: 3001 !important;
}

.thumbnail-upload-dialog .el-dialog__wrapper {
  z-index: 3002 !important;
}
</style>
