<template>
  <div class="sentence-manage">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 添加句子
      </el-button>
      <el-button type="warning" @click="handleReprocess">
        <i class="el-icon-refresh"></i> 重新处理字幕
      </el-button>
      <el-button type="info" @click="openTimelineEditor">
        <i class="el-icon-time"></i> 打开时间轴编辑器
      </el-button>
      <el-button @click="handleClose">
        <i class="el-icon-close"></i> 关闭
      </el-button>
    </div>

    <!-- 媒体播放器 -->
    <div class="media-player-section" v-if="videoUrl || audioUrl">
      <div class="media-player">
        <!-- 视频播放器（静音播放） -->
        <div class="video-container" v-if="videoUrl">
          <video
            ref="videoPlayer"
            :src="videoUrl"
            @loadedmetadata="onMediaLoaded"
            @timeupdate="onTimeUpdate"
            @ended="onMediaEnded"
            @error="onMediaError"
            preload="metadata"
            controls
            muted
            style="width: 100%; max-height: 300px;"
          ></video>
        </div>
        
        <!-- 音频播放器（播放原唱音频） -->
        <div class="audio-container" v-if="audioUrl">
          <audio
            ref="audioPlayer"
            :src="audioUrl"
            @loadedmetadata="onMediaLoaded"
            @timeupdate="onTimeUpdate"
            @ended="onMediaEnded"
            @error="onMediaError"
            preload="metadata"
            controls
            style="width: 100%;"
          ></audio>
        </div>
        
        <!-- 播放控制面板 -->
        <div class="player-controls" v-if="audioUrl || videoUrl">
          <el-button
            :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
            @click="togglePlay"
            size="small"
            circle
          ></el-button>
          
          <span class="time-display-player">
            {{ formatTime(currentTime * 1000) }} / {{ formatTime(duration * 1000) }}
          </span>
          
          <el-button
            icon="el-icon-refresh-left"
            @click="seekBackward"
            size="small"
            title="后退5秒"
          ></el-button>
          
          <el-button
            icon="el-icon-refresh-right"
            @click="seekForward"
            size="small"
            title="前进5秒"
          ></el-button>
          
          <div class="playback-rate-control">
            <span class="rate-label">速度:</span>
            <el-slider
              v-model="playbackRate"
              :min="0.5"
              :max="2"
              :step="0.1"
              :show-tooltip="false"
              @change="onPlaybackRateChange"
              style="width: 100px;"
            ></el-slider>
            <span class="rate-display">{{ playbackRate }}x</span>
          </div>
          
          <div class="volume-control">
            <i class="el-icon-voice"></i>
            <el-slider
              v-model="volume"
              :min="0"
              :max="100"
              @change="onVolumeChange"
              style="width: 80px;"
            ></el-slider>
          </div>
        </div>
      </div>
    </div>

    <!-- 句子列表 -->
    <el-table
      v-loading="loading"
      :data="sentenceList"
      border
      style="width: 100%"
      max-height="500"
    >
      <el-table-column prop="sentenceIndex" label="序号" width="80" />
      <el-table-column label="时间轴" width="180">
        <template slot-scope="scope">
          <div class="time-range">
            <span>{{ formatTime(scope.row.startTime) }}</span>
            <i class="el-icon-right"></i>
            <span>{{ formatTime(scope.row.endTime) }}</span>
          </div>
          <div class="duration">
            时长: {{ formatDuration(scope.row.duration) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="lyricsText" label="歌词文本" min-width="200" show-overflow-tooltip />
      <el-table-column prop="lyricsPinyin" label="拼音" min-width="150" show-overflow-tooltip />
      <el-table-column label="难度评分" width="100">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.difficultyScore"
            :max="5"
            :allow-half="true"
            size="mini"
            disabled
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="playSegment(scope.row)" title="播放片段">
            <i class="el-icon-video-play"></i>
          </el-button>
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :z-index="3000"
      :modal-append-to-body="false"
      append-to-body
    >
      <el-form
        ref="sentenceForm"
        :model="currentSentence"
        :rules="sentenceRules"
        label-width="100px"
      >
        <el-form-item label="句子序号" prop="sentenceIndex">
          <el-input-number
            v-model="currentSentence.sentenceIndex"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-input-number
                v-model="currentSentence.startTime"
                :min="0"
                :step="100"
                placeholder="毫秒"
                style="width: 100%"
                @change="calculateDuration"
              />
              <div class="time-display">{{ formatTime(currentSentence.startTime) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-input-number
                v-model="currentSentence.endTime"
                :min="0"
                :step="100"
                placeholder="毫秒"
                style="width: 100%"
                @change="calculateDuration"
              />
              <div class="time-display">{{ formatTime(currentSentence.endTime) }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="时长">
          <el-input-number
            v-model="currentSentence.duration"
            :min="0"
            disabled
            style="width: 100%"
          />
          <div class="time-display">{{ formatDuration(currentSentence.duration) }}</div>
        </el-form-item>
        
        <el-form-item label="歌词文本" prop="lyricsText">
          <el-input
            v-model="currentSentence.lyricsText"
            type="textarea"
            :rows="3"
            placeholder="请输入歌词文本"
          />
        </el-form-item>
        
        <el-form-item label="拼音" prop="lyricsPinyin">
          <el-input
            v-model="currentSentence.lyricsPinyin"
            type="textarea"
            :rows="2"
            placeholder="请输入拼音（可选）"
          />
        </el-form-item>
        
        <el-form-item label="难度评分" prop="difficultyScore">
          <el-rate
            v-model="currentSentence.difficultyScore"
            :max="5"
            :allow-half="true"
            show-score
            text-color="#ff9900"
          />
        </el-form-item>
      </el-form>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveSentence" :loading="saving">
          保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNurserySongSentences,
  updateNurserySongSentence,
  deleteNurserySongSentence,
  processNurserySongResources,
  getNurserySongDetail
} from '@/api/xhb/nurserySong'


export default {
  name: 'SentenceManage',
  components: {},
  props: {
    songId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      saving: false,
      sentenceList: [],
      dialogVisible: false,
      isEdit: false,

      audioUrl: '',
      videoUrl: '',
      songDetail: null,
      // 媒体播放器相关状态
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      playbackRate: 1.0,
      volume: 50,
      // 句子片段播放控制
      segmentEndTime: null,
      segmentTimeoutId: null,
      currentSentence: {
        id: null,
        songId: null,
        sentenceIndex: 0,
        startTime: 0,
        endTime: 0,
        duration: 0,
        lyricsText: '',
        lyricsPinyin: '',
        difficultyScore: 1.0
      },
      sentenceRules: {
        sentenceIndex: [
          { required: true, message: '请输入句子序号', trigger: 'blur' },
          { type: 'number', min: 0, message: '序号不能小于0', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请输入开始时间', trigger: 'blur' },
          { type: 'number', min: 0, message: '开始时间不能小于0', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '请输入结束时间', trigger: 'blur' },
          { type: 'number', min: 0, message: '结束时间不能小于0', trigger: 'blur' }
        ],
        lyricsText: [
          { required: true, message: '请输入歌词文本', trigger: 'blur' },
          { max: 200, message: '歌词文本长度不能超过200个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑句子' : '添加句子'
    }
  },
  mounted() {
    this.loadSentences()
    this.loadSongDetail()
  },
  methods: {
    // 加载句子列表
    async loadSentences() {
      this.loading = true
      try {
        const response = await getNurserySongSentences({ songId: this.songId })
        if (response.code === 200) {
          this.sentenceList = response.data || []
        } else {
          this.$message.error(response.msg || '获取句子列表失败')
        }
      } catch (error) {
        this.$message.error('网络请求失败')
        console.error('Load sentences error:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 添加句子
    handleAdd() {
      this.currentSentence = {
        id: null,
        songId: this.songId,
        sentenceIndex: this.sentenceList.length,
        startTime: 0,
        endTime: 0,
        duration: 0,
        lyricsText: '',
        lyricsPinyin: '',
        difficultyScore: 1.0
      }
      this.isEdit = false
      this.dialogVisible = true
    },
    
    // 编辑句子
    handleEdit(row) {
      this.currentSentence = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },
    
    // 删除句子
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个句子吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          zIndex: 3100
        })
        
        const response = await deleteNurserySongSentence(row.id)
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadSentences()
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('Delete sentence error:', error)
        }
      }
    },
    
    // 保存句子
    async handleSaveSentence() {
      try {
        await this.$refs.sentenceForm.validate()
        
        // 验证时间轴
        if (this.currentSentence.startTime >= this.currentSentence.endTime) {
          this.$message.error('开始时间必须小于结束时间')
          return
        }
        
        this.saving = true
        const response = await updateNurserySongSentence(this.currentSentence)
        
        if (response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '添加成功')
          this.dialogVisible = false
          this.loadSentences()
        } else {
          this.$message.error(response.msg || '保存失败')
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error('保存失败')
          console.error('Save sentence error:', error)
        }
      } finally {
        this.saving = false
      }
    },
    
    // 重新处理字幕
    async handleReprocess() {
      try {
        await this.$confirm('确定要重新处理字幕吗？这将覆盖现有的句子数据。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          zIndex: 3100
        })
        
        this.loading = true
        const response = await processNurserySongResources(this.songId)
        
        if (response.code === 200) {
          this.$message.success('字幕处理成功')
          this.loadSentences()
        } else {
          this.$message.error(response.msg || '字幕处理失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('字幕处理失败')
          console.error('Reprocess subtitle error:', error)
        }
      } finally {
        this.loading = false
      }
    },
    
    // 关闭
    handleClose() {
      this.$emit('close')
    },
    
    // 计算时长
    calculateDuration() {
      if (this.currentSentence.startTime && this.currentSentence.endTime) {
        this.currentSentence.duration = this.currentSentence.endTime - this.currentSentence.startTime
      }
    },
    
    // 格式化时间（毫秒转为 mm:ss.SSS）
    formatTime(milliseconds) {
      if (!milliseconds && milliseconds !== 0) return '00:00.000'
      
      const totalSeconds = Math.floor(milliseconds / 1000)
      const minutes = Math.floor(totalSeconds / 60)
      const seconds = totalSeconds % 60
      const ms = milliseconds % 1000
      
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
    },
    
    // 格式化时长
    formatDuration(milliseconds) {
      if (!milliseconds && milliseconds !== 0) return '0ms'
      
      if (milliseconds < 1000) {
        return `${milliseconds}ms`
      } else {
        const seconds = (milliseconds / 1000).toFixed(1)
        return `${seconds}s`
      }
    },

    // 加载歌曲详情
    async loadSongDetail() {
      try {
        console.log('Loading song detail for songId:', this.songId)
        const response = await getNurserySongDetail(this.songId)
        console.log('Song detail response:', response)
        
        if (response.code === 200) {
          this.songDetail = response.data
          console.log('Song detail data:', this.songDetail)
          
          // 注意：后端返回的数据结构是 { song: NurserySongDO, sentences: [...] }
          const song = this.songDetail.song || this.songDetail
          console.log('Song object:', song)
          
          // 设置视频URL（优先）
          this.videoUrl = song.videoUrl || ''
          // 设置音频URL，优先使用原唱音频
          this.audioUrl = song.originalAudioUrl || song.backgroundAudioUrl || ''
          
          console.log('Media URLs set:', {
            video: this.videoUrl,
            audio: this.audioUrl,
            songObject: {
              videoUrl: song.videoUrl,
              originalAudioUrl: song.originalAudioUrl,
              backgroundAudioUrl: song.backgroundAudioUrl
            },
            fullResponse: this.songDetail
          })
          
          // 检查URL是否有效
          if (!this.videoUrl && !this.audioUrl) {
            this.$message.warning('未找到可播放的媒体文件')
            console.warn('No media URLs found. Song data:', song)
          }
        } else {
          this.$message.error(response.msg || '获取歌曲详情失败')
        }
      } catch (error) {
        console.error('Load song detail error:', error)
        this.$message.error('加载歌曲详情失败')
      }
    },

    // 打开时间轴编辑器页面
    openTimelineEditor() {
      // 跳转到新的时间轴编辑器页面
      this.$router.push({
        name: 'timelineEditor',
        params: {
          songId: this.songId
        }
      })
    },

    // 媒体播放器相关方法
    // 获取当前媒体元素
    getMediaElement() {
      // 当同时有视频和音频时，优先使用音频播放器进行控制
      // 视频播放器设置为静音，音频播放器播放原唱音频
      if (this.audioUrl && this.$refs.audioPlayer) {
        return this.$refs.audioPlayer
      }
      return this.$refs.videoPlayer || this.$refs.audioPlayer
    },

    // 切换播放/暂停
    togglePlay() {
      const media = this.getMediaElement()
      if (!media) return

      if (this.isPlaying) {
        media.pause()
        // 如果同时有视频和音频，同步暂停视频
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
          this.$refs.videoPlayer.pause()
        }
        // 暂停时清除片段播放控制
        this.segmentEndTime = null
      } else {
        media.play()
        // 如果同时有视频和音频，同步播放视频
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
          this.$refs.videoPlayer.play()
        }
      }
    },

    // 媒体加载完成
    onMediaLoaded() {
      const media = this.getMediaElement()
      if (media) {
        this.duration = media.duration
        this.onVolumeChange() // 设置初始音量
        this.onPlaybackRateChange() // 设置初始播放速度
      }
    },

    // 时间更新
    onTimeUpdate() {
      const media = this.getMediaElement()
      if (media) {
        this.currentTime = media.currentTime
        this.isPlaying = !media.paused
        
        // 如果同时有视频和音频，同步视频播放时间
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
          const timeDiff = Math.abs(this.$refs.videoPlayer.currentTime - media.currentTime)
          if (timeDiff > 0.1) { // 如果时间差超过0.1秒，进行同步
            this.$refs.videoPlayer.currentTime = media.currentTime
          }
        }
        
        // 检查句子片段播放是否需要停止
        if (this.segmentEndTime !== null && media.currentTime >= this.segmentEndTime) {
          media.pause()
          // 同步暂停视频
          if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
            this.$refs.videoPlayer.pause()
          }
          this.segmentEndTime = null
          this.$message.success('句子片段播放完成')
        }
      }
    },

    // 媒体播放结束
    onMediaEnded() {
      this.isPlaying = false
      this.segmentEndTime = null
    },

    // 媒体加载错误
    onMediaError(event) {
      console.error('Media error:', event)
      this.$message.error('媒体文件加载失败')
    },

    // 后退5秒
    seekBackward() {
      const media = this.getMediaElement()
      if (media) {
        media.currentTime = Math.max(0, media.currentTime - 5)
      }
    },

    // 前进5秒
    seekForward() {
      const media = this.getMediaElement()
      if (media) {
        media.currentTime = Math.min(media.duration, media.currentTime + 5)
      }
    },

    // 跳转到指定时间
    seekTo(time) {
      const media = this.getMediaElement()
      if (media) {
        media.currentTime = time
        // 如果同时有视频和音频，同步视频时间
        if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
          this.$refs.videoPlayer.currentTime = time
        }
      }
    },

    // 播放速度改变
    onPlaybackRateChange() {
      const media = this.getMediaElement()
      if (media) {
        media.playbackRate = this.playbackRate
      }
    },

    // 音量改变
    onVolumeChange() {
      const media = this.getMediaElement()
      if (media) {
        media.volume = this.volume / 100
      }
    },

    // 播放指定句子片段
    playSegment(sentence) {
      if (!sentence.startTime && sentence.startTime !== 0) {
        this.$message.warning('该句子没有设置开始时间')
        return
      }

      const media = this.getMediaElement()
      if (!media) {
        this.$message.warning('没有可播放的媒体文件')
        return
      }

      // 清除之前的片段播放控制
      this.segmentEndTime = null
      if (this.segmentTimeoutId) {
        clearTimeout(this.segmentTimeoutId)
        this.segmentTimeoutId = null
      }

      // 跳转到开始时间（转换为秒）
      const startTimeInSeconds = sentence.startTime / 1000
      media.currentTime = startTimeInSeconds
      
      // 如果同时有视频和音频，同步视频时间
      if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
        this.$refs.videoPlayer.currentTime = startTimeInSeconds
      }
      
      // 设置结束时间（如果有的话）
      if (sentence.endTime) {
        this.segmentEndTime = sentence.endTime / 1000
      }
      
      // 开始播放
      media.play()
      
      // 如果同时有视频和音频，同步播放视频
      if (this.videoUrl && this.audioUrl && this.$refs.videoPlayer && media === this.$refs.audioPlayer) {
        this.$refs.videoPlayer.play()
      }
      
      this.$message.success(`开始播放句子: ${sentence.lyricsText || '未知歌词'}`)
    }
  }
}
</script>

<style scoped>
.sentence-manage {
  /* padding: 20px; */
  max-height: 700px;
  overflow-y: auto;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}



.time-range {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #303133;
}

.time-range i {
  margin: 0 5px;
  color: #909399;
}

.duration {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.time-display {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  text-align: right;
}

.el-rate {
  display: inline-block;
}

/* 媒体播放器样式 */
.media-player-section {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.media-player {
  background: #f8f9fa;
}

.video-container,
.audio-container {
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-container video,
.audio-container audio {
  display: block;
}

.player-controls {
  padding: 15px 20px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.time-display-player {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  min-width: 140px;
}

.playback-rate-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-label {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.rate-display {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
  text-align: center;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-control i {
  color: #909399;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
  }
  
  .player-controls {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .player-controls > * {
    justify-content: center;
  }
  
  .playback-rate-control,
  .volume-control {
    justify-content: center;
  }
}
</style>
