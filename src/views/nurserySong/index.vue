<template>
  <div class="nursery-song-management">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="标题搜索">
          <el-input
            v-model="searchForm.title"
            placeholder="输入儿歌标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态筛选">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级">
          <el-select v-model="searchForm.difficultyLevel" placeholder="全部难度" clearable style="width: 120px">
            <el-option
              v-for="item in difficultyLevels"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 添加儿歌
      </el-button>
      <el-button type="success" @click="handleBatchUpload">
        <i class="el-icon-upload"></i> 批量上传
      </el-button>
      <el-button 
        type="warning" 
        :disabled="selectedRows.length === 0"
        @click="handleBatchProcess"
      >
        <i class="el-icon-setting"></i> 批量处理
      </el-button>
      <el-button 
        type="danger" 
        :disabled="selectedRows.length === 0"
        @click="handleBatchStatusUpdate"
      >
        <i class="el-icon-edit"></i> 批量状态更新
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      height="580px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
      <el-table-column label="缩略图" width="80">
        <template slot-scope="scope">
          <el-image 
            style="width: 60px;"
            :src="scope.row.thumbnailUrl" 
            :preview-src-list="[scope.row.thumbnailUrl]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="时长" width="80">
        <template slot-scope="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column label="难度" width="80">
        <template slot-scope="scope">
          <el-tag size="small" :type="getDifficultyTagType(scope.row.difficultyLevel)">
            {{ getDifficultyText(scope.row.difficultyLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sentenceCount" label="句子数" width="80" />
      <el-table-column label="状态" width="80">
        <template slot-scope="scope">
          <el-tag size="small" :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="playCount" label="播放次数" width="100" />
      <el-table-column prop="recordingCount" label="录音次数" width="100" />
      <el-table-column label="创建时间" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button size="mini" type="success" @click="handleManageSentences(scope.row)">
            句子管理
          </el-button>
          <el-button size="mini" type="warning" @click="handleProcess(scope.row)">
            处理
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="60%"
      height="900px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <nursery-song-form
        ref="songForm"
        :form-data="currentSong"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </el-dialog>

    <!-- 批量上传对话框 -->
    <el-dialog
      title="批量上传文件"
      :visible.sync="batchUploadVisible"
      width="50%"
       top="5vh"
      :close-on-click-modal="false"
    >
      <batch-upload-form
        @success="handleBatchUploadSuccess"
        @cancel="batchUploadVisible = false"
      />
    </el-dialog>

    <!-- 句子管理对话框 -->
    <el-dialog
      title="句子管理"
      :visible.sync="sentenceManageVisible"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <sentence-manage
        v-if="sentenceManageVisible"
        :song-id="currentSongId"
        @close="sentenceManageVisible = false"
      />
    </el-dialog>

    <!-- 批量状态更新对话框 -->
    <el-dialog
      title="批量状态更新"
      :visible.sync="batchStatusVisible"
      width="30%"
    >
      <el-form label-width="80px">
        <el-form-item label="新状态">
          <el-select v-model="batchStatus" placeholder="请选择状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchStatusVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchStatusUpdate">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNurserySongList,
  deleteNurserySong,
  updateNurserySongStatus,
  batchUpdateNurserySongStatus,
  processNurserySongResources,
  batchProcessNurserySongs,
  getDifficultyLevels,
  getStatusOptions
} from '@/api/xhb/nurserySong'

import NurserySongForm from './components/NurserySongForm.vue'
import BatchUploadForm from './components/BatchUploadForm.vue'
import SentenceManage from './components/SentenceManage.vue'

export default {
  name: 'NurserySongManagement',
  components: {
    NurserySongForm,
    BatchUploadForm,
    SentenceManage
  },
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [],
      
      // 搜索表单
      searchForm: {
        title: '',
        status: null,
        difficultyLevel: null
      },
      
      // 分页
      pagination: {
        pageIndex: 1,
        pageSize: 20,
        total: 0
      },
      
      // 对话框
      dialogVisible: false,
      batchUploadVisible: false,
      sentenceManageVisible: false,
      batchStatusVisible: false,
      
      // 表单数据
      currentSong: {},
      currentSongId: null,
      isEdit: false,
      batchStatus: null,
      
      // 选项数据
      difficultyLevels: getDifficultyLevels(),
      statusOptions: getStatusOptions()
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑儿歌' : '添加儿歌'
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleDialogClose(){
      this.$refs.songForm.resetForm()
      console.log('%c [ this.$refs.songForm ]-284', 'font-size:13px; background:pink; color:#bf2c9f;', this.$refs.songForm)
    },
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          pageIndex: this.pagination.pageIndex,
          pageSize: this.pagination.pageSize
        }
        
        const response = await getNurserySongList(params)
        if (response.code === 200) {
          this.tableData = response.data.list || []
          this.pagination.total = response.data.totalCount || 0
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        this.$message.error('网络请求失败')
        console.error('Load data error:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.pageIndex = 1
      this.loadData()
    },
    
    // 重置搜索
    handleResetSearch() {
      this.searchForm = {
        title: '',
        status: null,
        difficultyLevel: null
      }
      this.pagination.pageIndex = 1
      this.loadData()
    },
    
    // 分页
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageIndex = 1
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.pagination.pageIndex = val
      this.loadData()
    },
    
    // 选择行
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 添加
    handleAdd() {
      this.currentSong = {}
      this.isEdit = false
      this.dialogVisible = true
    },
    
    // 编辑
    handleEdit(row) {
      this.currentSong = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },
    
    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这首儿歌吗？此操作不可恢复。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await deleteNurserySong(row.id)
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('Delete error:', error)
        }
      }
    },
    
    // 处理资源
    async handleProcess(row) {
      try {
        await this.$confirm('确定要处理这首儿歌的资源吗？这将解析字幕文件并生成句子数据。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        
        this.loading = true
        const response = await processNurserySongResources(row.id)
        if (response.code === 200) {
          this.$message.success('处理成功')
          this.loadData()
        } else {
          this.$message.error(response.msg || '处理失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('处理失败')
          console.error('Process error:', error)
        }
      } finally {
        this.loading = false
      }
    },
    
    // 句子管理
    handleManageSentences(row) {
      this.currentSongId = row.id
      this.sentenceManageVisible = true
    },
    
    // 批量上传
    handleBatchUpload() {
      this.batchUploadVisible = true
    },
    
    // 批量处理
    async handleBatchProcess() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要处理的儿歌')
        return
      }
      
      try {
        await this.$confirm(`确定要批量处理选中的 ${this.selectedRows.length} 首儿歌吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        
        this.loading = true
        const songIds = this.selectedRows.map(row => row.id)
        const response = await batchProcessNurserySongs(songIds)
        
        if (response.code === 200) {
          this.$message.success('批量处理成功')
          this.loadData()
        } else {
          this.$message.error(response.msg || '批量处理失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量处理失败')
          console.error('Batch process error:', error)
        }
      } finally {
        this.loading = false
      }
    },
    
    // 批量状态更新
    handleBatchStatusUpdate() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要更新状态的儿歌')
        return
      }
      this.batchStatus = null
      this.batchStatusVisible = true
    },
    
    // 确认批量状态更新
    async confirmBatchStatusUpdate() {
      if (this.batchStatus === null) {
        this.$message.warning('请选择新状态')
        return
      }
      
      try {
        this.loading = true
        const ids = this.selectedRows.map(row => row.id)
        const response = await batchUpdateNurserySongStatus(ids, this.batchStatus)
        
        if (response.code === 200) {
          this.$message.success('批量更新成功')
          this.batchStatusVisible = false
          this.loadData()
        } else {
          this.$message.error(response.msg || '批量更新失败')
        }
      } catch (error) {
        this.$message.error('批量更新失败')
        console.error('Batch status update error:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 表单提交
    handleFormSubmit() {
      this.dialogVisible = false
      this.loadData()
    },
    
    // 表单取消
    handleFormCancel() {
      this.dialogVisible = false
    },
    
    // 批量上传成功
    handleBatchUploadSuccess() {
      this.batchUploadVisible = false
      this.loadData()
    },
    
    // 格式化时长
    formatDuration(milliseconds) {
      if (!milliseconds) return '0:00'
      const seconds = Math.floor(milliseconds / 1000)
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    // 获取难度文本
    getDifficultyText(level) {
      const item = this.difficultyLevels.find(d => d.value === level)
      return item ? item.label : '未知'
    },
    
    // 获取难度标签类型
    getDifficultyTagType(level) {
      const types = ['', 'success', 'info', 'warning', 'danger']
      return types[level] || ''
    },
    
    // 获取状态文本
    getStatusText(status) {
      const item = this.statusOptions.find(s => s.value === status)
      return item ? item.label : '未知'
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const item = this.statusOptions.find(s => s.value === status)
      return item ? item.type : ''
    }
  }
}
</script>

<style scoped>
.nursery-song-management {
  padding: 20px;
}

.search-bar {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.action-bar {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
