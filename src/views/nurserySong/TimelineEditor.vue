<template>
  <div class="timeline-editor-page">
    <div class="page-header">
      <el-page-header @back="goBack" content="时间轴编辑器">
        <template slot="title">
          <span>返回儿歌管理</span>
        </template>
      </el-page-header>
    </div>
    
    <div class="page-content">
      <TimelineEditor
        :song-id="songId"
        :video-url="videoUrl"
        :audio-url="audioUrl"
        :sentences="sentences"
        @sentence-updated="handleSentenceUpdated"
        @sentence-deleted="handleSentenceDeleted"
      />
    </div>
  </div>
</template>

<script>
import TimelineEditor from '@/components/TimelineEditor'
import { getNurserySongDetail, getNurserySongSentences } from '@/api/xhb/nurserySong'

export default {
  name: 'TimelineEditorPage',
  components: {
    TimelineEditor
  },
  data() {
    return {
      songId: null,
      videoUrl: '',
      audioUrl: '',
      sentences: []
    }
  },
  async created() {
    this.songId = this.$route.params.songId
    if (this.songId) {
      await this.loadSongData()
    }
  },
  methods: {
    async loadSongData() {
      try {
        // 加载歌曲详情
        const detailResponse = await getNurserySongDetail(this.songId)
        if (detailResponse.code === 200 && detailResponse.data) {
          const songDetail = detailResponse.data
          
          // 从嵌套的song对象中获取媒体URL
          if (songDetail.song) {
            this.videoUrl = songDetail.song.videoUrl || ''
            this.audioUrl = songDetail.song.originalAudioUrl || songDetail.song.backgroundAudioUrl || ''
          }
          
          console.log('时间轴编辑器 - 媒体URL:', {
            videoUrl: this.videoUrl,
            audioUrl: this.audioUrl
          })
        }
        
        // 加载句子数据
        const sentencesResponse = await getNurserySongSentences({ songId: this.songId })
        if (sentencesResponse.code === 200 && sentencesResponse.data) {
          this.sentences = sentencesResponse.data
        }
      } catch (error) {
        console.error('加载歌曲数据失败:', error)
        this.$message.error('加载歌曲数据失败')
      }
    },
    
    handleSentenceUpdated(sentence) {
      // 更新句子列表
      const index = this.sentences.findIndex(s => s.id === sentence.id)
      if (index !== -1) {
        this.$set(this.sentences, index, sentence)
      }
    },
    
    handleSentenceDeleted(sentenceId) {
      // 从句子列表中移除
      this.sentences = this.sentences.filter(s => s.id !== sentenceId)
    },
    
    goBack() {
      this.$router.push('/nursery-song')
    }
  }
}
</script>

<style scoped>
.timeline-editor-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 80px);
}
</style>