// 儿歌资源管理模块样式

.nursery-song-management {
  .search-bar {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .search-form {
      margin: 0;
      
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;
      }
    }
  }
  
  .action-bar {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}

// 表单样式
.nursery-song-form {
  .form-actions {
    text-align: right;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
  
  .preview-item {
    margin-bottom: 15px;
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #606266;
    }
  }
  
  .thumbnail-preview {
    max-width: 200px;
    max-height: 150px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
  
  .audio-preview,
  .video-preview {
    width: 100%;
    max-width: 300px;
  }
  
  .video-preview {
    max-height: 200px;
  }
}

// 文件上传输入组件样式
.file-upload-input {
  .file-info {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    display: flex;
    align-items: center;
    
    .file-name {
      margin-left: 8px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .file-size {
      margin-left: 8px;
      color: #909399;
      font-size: 12px;
    }
  }
}

// 批量上传样式
.batch-upload-form {
  .upload-section {
    margin-bottom: 30px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;
    
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 14px;
      
      i {
        margin-right: 5px;
        color: #409eff;
      }
    }
  }
  
  .upload-results {
    margin-bottom: 20px;
  }
  
  .form-actions {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
}

// 句子管理样式
.sentence-manage {
  .action-bar {
    margin-bottom: 20px;
  }
  
  .time-range {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #303133;
    
    i {
      margin: 0 5px;
      color: #909399;
    }
  }
  
  .duration {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
  
  .time-display {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
}

// 通用样式
.dialog-footer {
  text-align: right;
}

// 状态标签样式
.status-tag {
  &.status-online {
    background-color: #f0f9ff;
    color: #1890ff;
    border-color: #91d5ff;
  }
  
  &.status-offline {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #ffccc7;
  }
  
  &.status-pending {
    background-color: #fffbe6;
    color: #faad14;
    border-color: #ffe58f;
  }
}

// 难度标签样式
.difficulty-tag {
  &.difficulty-1 {
    background-color: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
  }
  
  &.difficulty-2 {
    background-color: #f0f9ff;
    color: #1890ff;
    border-color: #91d5ff;
  }
  
  &.difficulty-3 {
    background-color: #fffbe6;
    color: #faad14;
    border-color: #ffe58f;
  }
  
  &.difficulty-4 {
    background-color: #fff2e8;
    color: #fa8c16;
    border-color: #ffd591;
  }
  
  &.difficulty-5 {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #ffccc7;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .nursery-song-management {
    .search-bar {
      padding: 15px;
      
      .search-form {
        .el-form-item {
          margin-right: 0;
          margin-bottom: 15px;
        }
      }
    }
    
    .action-bar {
      .el-button {
        margin-bottom: 10px;
        width: 100%;
      }
    }
  }
  
  .nursery-song-form {
    .preview-item {
      .thumbnail-preview,
      .audio-preview,
      .video-preview {
        max-width: 100%;
      }
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

// 加载状态
.loading-overlay {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
  }
}

// 文件拖拽上传样式
.upload-dragger {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  text-align: center;
  padding: 40px 20px;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #409eff;
  }
  
  &.is-dragover {
    border-color: #409eff;
    background-color: #f0f9ff;
  }
  
  .upload-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 16px;
  }
  
  .upload-text {
    color: #606266;
    font-size: 14px;
  }
  
  .upload-hint {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}
