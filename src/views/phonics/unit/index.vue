<template>
  <div class="phonics-units">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/phonics/dashboard' }">概览</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/phonics/courses' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ currentCourse.courseNameZh }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>{{ currentCourse.courseNameZh }} - 单元管理</h2>
        <p>{{ currentCourse.courseNameEn }} ({{ currentCourse.courseCode }})</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增单元
        </el-button>
      </div>
    </div>

    <!-- 音素类型筛选 -->
    <el-card class="filter-card">
      <el-radio-group v-model="filterType" @change="handleFilterChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button label="SINGLE_LETTER">单个字母</el-radio-button>
        <el-radio-button label="VOWEL_COMBINATION">元音组合</el-radio-button>
        <el-radio-button label="OTHER">其他</el-radio-button>
      </el-radio-group>
      
      <div class="filter-stats">
        <span>共 {{ filteredUnits.length }} 个单元</span>
      </div>
    </el-card>

    <!-- 单元列表 -->
    <el-card class="table-card">
      <el-table 
        :data="filteredUnits" 
        v-loading="loading"
        @row-click="handleRowClick"
        style="cursor: pointer;"
      >
        <el-table-column prop="sequence" label="序号" width="80" align="center"/>
        <el-table-column prop="unitCode" label="单元编码" width="120"/>
        <el-table-column prop="unitName" label="单元名称" width="150"/>
        <el-table-column prop="phonemeType" label="音素类型" width="120">
          <template slot-scope="scope">
            <el-tag 
              :type="getPhonemeTypeColor(scope.row.phonemeType)"
              size="mini"
            >
              {{ getPhonemeTypeName(scope.row.phonemeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip/>
        <el-table-column prop="letterCount" label="音素数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="success">{{ scope.row.letterCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="wordCount" label="例词数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="warning">{{ scope.row.wordCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              @click.stop="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button 
              size="mini" 
              type="primary"
              @click.stop="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              size="mini" 
              type="danger"
              @click.stop="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 单元表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="unitForm"
        :model="unitForm"
        :rules="unitRules"
        label-width="100px"
      >
        <el-form-item label="单元编码" prop="unitCode">
          <el-input v-model="unitForm.unitCode" placeholder="如：U1"/>
        </el-form-item>
        <el-form-item label="单元名称" prop="unitName">
          <el-input v-model="unitForm.unitName" placeholder="如：b"/>
        </el-form-item>
        <el-form-item label="音素类型" prop="phonemeType">
          <el-select v-model="unitForm.phonemeType" placeholder="请选择音素类型">
            <el-option label="单个字母" value="SINGLE_LETTER"/>
            <el-option label="元音组合" value="VOWEL_COMBINATION"/>
            <el-option label="其他" value="OTHER"/>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="unitForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入单元描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input-number
            v-model="unitForm.sequence"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { courseApi } from '@/api/phonics/course'
import { unitApi } from '@/api/phonics/unit'

export default {
  name: 'PhonicsUnits',
  data() {
    return {
      loading: false,
      submitting: false,
      courseId: null,
      currentCourse: {},
      unitList: [],
      filterType: '',
      dialogVisible: false,
      dialogType: 'add',
      unitForm: {
        unitCode: '',
        unitName: '',
        phonemeType: 'SINGLE_LETTER',
        description: '',
        sequence: 1
      },
      unitRules: {
        unitCode: [
          { required: true, message: '请输入单元编码', trigger: 'blur' }
        ],
        unitName: [
          { required: true, message: '请输入单元名称', trigger: 'blur' }
        ],
        phonemeType: [
          { required: true, message: '请选择音素类型', trigger: 'change' }
        ],
        sequence: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增单元' : '编辑单元'
    },
    
    filteredUnits() {
      if (!this.filterType) {
        return this.unitList
      }
      return this.unitList.filter(unit => unit.phonemeType === this.filterType)
    }
  },
  created() {
    this.courseId = this.$route.params.courseId
    this.fetchCourseDetail()
    this.fetchUnits()
  },
  methods: {
    async fetchCourseDetail() {
      try {
        const response = await courseApi.getCourseDetail(this.courseId)
        this.currentCourse = response.data || {}
      } catch (error) {
        this.$message.error('获取课程详情失败: ' + error.message)
      }
    },

    async fetchUnits() {
      this.loading = true
      try {
        const response = await unitApi.getUnitsByCourse(this.courseId)
        this.unitList = response.data?.list || []
      } catch (error) {
        this.$message.error('获取单元列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    handleFilterChange() {
      // 筛选逻辑已在computed中处理
    },

    getPhonemeTypeName(type) {
      const typeMap = {
        'SINGLE_LETTER': '单个字母',
        'VOWEL_COMBINATION': '元音组合',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },

    getPhonemeTypeColor(type) {
      const colorMap = {
        'SINGLE_LETTER': 'primary',
        'VOWEL_COMBINATION': 'success',
        'OTHER': 'warning'
      }
      return colorMap[type] || 'info'
    },

    handleRowClick(row) {
      this.handleView(row)
    },

    handleView(row) {
      this.$router.push(`/phonics/unit/${row.id}/letters`)
    },

    handleAdd() {
      this.dialogType = 'add'
      this.unitForm = {
        unitCode: '',
        unitName: '',
        phonemeType: 'SINGLE_LETTER',
        description: '',
        sequence: this.unitList.length + 1
      }
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.dialogType = 'edit'
      this.unitForm = { ...row }
      this.dialogVisible = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个单元吗？', '提示', {
          type: 'warning'
        })
        await unitApi.deleteUnit(row.id)
        this.$message.success('删除成功')
        this.fetchUnits()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.unitForm.validate()
        this.submitting = true
        
        if (this.dialogType === 'add') {
          await unitApi.createUnit(this.courseId, this.unitForm)
          this.$message.success('创建成功')
        } else {
          await unitApi.updateUnit(this.unitForm.id, this.unitForm)
          this.$message.success('更新成功')
        }
        
        this.dialogVisible = false
        this.fetchUnits()
      } catch (error) {
        if (error !== false) {
          this.$message.error('操作失败: ' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },

    handleDialogClose() {
      this.$refs.unitForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.phonics-units {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-left {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  
  .el-card__body {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .filter-stats {
    color: #606266;
    font-size: 14px;
  }
}

.table-card {
  // 表格样式
}

.dialog-footer {
  text-align: right;
}
</style>
