<template>
  <div class="phonics-words">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/phonics/dashboard' }">概览</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/phonics/courses' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: `/phonics/course/${currentCourse.id}/units` }">
        {{ currentCourse.courseNameZh }}
      </el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: `/phonics/unit/${currentLetter.unitId}/letters` }">
        {{ currentUnit.unitName }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ currentLetter.letter }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>{{ currentLetter.letter }} - 例词管理</h2>
        <p>音标：{{ currentLetter.ipa }} | 音素类型：{{ getPhonemeTypeName(currentUnit.phonemeType) }}</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="success" icon="el-icon-upload2" @click="handleBatchAdd">
          批量添加
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增例词
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="单词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入单词"
            clearable
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 例词列表 -->
    <el-card class="table-card">
      <div slot="header" class="card-header">
        <span>例词列表 ({{ wordList.length }})</span>
        <div class="header-actions">
          <el-button size="small" @click="playAllSounds">
            <i class="el-icon-video-play"></i>
            播放全部
          </el-button>
        </div>
      </div>

      <el-table
        :data="wordList"
        v-loading="loading"
        @row-click="handleRowClick"
        style="cursor: pointer;"
      >
        <el-table-column prop="sequence" label="序号" width="80" align="center"/>
        <el-table-column prop="word" label="单词" width="120">
          <template slot-scope="scope">
            <span class="word-display">{{ scope.row.word }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phonetic" label="音标" width="120">
          <template slot-scope="scope">
            <span class="phonetic-display">{{ scope.row.phonetic }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="translation" label="中文翻译" width="120"/>
        <el-table-column label="音频" width="200" align="center">
          <template slot-scope="scope">
            <div class="audio-controls">
              <el-button
                v-if="scope.row.fullSoundUrl"
                size="mini"
                type="text"
                @click.stop="playSound(scope.row.fullSoundUrl)"
                title="完整发音"
              >
                <i class="el-icon-video-play"></i>完整
              </el-button>
              <el-button
                v-if="scope.row.initialSoundUrl"
                size="mini"
                type="text"
                @click.stop="playSound(scope.row.initialSoundUrl)"
                title="首音"
              >
                <i class="el-icon-video-play"></i>首音
              </el-button>
              <el-button
                v-if="scope.row.rhymeSoundUrl"
                size="mini"
                type="text"
                @click.stop="playSound(scope.row.rhymeSoundUrl)"
                title="韵尾"
              >
                <i class="el-icon-video-play"></i>韵尾
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="imageUrl" label="图片" width="100" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.imageUrl" class="image-preview">
              <img
                :src="scope.row.imageUrl"
                :alt="scope.row.word"
                @click.stop="previewImage(scope.row.imageUrl)"
                class="word-image"
              />
            </div>
            <span v-else class="no-image">未上传</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click.stop="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click.stop="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 例词表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="wordForm"
        :model="wordForm"
        :rules="wordRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单词" prop="word">
              <el-input v-model="wordForm.word" placeholder="如：ball"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="音标" prop="phonetic">
              <el-input v-model="wordForm.phonetic" placeholder="如：/bɔːl/"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="中文翻译" prop="translation">
          <el-input v-model="wordForm.translation" placeholder="如：球"/>
        </el-form-item>

        <el-form-item label="完整发音">
          <audio-uploader
            v-model="wordForm.fullSoundUrl"
            :upload-url="uploadUrl"
            :headers="uploadHeaders"
          />
        </el-form-item>

        <el-form-item label="首音">
          <audio-uploader
            v-model="wordForm.initialSoundUrl"
            :upload-url="uploadUrl"
            :headers="uploadHeaders"
          />
        </el-form-item>

        <el-form-item label="韵尾">
          <audio-uploader
            v-model="wordForm.rhymeSoundUrl"
            :upload-url="uploadUrl"
            :headers="uploadHeaders"
          />
        </el-form-item>

        <el-form-item label="图片">
          <image-uploader
            v-model="wordForm.imageUrl"
            :upload-url="imageUploadUrl"
            :headers="uploadHeaders"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sequence">
          <el-input-number
            v-model="wordForm.sequence"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量添加对话框 -->
    <el-dialog
      title="批量添加例词"
      :visible.sync="batchDialogVisible"
      width="600px"
    >
      <el-form label-width="100px">
        <el-form-item label="批量输入">
          <el-input
            v-model="batchText"
            type="textarea"
            :rows="10"
            placeholder="请按以下格式输入，每行一个例词：&#10;单词|音标|中文翻译&#10;例如：&#10;ball|/bɔːl/|球&#10;bat|/bæt/|蝙蝠"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="imagePreviewVisible" width="60%">
      <img :src="previewImageUrl" style="width: 100%"/>
    </el-dialog>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" style="display: none;"></audio>
  </div>
</template>

<script>
import { courseApi } from '@/api/phonics/course'
import { unitApi } from '@/api/phonics/unit'
import { letterApi } from '@/api/phonics/letter'
import { wordApi } from '@/api/phonics/word'
import AudioUploader from '@/components/phonics/AudioUploader'
import ImageUploader from '@/components/phonics/ImageUploader'

export default {
  name: 'PhonicsWords',
  components: {
    AudioUploader,
    ImageUploader
  },
  data() {
    return {
      loading: false,
      submitting: false,
      batchSubmitting: false,
      letterId: null,
      currentCourse: {},
      currentUnit: {},
      currentLetter: {},
      wordList: [],
      searchForm: {
        keyword: ''
      },
      dialogVisible: false,
      batchDialogVisible: false,
      imagePreviewVisible: false,
      previewImageUrl: '',
      dialogType: 'add',
      batchText: '',
      wordForm: {
        word: '',
        phonetic: '',
        translation: '',
        fullSoundUrl: '',
        initialSoundUrl: '',
        rhymeSoundUrl: '',
        imageUrl: '',
        sequence: 1
      },
      wordRules: {
        word: [
          { required: true, message: '请输入单词', trigger: 'blur' }
        ],
        phonetic: [
          { required: true, message: '请输入音标', trigger: 'blur' }
        ],
        translation: [
          { required: true, message: '请输入中文翻译', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      },
      uploadUrl: process.env.VUE_APP_AXIOS_BASE_URL + '/kid-phonics/upload/audio',
      imageUploadUrl: process.env.VUE_APP_AXIOS_BASE_URL + '/kid-phonics/upload/image',
      uploadHeaders: {
        'authorization': this.$store.getters.token
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增例词' : '编辑例词'
    }
  },
  created() {
    this.letterId = this.$route.params.letterId
    this.fetchLetterDetail()
    this.fetchWords()
  },
  methods: {
    async fetchLetterDetail() {
      try {
        const response = await letterApi.getLetterDetail(this.letterId)
        this.currentLetter = response.data || {}

        // 获取单元信息
        if (this.currentLetter.unitId) {
          const unitResponse = await unitApi.getUnitDetail(this.currentLetter.unitId)
          this.currentUnit = unitResponse.data || {}

          // 获取课程信息
          if (this.currentUnit.courseId) {
            const courseResponse = await courseApi.getCourseDetail(this.currentUnit.courseId)
            this.currentCourse = courseResponse.data || {}
          }
        }
      } catch (error) {
        this.$message.error('获取音素详情失败: ' + error.message)
      }
    },

    async fetchWords() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchForm.keyword
        }
        const response = await wordApi.getWordsByLetter(this.letterId, params)
        this.wordList = response.data?.list || []
      } catch (error) {
        this.$message.error('获取例词列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    getPhonemeTypeName(type) {
      const typeMap = {
        'SINGLE_LETTER': '单个字母',
        'VOWEL_COMBINATION': '元音组合',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },

    handleSearch() {
      this.fetchWords()
    },

    handleReset() {
      this.searchForm.keyword = ''
      this.fetchWords()
    },

    handleRowClick(row) {
      this.handleEdit(row)
    },

    handleAdd() {
      this.dialogType = 'add'
      this.wordForm = {
        word: '',
        phonetic: '',
        translation: '',
        fullSoundUrl: '',
        initialSoundUrl: '',
        rhymeSoundUrl: '',
        imageUrl: '',
        sequence: this.wordList.length + 1
      }
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.dialogType = 'edit'
      this.wordForm = { ...row }
      this.dialogVisible = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个例词吗？', '提示', {
          type: 'warning'
        })
        await wordApi.deleteWord(row.id)
        this.$message.success('删除成功')
        this.fetchWords()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.wordForm.validate()
        this.submitting = true

        if (this.dialogType === 'add') {
          await wordApi.createWord(this.letterId, this.wordForm)
          this.$message.success('创建成功')
        } else {
          await wordApi.updateWord(this.wordForm.id, this.wordForm)
          this.$message.success('更新成功')
        }

        this.dialogVisible = false
        this.fetchWords()
      } catch (error) {
        if (error !== false) {
          this.$message.error('操作失败: ' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },

    handleDialogClose() {
      this.$refs.wordForm.resetFields()
    },

    handleBatchAdd() {
      this.batchText = ''
      this.batchDialogVisible = true
    },

    async handleBatchSubmit() {
      if (!this.batchText.trim()) {
        this.$message.warning('请输入例词数据')
        return
      }

      try {
        this.batchSubmitting = true
        const lines = this.batchText.trim().split('\n')
        const words = []

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim()
          if (!line) continue

          const parts = line.split('|')
          if (parts.length < 3) {
            this.$message.error(`第${i + 1}行格式错误，请使用：单词|音标|中文翻译`)
            return
          }

          words.push({
            word: parts[0].trim(),
            phonetic: parts[1].trim(),
            translation: parts[2].trim(),
            sequence: this.wordList.length + words.length + 1
          })
        }

        // 批量创建例词
        for (const word of words) {
          await wordApi.createWord(this.letterId, word)
        }

        this.$message.success(`成功添加 ${words.length} 个例词`)
        this.batchDialogVisible = false
        this.fetchWords()
      } catch (error) {
        this.$message.error('批量添加失败: ' + error.message)
      } finally {
        this.batchSubmitting = false
      }
    },

    playSound(url) {
      if (url) {
        const audio = this.$refs.audioPlayer
        audio.src = url
        audio.play().catch(error => {
          this.$message.error('音频播放失败')
          console.error('Audio play error:', error)
        })
      }
    },

    async playAllSounds() {
      const sounds = []
      this.wordList.forEach(word => {
        if (word.fullSoundUrl) {
          sounds.push(word.fullSoundUrl)
        }
      })

      if (sounds.length === 0) {
        this.$message.warning('没有可播放的音频')
        return
      }

      // 依次播放所有音频
      for (let i = 0; i < sounds.length; i++) {
        await this.playAudioWithDelay(sounds[i], 1000)
      }
    },

    playAudioWithDelay(url, delay) {
      return new Promise((resolve) => {
        const audio = this.$refs.audioPlayer
        audio.src = url
        audio.play()
        audio.onended = () => {
          setTimeout(resolve, delay)
        }
      })
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.imagePreviewVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.phonics-words {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.search-card {
  margin-bottom: 20px;

  .search-form {
    margin-bottom: 0;
  }
}

.table-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.word-display {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.phonetic-display {
  font-family: 'Times New Roman', serif;
  font-size: 14px;
  color: #67C23A;
}

.audio-controls {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.image-preview {
  .word-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #dcdfe6;
  }
}

.no-image {
  color: #C0C4CC;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
