<template>
  <div class="phonics-courses">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>课程管理</h2>
        <p>管理自然拼读课程（C1-C6）</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增课程
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="课程名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入课程名称"
            clearable
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 课程列表 -->
    <el-card class="table-card">
      <el-table 
        :data="courseList" 
        v-loading="loading"
        @row-click="handleRowClick"
        style="cursor: pointer;"
      >
        <el-table-column prop="sequence" label="序号" width="80" align="center"/>
        <el-table-column prop="courseCode" label="课程编码" width="120"/>
        <el-table-column prop="courseNameZh" label="中文名称" width="150"/>
        <el-table-column prop="courseNameEn" label="英文名称" width="200"/>
        <el-table-column prop="description" label="描述" show-overflow-tooltip/>
        <el-table-column prop="unitCount" label="单元数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini">{{ scope.row.unitCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="letterCount" label="音素数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="success">{{ scope.row.letterCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="wordCount" label="例词数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="warning">{{ scope.row.wordCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              @click.stop="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button 
              size="mini" 
              type="primary"
              @click.stop="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              size="mini" 
              type="danger"
              @click.stop="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 课程表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="courseForm"
        :model="courseForm"
        :rules="courseRules"
        label-width="100px"
      >
        <el-form-item label="课程编码" prop="courseCode">
          <el-input v-model="courseForm.courseCode" placeholder="如：C1"/>
        </el-form-item>
        <el-form-item label="中文名称" prop="courseNameZh">
          <el-input v-model="courseForm.courseNameZh" placeholder="如：辅音"/>
        </el-form-item>
        <el-form-item label="英文名称" prop="courseNameEn">
          <el-input v-model="courseForm.courseNameEn" placeholder="如：Consonants"/>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="courseForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入课程描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input-number
            v-model="courseForm.sequence"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { courseApi } from '@/api/phonics/course'

export default {
  name: 'PhonicsCourses',
  data() {
    return {
      loading: false,
      submitting: false,
      courseList: [],
      searchForm: {
        keyword: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      dialogVisible: false,
      dialogType: 'add', // add, edit
      courseForm: {
        courseCode: '',
        courseNameZh: '',
        courseNameEn: '',
        description: '',
        sequence: 1
      },
      courseRules: {
        courseCode: [
          { required: true, message: '请输入课程编码', trigger: 'blur' }
        ],
        courseNameZh: [
          { required: true, message: '请输入中文名称', trigger: 'blur' }
        ],
        courseNameEn: [
          { required: true, message: '请输入英文名称', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增课程' : '编辑课程'
    }
  },
  created() {
    this.fetchCourses()
  },
  methods: {
    async fetchCourses() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          keyword: this.searchForm.keyword
        }
        const response = await courseApi.getCourses(params)
        this.courseList = response.data?.list || []
        this.pagination.total = response.data?.total || 0
      } catch (error) {
        this.$message.error('获取课程列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.pagination.pageNum = 1
      this.fetchCourses()
    },

    handleReset() {
      this.searchForm.keyword = ''
      this.pagination.pageNum = 1
      this.fetchCourses()
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.fetchCourses()
    },

    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.fetchCourses()
    },

    handleRowClick(row) {
      this.handleView(row)
    },

    handleView(row) {
      this.$router.push(`/phonics/course/${row.id}/units`)
    },

    handleAdd() {
      this.dialogType = 'add'
      this.courseForm = {
        courseCode: '',
        courseNameZh: '',
        courseNameEn: '',
        description: '',
        sequence: this.courseList.length + 1
      }
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.dialogType = 'edit'
      this.courseForm = { ...row }
      this.dialogVisible = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个课程吗？', '提示', {
          type: 'warning'
        })
        await courseApi.deleteCourse(row.id)
        this.$message.success('删除成功')
        this.fetchCourses()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.courseForm.validate()
        this.submitting = true
        
        if (this.dialogType === 'add') {
          await courseApi.createCourse(this.courseForm)
          this.$message.success('创建成功')
        } else {
          await courseApi.updateCourse(this.courseForm.id, this.courseForm)
          this.$message.success('更新成功')
        }
        
        this.dialogVisible = false
        this.fetchCourses()
      } catch (error) {
        if (error !== false) {
          this.$message.error('操作失败: ' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },

    handleDialogClose() {
      this.$refs.courseForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.phonics-courses {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-left {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.search-card {
  margin-bottom: 20px;
  
  .search-form {
    margin-bottom: 0;
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
