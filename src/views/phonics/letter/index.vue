<template>
  <div class="phonics-letters">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/phonics/dashboard' }">概览</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/phonics/courses' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: `/phonics/course/${currentUnit.courseId}/units` }">
        {{ currentCourse.courseNameZh }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ currentUnit.unitName }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>{{ currentUnit.unitName }} - 音素管理</h2>
        <p>{{ getPhonemeTypeName(currentUnit.phonemeType) }} ({{ currentUnit.unitCode }})</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增音素
        </el-button>
      </div>
    </div>

    <!-- 音素列表 -->
    <el-card class="table-card">
      <el-table 
        :data="letterList" 
        v-loading="loading"
        @row-click="handleRowClick"
        style="cursor: pointer;"
      >
        <el-table-column prop="sequence" label="序号" width="80" align="center"/>
        <el-table-column prop="letter" label="字母/组合" width="150">
          <template slot-scope="scope">
            <span class="letter-display">{{ scope.row.letter }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="ipa" label="IPA音标" width="120">
          <template slot-scope="scope">
            <span class="ipa-display">{{ scope.row.ipa }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isCombination" label="组合音素" width="100" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.isCombination ? 'success' : 'info'"
              size="mini"
            >
              {{ scope.row.isCombination ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="soundUrl" label="发音音频" width="120" align="center">
          <template slot-scope="scope">
            <el-button 
              v-if="scope.row.soundUrl"
              size="mini"
              type="text"
              icon="el-icon-video-play"
              @click.stop="playSound(scope.row.soundUrl)"
            >
              播放
            </el-button>
            <span v-else class="no-audio">未上传</span>
          </template>
        </el-table-column>
        <el-table-column prop="wordCount" label="例词数" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="warning">{{ scope.row.wordCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              @click.stop="handleView(scope.row)"
            >
              查看例词
            </el-button>
            <el-button 
              size="mini" 
              type="primary"
              @click.stop="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              size="mini" 
              type="danger"
              @click.stop="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 音素表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="letterForm"
        :model="letterForm"
        :rules="letterRules"
        label-width="100px"
      >
        <el-form-item label="字母/组合" prop="letter">
          <el-input v-model="letterForm.letter" placeholder="如：b 或 ch"/>
        </el-form-item>
        <el-form-item label="IPA音标" prop="ipa">
          <el-input v-model="letterForm.ipa" placeholder="如：/b/ 或 /tʃ/"/>
        </el-form-item>
        <el-form-item label="组合音素">
          <el-switch v-model="letterForm.isCombination"/>
          <span class="form-tip">是否为组合音素（如ch、sh等）</span>
        </el-form-item>
        <el-form-item label="发音音频">
          <el-upload
            class="audio-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAudioSuccess"
            :before-upload="beforeAudioUpload"
            accept="audio/*"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传mp3/wav文件，且不超过10MB</div>
          </el-upload>
          <div v-if="letterForm.soundUrl" class="audio-preview">
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-video-play"
              @click="playSound(letterForm.soundUrl)"
            >
              播放音频
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-delete"
              @click="removeAudio"
            >
              删除
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input-number
            v-model="letterForm.sequence"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" style="display: none;"></audio>
  </div>
</template>

<script>
import { courseApi } from '@/api/phonics/course'
import { unitApi } from '@/api/phonics/unit'
import { letterApi } from '@/api/phonics/letter'

export default {
  name: 'PhonicsLetters',
  data() {
    return {
      loading: false,
      submitting: false,
      unitId: null,
      currentCourse: {},
      currentUnit: {},
      letterList: [],
      dialogVisible: false,
      dialogType: 'add',
      letterForm: {
        letter: '',
        ipa: '',
        isCombination: false,
        soundUrl: '',
        sequence: 1
      },
      letterRules: {
        letter: [
          { required: true, message: '请输入字母或组合', trigger: 'blur' }
        ],
        ipa: [
          { required: true, message: '请输入IPA音标', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      },
      uploadUrl: process.env.VUE_APP_BASE_API + '/kwapi/kid-phonics/upload/audio',
      uploadHeaders: {
        'Authorization': 'Bearer ' + this.$store.getters.token
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增音素' : '编辑音素'
    }
  },
  created() {
    this.unitId = this.$route.params.unitId
    this.fetchUnitDetail()
    this.fetchLetters()
  },
  methods: {
    async fetchUnitDetail() {
      try {
        const response = await unitApi.getUnitDetail(this.unitId)
        this.currentUnit = response.data || {}
        
        // 获取课程信息
        if (this.currentUnit.courseId) {
          const courseResponse = await courseApi.getCourseDetail(this.currentUnit.courseId)
          this.currentCourse = courseResponse.data || {}
        }
      } catch (error) {
        this.$message.error('获取单元详情失败: ' + error.message)
      }
    },

    async fetchLetters() {
      this.loading = true
      try {
        const response = await letterApi.getLettersByUnit(this.unitId)
        this.letterList = response.data?.list || []
      } catch (error) {
        this.$message.error('获取音素列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    getPhonemeTypeName(type) {
      const typeMap = {
        'SINGLE_LETTER': '单个字母',
        'VOWEL_COMBINATION': '元音组合',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },

    handleRowClick(row) {
      this.handleView(row)
    },

    handleView(row) {
      this.$router.push(`/phonics/letter/${row.id}/words`)
    },

    handleAdd() {
      this.dialogType = 'add'
      this.letterForm = {
        letter: '',
        ipa: '',
        isCombination: false,
        soundUrl: '',
        sequence: this.letterList.length + 1
      }
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.dialogType = 'edit'
      this.letterForm = { ...row }
      this.dialogVisible = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个音素吗？', '提示', {
          type: 'warning'
        })
        await letterApi.deleteLetter(row.id)
        this.$message.success('删除成功')
        this.fetchLetters()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.letterForm.validate()
        this.submitting = true
        
        if (this.dialogType === 'add') {
          await letterApi.createLetter(this.unitId, this.letterForm)
          this.$message.success('创建成功')
        } else {
          await letterApi.updateLetter(this.letterForm.id, this.letterForm)
          this.$message.success('更新成功')
        }
        
        this.dialogVisible = false
        this.fetchLetters()
      } catch (error) {
        if (error !== false) {
          this.$message.error('操作失败: ' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },

    handleDialogClose() {
      this.$refs.letterForm.resetFields()
    },

    beforeAudioUpload(file) {
      const isAudio = file.type.startsWith('audio/')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isAudio) {
        this.$message.error('只能上传音频文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('音频文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    handleAudioSuccess(response) {
      if (response.success) {
        this.letterForm.soundUrl = response.data.url
        this.$message.success('音频上传成功')
      } else {
        this.$message.error('音频上传失败: ' + response.message)
      }
    },

    removeAudio() {
      this.letterForm.soundUrl = ''
    },

    playSound(url) {
      if (url) {
        const audio = this.$refs.audioPlayer
        audio.src = url
        audio.play().catch(error => {
          this.$message.error('音频播放失败')
          console.error('Audio play error:', error)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.phonics-letters {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-left {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.letter-display {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.ipa-display {
  font-family: 'Times New Roman', serif;
  font-size: 16px;
  color: #67C23A;
}

.no-audio {
  color: #C0C4CC;
  font-size: 12px;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.audio-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}

.audio-preview {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
