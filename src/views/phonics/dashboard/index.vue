<template>
  <div class="phonics-dashboard">
    <div class="dashboard-header">
      <h1>自然拼读资源管理系统</h1>
      <p>管理C1-C6课程的自然拼读内容，包括音素、例词、儿歌、绘本等资源</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon course-icon">
              <i class="el-icon-notebook-1"></i>
            </div>
            <div class="stats-info">
              <h3>{{ stats.courseCount }}</h3>
              <p>课程总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon unit-icon">
              <i class="el-icon-folder"></i>
            </div>
            <div class="stats-info">
              <h3>{{ stats.unitCount }}</h3>
              <p>单元总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon letter-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stats-info">
              <h3>{{ stats.letterCount }}</h3>
              <p>音素总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon word-icon">
              <i class="el-icon-edit-outline"></i>
            </div>
            <div class="stats-info">
              <h3>{{ stats.wordCount }}</h3>
              <p>例词总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 课程列表 -->
    <el-card class="course-list-card">
      <div slot="header" class="card-header">
        <span>课程列表</span>
        <el-button type="primary" size="small" @click="$router.push('/phonics/courses')">
          管理课程
        </el-button>
      </div>
      
      <el-table :data="courses" v-loading="loading">
        <el-table-column prop="courseCode" label="课程编码" width="120"/>
        <el-table-column prop="courseNameZh" label="中文名称" width="150"/>
        <el-table-column prop="courseNameEn" label="英文名称" width="200"/>
        <el-table-column prop="unitCount" label="单元数量" width="100"/>
        <el-table-column prop="letterCount" label="音素数量" width="100"/>
        <el-table-column prop="wordCount" label="例词数量" width="100"/>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              @click="viewCourse(scope.row)"
            >
              查看详情
            </el-button>
            <el-button 
              size="mini" 
              type="primary"
              @click="manageCourse(scope.row)"
            >
              管理内容
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { courseApi } from '@/api/phonics/course'

export default {
  name: 'PhonicsDashboard',
  data() {
    return {
      loading: false,
      stats: {
        courseCount: 6,
        unitCount: 114,
        letterCount: 114,
        wordCount: 456
      },
      courses: []
    }
  },
  created() {
    this.fetchCourses()
  },
  methods: {
    async fetchCourses() {
      this.loading = true
      try {
        const response = await courseApi.getCourses()
        this.courses = response.data || []
      } catch (error) {
        this.$message.error('获取课程列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    viewCourse(course) {
      this.$router.push(`/phonics/course/${course.id}/units`)
    },
    
    manageCourse(course) {
      this.$router.push(`/phonics/course/${course.id}/units`)
    }
  }
}
</script>

<style lang="scss" scoped>
.phonics-dashboard {
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    font-size: 14px;
  }
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  .stats-content {
    display: flex;
    align-items: center;
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 24px;
        color: white;
      }
      
      &.course-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.unit-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.letter-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.word-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stats-info {
      h3 {
        margin: 0;
        font-size: 28px;
        font-weight: bold;
        color: #303133;
      }
      
      p {
        margin: 5px 0 0 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.course-list-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
