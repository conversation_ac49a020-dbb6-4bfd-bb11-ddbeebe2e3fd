import api from './index'

// 例词管理接口
export const wordApi = {
  // 获取音素下的例词列表
  getWordsByLetter(letterId, params = {}) {
    return api.get(`/letters/${letterId}/words`, {
      pageNum: 1,
      pageSize: 50,
      keyword: '',
      ...params
    })
  },

  // 获取例词详情
  getWordDetail(id) {
    return api.get(`/words/${id}`)
  },

  // 创建例词
  createWord(letterId, data) {
    return api.post(`/letters/${letterId}/words`, {
      word: '',
      phonetic: '',
      translation: '',
      fullSoundUrl: '',
      initialSoundUrl: '',
      rhymeSoundUrl: '',
      imageUrl: '',
      sequence: 0,
      ...data
    })
  },

  // 更新例词
  updateWord(id, data) {
    return api.put(`/words/${id}`, data)
  },

  // 删除例词
  deleteWord(id) {
    return api.delete(`/words/${id}`)
  },

  // 批量更新例词
  batchUpdateWords(words) {
    return api.post('/words/batch-update', { words })
  }
}
