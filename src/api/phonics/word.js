import { phonicsRequest } from './index'

// 例词管理接口 - 按照文档中的RESTful接口规范
export const wordApi = {
  // 获取音素下的例词列表
  getWordsByLetter(letterId, params = {}) {
    return phonicsRequest.get(`/letters/${letterId}/words`, params)
  },

  // 获取例词详情
  getWordDetail(id) {
    return phonicsRequest.get(`/words/${id}`)
  },

  // 创建例词
  createWord(letterId, data) {
    const wordData = {
      word: '',
      phonetic: '',
      translation: '',
      fullSoundUrl: '',
      initialSoundUrl: '',
      rhymeSoundUrl: '',
      imageUrl: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.post(`/letters/${letterId}/words`, wordData)
  },

  // 更新例词
  updateWord(id, data) {
    return phonicsRequest.put(`/words/${id}`, data)
  },

  // 删除例词
  deleteWord(id) {
    return phonicsRequest.delete(`/words/${id}`)
  },

  // 批量更新例词（使用Promise.all实现）
  async batchUpdateWords(words) {
    const promises = words.map(word =>
      phonicsRequest.put(`/words/${word.id}`, word)
    )
    return Promise.all(promises)
  }
}
