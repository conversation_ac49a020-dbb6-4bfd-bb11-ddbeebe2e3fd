import { phonicsRequest } from './index'

// 例词管理接口
export const wordApi = {
  // 获取音素下的例词列表
  getWordsByLetter(letterId, params = {}) {
    const requestParams = {
      letterId,
      pageNum: 1,
      pageSize: 50,
      keyword: '',
      ...params
    }
    return phonicsRequest.postForm('/words/list', requestParams)
  },

  // 获取例词详情
  getWordDetail(id) {
    return phonicsRequest.postForm('/words/detail', { id })
  },

  // 创建例词
  createWord(letterId, data) {
    const wordData = {
      letterId,
      word: '',
      phonetic: '',
      translation: '',
      fullSoundUrl: '',
      initialSoundUrl: '',
      rhymeSoundUrl: '',
      imageUrl: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.postForm('/words/create', wordData)
  },

  // 更新例词
  updateWord(id, data) {
    return phonicsRequest.postForm('/words/update', { id, ...data })
  },

  // 删除例词
  deleteWord(id) {
    return phonicsRequest.postForm('/words/delete', { id })
  },

  // 批量更新例词
  batchUpdateWords(words) {
    return phonicsRequest.postForm('/words/batch-update', { words })
  }
}
