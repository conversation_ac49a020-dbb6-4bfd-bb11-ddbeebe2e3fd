// 临时Mock数据，用于测试功能
// 基于import_phonics_data.sql中的真实数据结构

export const mockCourses = [
  {
    id: 1,
    courseCode: 'C1',
    courseNameZh: '辅音',
    courseNameEn: 'Consonants',
    description: '学习基础辅音字母的发音和拼读',
    sequence: 1,
    unitCount: 21,
    letterCount: 21,
    wordCount: 84
  },
  {
    id: 2,
    courseCode: 'C2',
    courseNameZh: '元音1',
    courseNameEn: 'Vowels 1',
    description: '学习基础元音字母的发音和拼读',
    sequence: 2,
    unitCount: 15,
    letterCount: 15,
    wordCount: 60
  },
  {
    id: 3,
    courseCode: 'C3',
    courseNameZh: '元音2',
    courseNameEn: 'Vowels 2',
    description: '学习进阶元音组合的发音和拼读',
    sequence: 3,
    unitCount: 15,
    letterCount: 15,
    wordCount: 60
  },
  {
    id: 4,
    courseCode: 'C4',
    courseNameZh: '辅音连缀',
    courseNameEn: 'Consonant Blends',
    description: '学习辅音连缀的发音和拼读',
    sequence: 4,
    unitCount: 20,
    letterCount: 20,
    wordCount: 80
  },
  {
    id: 5,
    courseCode: 'C5',
    courseNameZh: '字母组合1',
    courseNameEn: 'Letter Combinations 1',
    description: '学习常见字母组合的发音和拼读',
    sequence: 5,
    unitCount: 25,
    letterCount: 25,
    wordCount: 100
  },
  {
    id: 6,
    courseCode: 'C6',
    courseNameZh: '字母组合2',
    courseNameEn: 'Letter Combinations 2',
    description: '学习高级字母组合的发音和拼读',
    sequence: 6,
    unitCount: 18,
    letterCount: 18,
    wordCount: 72
  }
]

export const mockUnits = {
  1: [ // C1 辅音
    { id: 1, courseId: 1, unitCode: 'U1', unitName: 'b', phonemeType: 'SINGLE_LETTER', description: '学习字母b的发音', sequence: 1, letterCount: 1, wordCount: 4 },
    { id: 2, courseId: 1, unitCode: 'U2', unitName: 'c', phonemeType: 'SINGLE_LETTER', description: '学习字母c的发音', sequence: 2, letterCount: 1, wordCount: 4 },
    { id: 3, courseId: 1, unitCode: 'U3', unitName: 'd', phonemeType: 'SINGLE_LETTER', description: '学习字母d的发音', sequence: 3, letterCount: 1, wordCount: 4 },
    { id: 4, courseId: 1, unitCode: 'U4', unitName: 'f', phonemeType: 'SINGLE_LETTER', description: '学习字母f的发音', sequence: 4, letterCount: 1, wordCount: 4 },
    { id: 5, courseId: 1, unitCode: 'U5', unitName: 'g', phonemeType: 'SINGLE_LETTER', description: '学习字母g的发音', sequence: 5, letterCount: 1, wordCount: 4 }
  ],
  2: [ // C2 元音1
    { id: 22, courseId: 2, unitCode: 'U22', unitName: 'a', phonemeType: 'SINGLE_LETTER', description: '学习字母a的发音', sequence: 1, letterCount: 1, wordCount: 4 },
    { id: 23, courseId: 2, unitCode: 'U23', unitName: 'ad', phonemeType: 'VOWEL_COMBINATION', description: '学习ad组合的发音', sequence: 2, letterCount: 1, wordCount: 4 },
    { id: 24, courseId: 2, unitCode: 'U24', unitName: 'am', phonemeType: 'VOWEL_COMBINATION', description: '学习am组合的发音', sequence: 3, letterCount: 1, wordCount: 4 }
  ]
}

export const mockLetters = {
  1: [{ id: 1, unitId: 1, letter: 'b', ipa: '/b/', isCombination: false, soundUrl: '', sequence: 1, wordCount: 4 }],
  2: [{ id: 2, unitId: 2, letter: 'c', ipa: '/k/', isCombination: false, soundUrl: '', sequence: 1, wordCount: 4 }],
  22: [{ id: 22, unitId: 22, letter: 'a', ipa: '/æ/', isCombination: false, soundUrl: '', sequence: 1, wordCount: 4 }]
}

export const mockWords = {
  1: [ // 字母b的例词
    { id: 1, letterId: 1, word: 'ball', phonetic: '/bɔːl/', translation: '球', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 1 },
    { id: 2, letterId: 1, word: 'bat', phonetic: '/bæt/', translation: '蝙蝠', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 2 },
    { id: 3, letterId: 1, word: 'book', phonetic: '/bʊk/', translation: '书', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 3 },
    { id: 4, letterId: 1, word: 'bus', phonetic: '/bʌs/', translation: '公交车', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 4 }
  ],
  2: [ // 字母c的例词
    { id: 5, letterId: 2, word: 'cat', phonetic: '/kæt/', translation: '猫', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 1 },
    { id: 6, letterId: 2, word: 'car', phonetic: '/kɑːr/', translation: '汽车', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 2 },
    { id: 7, letterId: 2, word: 'cup', phonetic: '/kʌp/', translation: '杯子', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 3 },
    { id: 8, letterId: 2, word: 'cake', phonetic: '/keɪk/', translation: '蛋糕', fullSoundUrl: '', initialSoundUrl: '', rhymeSoundUrl: '', imageUrl: '', sequence: 4 }
  ]
}

// Mock API响应格式
export const createMockResponse = (data, total = null) => {
  if (total !== null) {
    return {
      code: 200,
      message: 'success',
      data: {
        list: data,
        total: total,
        pageNum: 1,
        pageSize: 20
      },
      success: true
    }
  } else {
    return {
      code: 200,
      message: 'success',
      data: data,
      success: true
    }
  }
}
