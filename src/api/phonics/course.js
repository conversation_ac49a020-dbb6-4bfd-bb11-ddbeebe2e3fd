import { phonicsRequest } from './index'
import { mockCourses, createMockResponse } from './mock'

// 课程管理接口
export const courseApi = {
  // 获取课程列表
  getCourses(params = {}) {
    // 临时使用mock数据
    if (process.env.NODE_ENV === 'development') {
      return Promise.resolve(createMockResponse(mockCourses, mockCourses.length))
    }

    const requestParams = {
      pageNum: 1,
      pageSize: 20,
      keyword: '',
      ...params
    }
    return phonicsRequest.postForm('/courses/list', requestParams)
  },

  // 获取课程详情
  getCourseDetail(id) {
    return phonicsRequest.postForm('/courses/detail', { id })
  },

  // 获取课程统计信息
  getCourseStats(id) {
    return phonicsRequest.postForm('/courses/stats', { id })
  },

  // 创建课程
  createCourse(data) {
    const courseData = {
      courseCode: '',
      courseNameZh: '',
      courseNameEn: '',
      description: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.postForm('/courses/create', courseData)
  },

  // 更新课程
  updateCourse(id, data) {
    return phonicsRequest.postForm('/courses/update', { id, ...data })
  },

  // 删除课程
  deleteCourse(id) {
    return phonicsRequest.postForm('/courses/delete', { id })
  }
}
