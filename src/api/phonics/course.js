import api from './index'

// 课程管理接口
export const courseApi = {
  // 获取课程列表
  getCourses(params = {}) {
    return api.get('/courses', {
      pageNum: 1,
      pageSize: 20,
      keyword: '',
      ...params
    })
  },

  // 获取课程详情
  getCourseDetail(id) {
    return api.get(`/courses/${id}`)
  },

  // 获取课程统计信息
  getCourseStats(id) {
    return api.get(`/courses/${id}/stats`)
  },

  // 创建课程
  createCourse(data) {
    return api.post('/courses', {
      courseCode: '',
      courseNameZh: '',
      courseNameEn: '',
      description: '',
      sequence: 0,
      ...data
    })
  },

  // 更新课程
  updateCourse(id, data) {
    return api.put(`/courses/${id}`, data)
  },

  // 删除课程
  deleteCourse(id) {
    return api.delete(`/courses/${id}`)
  }
}
