import { phonicsRequest } from './index'

// 课程管理接口 - 按照文档中的RESTful接口规范
export const courseApi = {
  // 获取课程列表
  getCourses(params = {}) {
    return phonicsRequest.get('/courses', params)
  },

  // 获取课程详情
  getCourseDetail(id) {
    return phonicsRequest.get(`/courses/${id}`)
  },

  // 获取课程统计信息（如果后端有提供）
  getCourseStats(id) {
    return phonicsRequest.get(`/courses/${id}/stats`)
  },

  // 创建课程
  createCourse(data) {
    const courseData = {
      courseCode: '',
      courseNameZh: '',
      courseNameEn: '',
      description: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.post('/courses', courseData)
  },

  // 更新课程
  updateCourse(id, data) {
    return phonicsRequest.put(`/courses/${id}`, data)
  },

  // 删除课程
  deleteCourse(id) {
    return phonicsRequest.delete(`/courses/${id}`)
  },

  // 发布课程
  publishCourse(id) {
    return phonicsRequest.post(`/courses/${id}/publish`)
  }
}
