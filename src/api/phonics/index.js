import { post_formData, myAxios } from '@/utils/axios'

// 自然拼读API基础路径
const API_BASE = '/kid-phonics'

// 统一请求封装
export const phonicsRequest = {
  // GET请求
  get: (url, params) => myAxios.get(`${API_BASE}${url}`, { params }),
  // POST请求 (JSON)
  post: (url, data) => myAxios.post(`${API_BASE}${url}`, data),
  // POST请求 (FormData)
  postForm: (url, data) => post_formData(`${API_BASE}${url}`, data),
  // PUT请求
  put: (url, data) => myAxios.put(`${API_BASE}${url}`, data),
  // DELETE请求
  delete: (url, params) => myAxios.delete(`${API_BASE}${url}`, { params })
}
