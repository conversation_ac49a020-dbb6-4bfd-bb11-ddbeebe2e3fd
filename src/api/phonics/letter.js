import { phonicsRequest } from './index'

// 音素管理接口 - 按照文档中的RESTful接口规范
export const letterApi = {
  // 获取单元下的音素列表
  getLettersByUnit(unitId, params = {}) {
    return phonicsRequest.get(`/units/${unitId}/letters`, params)
  },

  // 获取音素详情
  getLetterDetail(id) {
    return phonicsRequest.get(`/letters/${id}`)
  },

  // 创建音素
  createLetter(unitId, data) {
    const letterData = {
      letter: '',
      ipa: '',
      isCombination: false,
      soundUrl: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.post(`/units/${unitId}/letters`, letterData)
  },

  // 更新音素
  updateLetter(id, data) {
    return phonicsRequest.put(`/letters/${id}`, data)
  },

  // 删除音素
  deleteLetter(id) {
    return phonicsRequest.delete(`/letters/${id}`)
  }
}
