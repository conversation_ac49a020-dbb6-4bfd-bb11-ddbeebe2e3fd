import api from './index'

// 音素管理接口
export const letterApi = {
  // 获取单元下的音素列表
  getLettersByUnit(unitId, params = {}) {
    return api.get(`/units/${unitId}/letters`, {
      pageNum: 1,
      pageSize: 50,
      ...params
    })
  },

  // 获取音素详情
  getLetterDetail(id) {
    return api.get(`/letters/${id}`)
  },

  // 创建音素
  createLetter(unitId, data) {
    return api.post(`/units/${unitId}/letters`, {
      letter: '',
      ipa: '',
      isCombination: false,
      soundUrl: '',
      sequence: 0,
      ...data
    })
  },

  // 更新音素
  updateLetter(id, data) {
    return api.put(`/letters/${id}`, data)
  },

  // 删除音素
  deleteLetter(id) {
    return api.delete(`/letters/${id}`)
  }
}
