import { phonicsRequest } from './index'

// 音素管理接口
export const letterApi = {
  // 获取单元下的音素列表
  getLettersByUnit(unitId, params = {}) {
    const requestParams = {
      unitId,
      pageNum: 1,
      pageSize: 50,
      ...params
    }
    return phonicsRequest.postForm('/letters/list', requestParams)
  },

  // 获取音素详情
  getLetterDetail(id) {
    return phonicsRequest.postForm('/letters/detail', { id })
  },

  // 创建音素
  createLetter(unitId, data) {
    const letterData = {
      unitId,
      letter: '',
      ipa: '',
      isCombination: false,
      soundUrl: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.postForm('/letters/create', letterData)
  },

  // 更新音素
  updateLetter(id, data) {
    return phonicsRequest.postForm('/letters/update', { id, ...data })
  },

  // 删除音素
  deleteLetter(id) {
    return phonicsRequest.postForm('/letters/delete', { id })
  }
}
