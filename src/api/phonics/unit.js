import { phonicsRequest } from './index'

// 单元管理接口
export const unitApi = {
  // 获取课程下的单元列表
  getUnitsByCourse(courseId, params = {}) {
    const requestParams = {
      courseId,
      pageNum: 1,
      pageSize: 50,
      ...params
    }
    return phonicsRequest.postForm('/units/list', requestParams)
  },

  // 获取单元详情
  getUnitDetail(id) {
    return phonicsRequest.postForm('/units/detail', { id })
  },

  // 获取单元详情（包含资源统计）
  getUnitWithStats(id) {
    return phonicsRequest.postForm('/units/detail-with-stats', { id })
  },

  // 创建单元
  createUnit(courseId, data) {
    const unitData = {
      courseId,
      unitCode: '',
      unitName: '',
      phonemeType: 'SINGLE_LETTER',
      description: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.postForm('/units/create', unitData)
  },

  // 更新单元
  updateUnit(id, data) {
    return phonicsRequest.postForm('/units/update', { id, ...data })
  },

  // 删除单元
  deleteUnit(id) {
    return phonicsRequest.postForm('/units/delete', { id })
  }
}
