import api from './index'

// 单元管理接口
export const unitApi = {
  // 获取课程下的单元列表
  getUnitsByCourse(courseId, params = {}) {
    return api.get(`/courses/${courseId}/units`, {
      pageNum: 1,
      pageSize: 50,
      ...params
    })
  },

  // 获取单元详情
  getUnitDetail(id) {
    return api.get(`/units/${id}`)
  },

  // 获取单元详情（包含资源统计）
  getUnitWithStats(id) {
    return api.get(`/units/${id}/with-stats`)
  },

  // 创建单元
  createUnit(courseId, data) {
    return api.post(`/courses/${courseId}/units`, {
      unitCode: '',
      unitName: '',
      phonemeType: 'SINGLE_LETTER',
      description: '',
      sequence: 0,
      ...data
    })
  },

  // 更新单元
  updateUnit(id, data) {
    return api.put(`/units/${id}`, data)
  },

  // 删除单元
  deleteUnit(id) {
    return api.delete(`/units/${id}`)
  }
}
