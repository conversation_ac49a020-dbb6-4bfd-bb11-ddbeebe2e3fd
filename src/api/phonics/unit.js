import { phonicsRequest } from './index'

// 单元管理接口 - 按照文档中的RESTful接口规范
export const unitApi = {
  // 获取课程下的单元列表
  getUnitsByCourse(courseId, params = {}) {
    return phonicsRequest.get(`/courses/${courseId}/units`, params)
  },

  // 获取单元详情
  getUnitDetail(id) {
    return phonicsRequest.get(`/units/${id}`)
  },

  // 获取单元详情（包含资源统计）
  getUnitWithStats(id) {
    return phonicsRequest.get(`/units/${id}/with-resources`)
  },

  // 创建单元
  createUnit(courseId, data) {
    const unitData = {
      unitCode: '',
      unitName: '',
      phonemeType: 'SINGLE_LETTER',
      description: '',
      sequence: 0,
      ...data
    }
    return phonicsRequest.post(`/courses/${courseId}/units`, unitData)
  },

  // 更新单元
  updateUnit(id, data) {
    return phonicsRequest.put(`/units/${id}`, data)
  },

  // 删除单元
  deleteUnit(id) {
    return phonicsRequest.delete(`/units/${id}`)
  }
}
