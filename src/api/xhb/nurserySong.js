import { post_formData, post_json, get, post_obj_array } from '@/utils/axios'

/**
 * 儿歌资源管理 API 接口
 */

// 获取儿歌列表
export function getNurserySongList(params) {
  return post_json('/nursery-song/list', params)
}

// 获取儿歌详情
export function getNurserySongDetail(songId) {
  return get('/nursery-song/detail', { id: songId })
}

// 添加儿歌
export function addNurserySong(data) {
  return post_json('/nursery-song/add', data)
}

// 更新儿歌
export function updateNurserySong(data) {
  return post_json('/nursery-song/update', data)
}

// 删除儿歌
export function deleteNurserySong(id) {
  return post_json('/nursery-song/delete?id=' + id)
}

// 更新儿歌状态
export function updateNurserySongStatus(id, status) {
  return post_json('/nursery-song/update-status?id=' + id + '&status=' + status)
}

// 批量更新儿歌状态
export function batchUpdateNurserySongStatus(ids, status) {
  const params = new URLSearchParams()
  ids.forEach(id => params.append('ids', id))
  params.append('status', status)
  return post_json('/nursery-song/batch-update-status?' + params.toString())
}

// 单文件上传
export function uploadNurserySongFile(formData) {
  return post_formData('/nursery-song/upload', formData)
}

// 批量文件上传
export function batchUploadNurserySongFiles(formData) {
  return post_formData('/nursery-song/upload/batch', formData)
}

// 处理儿歌资源（解析字幕）
export function processNurserySongResources(songId) {
  return post_json('/nursery-song/process?songId=' + songId)
}

// 批量处理儿歌资源
export function batchProcessNurserySongs(songIds) {
  const params = new URLSearchParams()
  songIds.forEach(id => params.append('songIds', id))
  return post_json('/nursery-song/batch-process?' + params.toString())
}

// 上传并解析字幕文件
export function uploadAndParseSubtitle(formData) {
  return post_formData('/nursery-song/subtitle/upload-parse', formData)
}

// 获取句子列表
export function getNurserySongSentences(params) {
  return get('/nursery-song/sentences', params)
}

// 更新句子
export function updateNurserySongSentence(data) {
  return post_json('/nursery-song/sentence/update', data)
}

// 删除句子
export function deleteNurserySongSentence(sentenceId) {
  return post_json('/nursery-song/sentence/delete?sentenceId=' + sentenceId)
}

// 验证儿歌数据
export function validateNurserySongData(data) {
  return post_json('/nursery-song/validate', data)
}

// 获取儿歌统计信息
export function getNurserySongStats() {
  return get('/nursery-song/stats')
}

// 导出儿歌数据
export function exportNurserySongData(params) {
  return get('/nursery-song/export', params)
}

// 获取难度等级选项
export function getDifficultyLevels() {
  return [
    { value: 1, label: '简单' },
    { value: 2, label: '较简单' },
    { value: 3, label: '中等' },
    { value: 4, label: '较难' },
    { value: 5, label: '困难' }
  ]
}

// 获取状态选项
export function getStatusOptions() {
  return [
    { value: 0, label: '下架', type: 'danger' },
    { value: 1, label: '上架', type: 'success' },
    { value: 2, label: '待审核', type: 'warning' }
  ]
}

// 获取文件类型选项
export function getFileTypeOptions() {
  return [
    { value: 1, label: '视频文件', accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv' },
    { value: 2, label: '原唱音频', accept: '.mp3,.wav,.aac,.flac,.ogg,.m4a' },
    { value: 3, label: '伴奏音频', accept: '.mp3,.wav,.aac,.flac,.ogg,.m4a' },
    { value: 4, label: '字幕文件', accept: '.srt,.vtt,.json,.ass,.ssa' }
  ]
}
