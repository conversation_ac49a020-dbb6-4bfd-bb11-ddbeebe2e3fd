import {post_formData,myAxios} from '@/utils/axios';
export function insertVersion (params) {
  return post_formData('/resourceVersion/insertVersion', params)
}
export function getVersionList (params) {
  return post_formData('/resourceVersion/getVersionList', params)
}
export function getVersionTypeList (params) {
  return post_formData('/resourceVersion/getVersionTypeList', params)
}
export function updateVersionById (params) {
  return post_formData('/resourceVersion/updateVersionById', params)
}
export function updateVersionDisplayOrder (params) {
  return post_formData('/resourceVersion/updateVersionDisplayOrder', params)
}
export function saveGrammarCard (params) {
    return myAxios.post('/newGrammarManager/saveGrammarCard', params)
}
export function insertVersionStage (params) {
  return post_formData('/resourceVersion/insertVersionStage', params)
}