import {post_formData, get_blob, post_json} from '@/utils/axios';

/**
 * 获取课程列表
 * params stage 学段id
 * params versionId 版本id
 * params pageStart 页码
 * params pageSize 条数
 *
 * return 课程列表
 */

export function getCourseList(params) {
  return post_formData('/resourceCourse/getCourseList', params)
}

/**
 * 点击修改后获取回显的课程信息
 * param id 课程id
 *
 * returns
 */
export function getCourseById(params) {
  return post_formData('/resourceCourse/getCourseById', params)
}

/**
 * 添加课程信息
 * param versionId 版本id
 * param nameEn 英文名
 * param nameCn 中文名
 * param stage 学段
 * param grade 年级
 *
 * returns
 */
export function insertCourse(params) {
  return post_formData('/resourceCourse/insertCourse', params)
}

/**
 * 保存修改后课程信息
 * param nameEn 英文名
 * param nameCn 中文名
 * param grade 年级
 * param id 课程id
 *
 * returns
 */
export function updateCourse(params) {
  return post_formData('/resourceCourse/updateCourse', params)
}

/**
 * 导入单元

 *
 * returns
 */
export function insertUnits(params) {
  return post_formData('/resourceUnit/insertUnits', params)
}

/**
 * 导入词汇资源
 *
 * returns
 */
export function importWord(params) {
  return post_formData('/import/word', params)
}

/**
 * 导入词汇资源
 *
 * returns
 */
export function importWordPhrase(params) {
  return post_formData('/import/wordPhrase', params)
}

/**
 * 导入句子资源
 *
 * returns
 */
export function importSentence(params) {
  return post_formData('/import/sentence', params)
}

/**
 * 导入课文资源
 *
 * returns
 */
export function importArticle(params) {
  return post_formData('/import/article', params)
}

export function importLeXue(params) {
  return post_formData('readExpand/article/importExcel', params)
}

/**
 * 课文列表（文章管理）
 *
 * returns
 */
export function articleList(params) {
  return post_formData('/article/list', params)
}

export function leXueList(params) {
  return post_formData('/readExpand/article/listByCourseId', params)
}

export function leXueReadContent(params) {
  return post_formData('/readExpand/article/edit', params)
}

/**
 * 文章-音频批量上传
 *
 * returns
 */
export function unloadArticleSoundFile(params) {
  return post_formData('/upload/articleSoundFile', params)
}

/**
 * 干扰项编辑-列表展示
 *
 * returns
 */
export function wordDisturbList(params) {
  return post_formData('/wordDisturb/list', params)
}

/**
 * 干扰项编辑-单词列表
 *
 * returns
 */
export function wordEditShowWordList(params) {
  return post_formData('/wordEdit/showWordList', params)
}

/**
 * 干扰项编辑-单词列表-选择
 *
 * returns
 */
export function wordDisturbChange(params) {
  return post_formData('/wordDisturb/change', params)
}

/**
 * 用词资源列表
 * @param params
 * @returns {AxiosPromise}
 */
export function wordUseResourceList(params) {
  return post_formData('/wordEdit/getWordUseList', params)
}

/**
 * 用词资源修改
 */
export function wordEditSaveWordUseResource(params) {
  return post_json('/wordEdit/updateWordUse', params)
}

/**
 * 用词资源上线
 */
export function wordDisturbSave(params) {
  return post_json('/wordEdit/updateWordUseOnline', params)
}

/**
 * 版本发布
 *
 * returns
 */
export function publishVersion(params) {
  return post_formData('/publish/version', params)
}

/**
 * 课程发布
 *
 * returns
 */
export function publishCourse(params) {
  return post_formData('/publish/course', params)
}

/**
 * 语法管理-列表
 *
 * returns
 */
export function newGrammarManagerGetUnitList(params) {
  return post_formData('/newGrammarManager/getUnitList', params)
}

/**
 * 语法管理-添加
 *
 * returns
 */
export function newGrammarManagerAddGrammarUnit(params) {
  return post_formData('/newGrammarManager/addGrammarUnit', params)
}


/**
 * 句子-匹配句子成分
 *
 * returns
 */
export function matchSentenceElement(params) {
  return post_formData('/wordEdit/matchSentenceElement', params)
}

/**
 * 习题 列表
 *
 * returns
 */
export function questionGetUnitList(params) {
  return post_formData('/question/getUnitList', params)
}

/**
 * 习题  学段列表
 *
 * returns
 */
export function questionCourseAllParentList(params) {
  return post_formData('/question/course/allParentList', params)
}

/**
 * 习题  版本列表
 *
 * returns
 */
export function questionCourseGetVersionList(params) {
  return post_formData('/question/course/getVersionList', params)
}

/**
 * 习题 课程 列表
 *
 * returns
 */
export function questionCourseGetCourseList(params) {
  return post_formData('/question/course/getCourseList', params)
}

/**
 * 习题  单元列表
 *
 * returns
 */
export function questionCourseGetUnitList(params) {
  return post_formData('/question/course/getUnitList', params)
}

/**
 * 习题  添加习题
 *
 * returns
 */
export function contentWordInsertContentQuestion(params) {
  return post_formData('/contentWord/insertContentQuestion', params)
}


/**
 * 习题  删除习题
 *
 * returns
 */
export function contentWordDelContentWord(params) {
  return post_formData('/contentWord/delContentWord', params)
}


/**
 * 单词缩写管理 添加
 *
 * returns
 */
export function wordAbbrAdd(params) {
  return post_formData('/wordAbbr/add', params)
}

/**
 * 单词缩写管理 列表
 *
 * returns
 */
export function wordAbbrList(params) {
  return post_formData('/wordAbbr/list', params)
}

/**
 * 单词缩写管理 修改
 *
 * returns
 */
export function wordAbbrUpdate(params) {
  return post_formData('/wordAbbr/update', params)
}


/**
 * 语法 复制
 *
 * returns
 */
export function saveCopiedUnitIdArr(params) {
  return post_formData('/newGrammarManager/saveCopiedUnitIdArr', params)
}

/**
 * 语法 粘贴
 *
 * returns
 */
export function pasteUnit(params) {
  return post_formData('/newGrammarManager/pasteUnit', params)
}

/**
 * 导出课程的所有文章
 *
 * returns
 */
export function articleExport(params) {
  return get_blob('/article/export', params)
}

/**
 * 配置小升初中考
 *
 * returns
 */
export function insertCourseStage(params) {
  return post_formData('/resourceCourse/insertCourseStage', params)
}
///修改知识点数量
export function updateUnitKnowledgeCount(params) {
  return post_formData('/newGrammarManager/updateUnitKnowledgeCount', params)
}
