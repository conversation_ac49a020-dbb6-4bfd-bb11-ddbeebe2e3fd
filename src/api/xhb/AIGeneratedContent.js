import { get,post_json ,post,post_formData} from '@/utils/axios';


// ai内容列表
export function getAiGenerateList(params) {
    return get('/aiGenerate/list', params)
}

//ai重新生成
export function getAiGenerateExecute(params) {
    return post_formData('/aiGenerate/execute', params)
}

//ai重新生成词汇
export function getAiGenerateExecuteWord(params) {
    return post_formData('/aiGenerate/executeWord', params)
}


//忽略处理
export function getAiGenerateHandleIgnore(params) {
    return post_formData('/aiGenerate/handleIgnore', params)
}

//加载助记反馈缓存
export function getAiGenerateReload(params) {
    return post_formData('/aiGenerate/reload', params)
}

//修改
export function getAiGenerateUpdate(params) {
    return post_json('/aiGenerate/update', params)
}
