"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || "vue Element Admin"; // page title

const port = process.env.port || process.env.npm_config_port || 9528; // dev port

module.exports = {
  publicPath: process.env.PUBLIC_PATH,
  outputDir: process.env.outputDir,
  assetsDir: "static",
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    public:
      require("os").networkInterfaces()[
        Object.keys(require("os").networkInterfaces())[0]
      ][1].address +
      ":" +
      port, // 设置访问ip端
    port: port,
    open: true,
    disableHostCheck: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    // before: require('./mock/mock-server.js'),解开注释会导致json格式接口报错
    proxy: {
      "/api": {
        //  target: 'http://*************:8487',
            //target: 'http://*************:8487',//测试
        //  target: 'http://************:8083',//振博
        //  target: "http://*************:8083", //宁虎本地
        //  target: 'http://*************:8083',//伟亚本地
        // target: 'http://*************:8487',
         //  target: 'http://*************:8487',
        target: 'http://127.0.0.1:8083',//测试
        // target: "http://*************:8083",//宁虎本地
        // target:'http://*************:8083',//伟亚本地
        changeOrigin: true,
        pathRewrite: {
          "^/api": "", // 用‘/api’代替target里面的地址，比如调用'http://localhost:8090/users'，直接写‘/api/users’即可
        },
      },

      "/kwapi": {
        // target: 'http://**************:8380',
        // target: 'http://**************:8222',
        // target: 'https://test.hssenglish.com:8877/',
        // target: 'https://beta.hssenglish.com:8877',
        // target: 'http://*************:8083/redbook-resource',
        target: "http://localhost:8083",
        changeOrigin: true,
        pathRewrite: {
          "^/kwapi": "", // 用‘/api’代替target里面的地址，比如调用'http://localhost:8090/users'，直接写‘/api/users’即可
        },
      },
    },
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin("preload").tap(() => [
      {
        rel: "preload",
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: "initial",
      },
    ]);

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete("prefetch");

    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    config.when(process.env.NODE_ENV !== "development", (config) => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // only package third parties that are initially dependent
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk("single");
    });
  },
};
