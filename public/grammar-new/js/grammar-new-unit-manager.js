/**---------------------节管理-------------------------*/
var courseId = Request["courseId"];
$(function(){
	loadCourseName();
	getUnitList();
});

/**
 * 加载章名
 */
var courseName;
function loadCourseName(){
	$.post("/newGrammarManager/getCourseFullNameById",{"courseId": courseId},
			function(data){
			$("#courseName").text(data.gradeName+"-"+data.versionName+"-"+data.courseName);
			$("#unit-div .courseName").text(data.courseName);
			$("#unit-div .versionName").text(data.versionName);
			$("#unit-div .gradeName").text(data.gradeName);
	},"json");
}

/**
 * 获取节列表
 */
var unitList;
function getUnitList(){
	$.post("/newGrammarManager/getUnitList",{"courseId":courseId},function(data){
		unitList = data;
		var _html="";
		if(data==null || data.length==0){
			_html = "<tr><td colspan='"+$("#dataTable thead th").length+"' style='color: red;'>没有添加节哦~</td></tr>"
		}else{
			$(data).each(function(i, item){
				_html += "<tr>";
				_html += "<td>"+(i+1)+"</td>";
				_html += "<td>"+item.name+"</td>";
				if(userType==2){
					_html += "<td>";
					if(i==0){
						_html += "<a style='margin-left: 23px;' href='javascript:adjustUnit("+i+",\"down\")'>↓</a>"; 
					}else if(i==data.length-1){
						_html += "<a style='margin-right: 23px;' href='javascript:adjustUnit("+i+",\"up\")'>↑</a>";
					}else{
						_html += "<a href='javascript:adjustUnit("+i+",\"up\")'>↑</a>";
						_html += "<a style='margin-left: 10px;' href='javascript:adjustUnit("+i+",\"down\")'>↓</a>"; 
					}
					_html += "<a style='margin-left: 10px;' href='javascript:updateUnit("+i+")'>修改</a>";
//					_html += "<a style='margin-left: 10px;' href='javascript:copyUnit("+item.id+")'>复制</a>";
					_html += "<a style='margin-left: 10px;' href='javascript:deleteUnit("+item.id+")'>删除</a>";
					_html += "</td>";
				}
				_html += "<td>"+item.inteNum+"步</td>";
				_html += "<td><a style='margin-left: 10px;' href='javascript:previewCard("+item.id+")'>预览</a>";
				if(userType==2){
					_html += "<a style='margin-left: 10px;' href='javascript:editCard("+item.id+")'>编辑</a>";
				}else{
					_html += "<a style='margin-left: 10px;' href='javascript:cardDetail("+item.id+")'>查看</a>";
				}
				_html += "<a style='margin-left: 10px;' href='javascript:grammarTeach("+item.id+")'>教学</a>";
				_html += "<a style='margin-left: 10px;' href='javascript:learnCard("+item.id+")'>学习</a>";
				_html += "</td>";
				_html += "<td>"+item.questionNum+"题（"+item.questionNum1+"/"+item.questionNum2+"/"+item.questionNum3+"）</td>";
				_html += "<td><a style='margin-left: 10px;' href='javascript:managerQuestion("+item.id+")'>查看</a></td>";
				_html += "<td>"+new Date(item.modifyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
				_html += "</tr>";
			});
		}
		$("#dataTable tbody").html(_html);
	},"json");
}

/**
 * 添加节
 * @param order
 */
function addUnit(){
	var _html = "<div id='unit-dia'>"+$("#unit-div").html()+"</div>";
	var d = dialog({
		title: "添加节",
		content: _html,
		padding: "20px",
		width: "300px",
		okValue: "提交",
		ok: function(){
			var unitName = $.trim($("#unit-dia .unitName").val());
			if(unitName==null || unitName==""){
				alert("请输入节名！");
				return false;
			}
			var grammarUnit = new Object();
			grammarUnit.name = unitName;
			grammarUnit.courseId = courseId;
			var flag = false;
			$.ajax({
				 type: "POST",
		         url: "/newGrammarManager/addUnit",
		         async: false,
		         data: grammarUnit,
		         dataType: "json",
		         success: function(data){
		        	 if(data){
		        		 getUnitList();
		        	 }else{
		        		 alert("添加失败！");
		        	 }
		        	 flag = data;
		         }
			});
			return flag;
		}
	});
	d.showModal();
}

/**
 * 修改节
 * @param index
 */
function updateUnit(index){
	var oldUnit = unitList[index];
	var _html = "<div id='unit-dia'>"+$("#unit-div").html()+"</div>";
	var d = dialog({
		title: "修改节",
		content: _html,
		padding: "20px",
		width: "300px",
		cancelValue: "取消",
		cancel: true,
		okValue: "确认",
		ok: function(){
			var unitName = $.trim($("#unit-dia .unitName").val());
			if(unitName==null || unitName==""){
				alert("请输入节名！");
				return false;
			}else if(unitName==oldUnit){
				return true;
			}
			var grammarUnit = new Object();
			grammarUnit.name = unitName;
			grammarUnit.id = oldUnit.id;
			grammarUnit.courseId = courseId;
			var flag = false;
			$.ajax({
				 type: "POST",
		         url: "/newGrammarManager/updateUnit",
		         async: false,
		         data: grammarUnit,
		         dataType: "json",
		         success: function(data){
		        	 if(data){
						getUnitList();
					}else{
						alert("修改失败！");
					}
		        	flag = data;
		         }
			});
			return flag;
		}
	});
	//回显数据
	$("#unit-dia .unitName").val(oldUnit.name);
	d.showModal();
}

/**
 * 调整节顺序
 */
var srcOrder, desOrder;
function adjustUnit(index,direction){
	srcOrder = unitList[index].disporder;
	if(direction == "up"){
		desOrder = unitList[parseInt(index)-1].disporder;
	}else{
		desOrder = unitList[parseInt(index)+1].disporder;
	}
	
	$.post("/newGrammarManager/adjustUnit",
			{"courseId": courseId, "srcOrder": srcOrder, "desOrder": desOrder},
			function(data){
				if(data){
					getUnitList();
				}else{
					alert("调整失败！");
				}
			},"json");
}

/**
 * 删除节
 * @param unitId
 */
function deleteUnit(unitId){
	var d = dialog({
		title: "删除节",
		content: "删除节，该节下面的卡片信息也会一并删除。你确定要执行该操作吗？",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "确认",
		ok: function(){
			$.post("/newGrammarManager/deleteUnit",
					{"unitId": unitId},
					function(data){
						if(data){
							getUnitList();
						}else{
							alert("删除失败！");
						}
					},"json");
		}
	});
	d.showModal();
}

/**
 * 复制小节
 * @param unitId
 */
function copyUnit(unitId){
	var d = dialog({
		title: "复制节",
		content: "确定要复制该节即该节下的卡片和试题信息吗？",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "确认",
		ok: function(){
			$.post("/newGrammarManager/copyUnit",
					{"unitId": unitId},
					function(data){
						if(data){
							getUnitList();
						}else{
							alert("复制失败！");
						}
					},"json");
		}
	});
	d.showModal();
}


/**-------------------------------语法卡片管理----------------------------------*/
/**
 * 删除语法卡
 */
/*function cleanGrammarCard(){
	var d = dialog({
		title: "清空卡片",
		content: "确定要清空该语法卡片吗？",
		okValue: "确认",
		cancelValue: "我再想想",
		ok: function(){
			$.post("/newGrammarManager/cleanGrammarCard",
					{"unitId": unitId},
					function(data){
						if(data){
							alert("操作成功！");
							getCardAndQuestionInfo();
						}else{
							alert("操作失败！");
						}
					},"json");
		},
		cancel: true
	});
	d.showModal();
}*/


/**
 * 展示语法卡片
 * @param unitId
 */
function showGrammarCard(){
	if(parseInt($("#cardNum").text())<1){
		alert("没有卡片内容，不能查看！");
		return;
	}
	window.open("grammar-card-preview.html?unitId="+unitId);
}

/**
 * 编辑语法卡片
 * @param unitId
 */
function editGrammarCard(){
	window.open("grammar-new-card-edit.html?unitId="+unitId);
}


/**-------------------------语法卡片管理-----------------------------*/

function editCard(unitId){
	window.open("grammar-new-card-edit.html?unitId="+unitId);
}

function previewCard(unitId){
	window.open("grammar-new-card-preview.html?unitId="+unitId);
}

function cardDetail(unitId){
	window.open("error-detail.html?unitId="+unitId);
}

function grammarTeach(unitId){
	window.open("grammar-new-teach.html?unitId="+unitId);
}
function learnCard(unitId){
	window.open("interactive-learning.html?unitId="+unitId);
}

function managerQuestion(unitId){
	window.location.href = "grammar-new-train-manager.html?courseId="+courseId+"&unitId="+unitId;
}
/**
 * 返回
 */
function goBack(){
	window.history.back()
}
