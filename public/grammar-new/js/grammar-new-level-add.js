var courseId=Request["courseId"];
$(function(){
	$(".all-type").click(function(){
		$(this).children("input").prop("checked",true);
		$(this).siblings(".item-type:visible").children("input").prop("checked", false);
	});
	$(".item-type").click(function(){
		if($(this).children("input").prop("checked") && 
				$(this).siblings(".all-type").children("input").prop("checked")){
			$(this).siblings(".all-type").children("input").prop("checked", false);
		}else if(!($(this).siblings(".item-type").children("input").is(":checked"))){
			$(this).siblings(".all-type").children("input").prop("checked", true);
		}
	});
	$("#gradePhase :radio").change(function(){
		var gradePhase = $(this).val();
		var gradePhaseName = $(this).text();
		$("#grade .item-type").hide().children("input").prop("checked", false);
		$("#grade .grade"+gradePhase).show();
		$("#grade .all-type").children("input").prop("checked",true);
		$("#knowledgePointList").html("");
	});
});

/**
 * 选择知识点
 */
function toSelectKnowledgePoint(){
	var gradePhase = $("#gradePhase :radio:checked").val();
	var gradePhaseName = $("#gradePhase :radio:checked").parent().text();
	knowledgePointer.gradePhase = gradePhase;
	knowledgePointer.gradePhaseName = gradePhaseName;
	var checkedArr = new Array();
	$("#knowledgePointList>a").each(function(){
		checkedArr.push($(this).attr("data"));
	});
	knowledgePointer.selectKnowledgePoint(null, checkedArr, null, showKnowledgePoint);
}

function showKnowledgePoint(knowledgeArr){
	_html = '';
	$(knowledgeArr).each(function(i, item){
		_html += '<a data="' + item.id + '" href="javascript:void(0);">'
		+ item.name + '<span class="knowledge-close" onclick="removeLabel(this)"></span></a>';
	});
	$("#knowledgePointList").html(_html);
}

function removeLabel(obj){
	$(obj).parent().remove();
}

/**
 * 加载章名
 */
$(function(){
	$.post("/newGrammarManager/getCourseFullNameById",{"courseId": courseId},
			function(data){
		if(data){
			$("#courseName").text(data.gradeName+"-"+data.versionName+"-"+data.courseName);
		}
	},"json");
});


/**
 * 获取满足过滤条件的题库试题
 */
var questionList;
var questionNum;
function getSimulationQuestionList(nowPage){
	loaded = true;
	if(nowPage==null){
		nowPage=1;
	}
	param.pageNum = nowPage;
	if($("#pageSize").length>0){
		param.pageSize = parseInt($("#pageSize").val());
	}
	if(!getParam()){
		return;
	}
	var queryDialog =null;
	queryDialog = dialog({
        title: "温馨提示",
        content:"正在查询，请稍等"
    });
    queryDialog.showModal();
	$.ajax({
		type: "POST",
        url:"/newGrammarManager/getSimulationQuestionList",
        data: param,
        dataType: "json",
        success: function(data){
	 		queryDialog.remove();
        	questionNum = data.questionNum;
        	questionList = data.questionList;
        	$("#haveNum").text(questionNum);
			showQuestionList();
        }
	});
}

/**
 * 获取参数
 */
var param = {
		sourceTypeArr : new Array(),
		gradePhase: null,
		gradeArr : new Array(),
		knowledgePointArr : new Array(),
		questionTypeArr : new Array(),
		difficultyArr : new Array(),
		orderBy : "createTime desc",
		pageNum : 1,
		pageSize : 10
	}

/**
 *点击排序按钮
 */
var loaded = false;
function orderByQuery(str,obj){
	$(obj).find(".shang").toggleClass('none');
	$(obj).find(".xia").toggleClass('none');
	$(obj).siblings().find(".s-ico").addClass('qianhui');
	$(obj).find(".s-ico").removeClass('qianhui');
	
	param.orderBy=str+" "+$(obj).find(".s-ico span:visible").attr("sort");
	if(loaded){
		getSimulationQuestionList();
	}
}

function getParam(){
	param.sourceTypeArr.length=0;
	param.gradePhase = null;
	param.gradeArr.length=0;
	param.knowledgePointArr.length=0;
	param.questionTypeArr.length=0;
	param.difficultyArr.length=0;
	
	$("#sourceType .item-type input:checked").each(function(){
		param.sourceTypeArr.push($(this).val());
	});
	param.gradePhase = $("#gradePhase :radio:checked").val();
	$("#grade .item-type input:checked").each(function(){
		param.gradeArr.push($(this).val());
	});
	$("#knowledgePointList>a").each(function(){
		param.knowledgePointArr.push($(this).attr("data"));
	});
	if(param.knowledgePointArr.length==0){
		alert("请选择知识点！");
		return false;
	}
	if($("#questionType .all-type input").prop("checked")){
		$("#questionType .item-type input").each(function(){
			param.questionTypeArr.push($(this).val());
		});
	}else{
		$("#questionType .item-type input:checked").each(function(){
			param.questionTypeArr.push($(this).val());
		});
	}
	$("#difficulty .item-type input:checked").each(function(){
		param.difficultyArr.push($(this).val());
	});
	return true;
}

function showQuestionList(){
	$("#dataTbody").children().remove();
	var contentHtml = "";
	if(questionList==null||questionList.length==0){
		tip = "*没有符合条件的系统试题哦~*";
		contentHtml="<tr><td colspan='"+$("#dataTable thead tr th").length+"' style='color:red'>"+tip+"</td></tr>";
		$("#dataTbody").append(contentHtml);
		$("#add-question-btn").hiden();
	}else{
		var questionArr = null;
		$(questionList).each(function(index){
			contentHtml ="<tr id='trId_"+this.id+"'>";
			if(arrayHasElement(questionIdArr, this.id)){
				contentHtml+="<td><input name='questionId' value='"+this.id+"' type='checkbox' checked='checked' onclick='clickCheckBox(this);'/></td>";
			}else{
				contentHtml+="<td><input name='questionId' value='"+this.id+"' type='checkbox' onclick='clickCheckBox(this);'/></td>";
			}
			contentHtml+="<td>"+(param.pageSize*(param.pageNum-1)+index+1)+"</td>";
			contentHtml+="<td>"+getKnowledgePointName(this.knowledgePoint)+"</td>";
			contentHtml+="<td>"+questionTypeArr[this.questionType]+"</td>";
			contentHtml+="<td>"+difficultyArr[this.difficulty]+"</td>";
			contentHtml+="<td class='text-left "+(this.parse==null?"'":("parse-tips' title='"+this.parse+"'"))+">";
			if(this.questionType==3){//判断题，不显示选项
				contentHtml+=this.question+"</td>";
				contentHtml+="<td>"+(this.correctOption=="T"?"√":"×")+"</td>";
			}else if(this.questionType==4){//填空题
				questionArr = $.trim(this.question).split(/_+/g);
				$(questionArr).each(function(i,item){
					contentHtml += item;
					if(i<questionArr.length-1){
						contentHtml += "<u>&nbsp;"+(i+1)+"&nbsp;</u>";
					}
				});
				contentHtml+="</td><td>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="1."+this.optionA;
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<br/>2."+this.optionB;
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<br/>3."+this.optionC;
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<br/>4."+this.optionD;
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<br/>5."+this.optionE;
				}
				contentHtml+="</td>";
			}else{
				contentHtml+=this.question+"<br>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="<span>A."+this.optionA+"</span>&emsp;";
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<span>B."+this.optionB+"</span>&emsp;";
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<span>C."+this.optionC+"</span>&emsp;";
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<span>D."+this.optionD+"</span>&emsp;";
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<span>E."+this.optionE+"</span>&emsp;";
				}
				contentHtml+="</td>";
				contentHtml+="<td>"+this.correctOption+"</td>";
			}
			contentHtml+="<td>"+new Date(this.createTime).Format('yyyy-MM-dd hh:mm:ss')+"</td>";
			contentHtml+="</tr>";
			$("#dataTbody").append(contentHtml);
			$(".parse-tips").tipso({
				useTitle: true,
				position: 'bottom',
				width: '300px'
			});
		});	
		$("#add-question-btn").show();
	}
	loadPageInfo();
}

/**
 * 判断试题数组中是否有指定id的试题
 * @param questionArr
 * @param id
 * @returns {Boolean}
 */
function arrayHasElement(questionArr, id){
	if(questionArr==null || id==null){
		return false;
	}
	for(index=0; index<questionArr.length; index++){
		if(questionArr[index]==id){
			return true;
		}
	}
	return false;
}


/**
 * 添加试题
 */
var questionIdArr = new Array();
function addQuestion(id){
	questionIdArr.push(id);
	return true;
}

/**
 * 删除试题
 */
function removeQuestion(id){
	var flag =false;
	for(var i=0; i<questionIdArr.length; i++){
		if(flag){
			questionIdArr[i] = questionIdArr[i+1];
		}else if(questionIdArr[i]==id){
			flag=true;
			questionIdArr[i]=questionIdArr[i+1];
		}
	}
	if(flag){
		questionIdArr.length--;
	}
	return flag;
}

/**
 * 表格中复选框点击事件
 * @param element
 */
function clickCheckBox(element){
	if($(element).prop("checked")){
		addQuestion($(element).val());
	}else{
		removeQuestion($(element).val());
	}
	$("#addNum").text(questionIdArr.length);
}


/**
 * 加载分页信息
 */
function loadPageInfo(){
	var pageLen = 1;
	if(questionNum!=undefined&&questionNum>1){
		if(param.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(questionNum/param.pageSize);					
		}
	}
	
	$('.pageTest').page({
	      leng: pageLen,//分页总数
	      activeClass: 'activP' , //active 类样式定义
	      page_size:param.pageSize,
	      nowPage:param.pageNum,
	      selectPage:getSimulationQuestionList
	    });
	$(".selectAll").prop("checked",false);	
}

/**
 * 全选按钮点击事件
 * @param obj
 */
function selectQuestionAll(obj){
	if($(obj).prop("checked")){
		$("input[name='questionId']:not(:checked)").click();		
	}else{
		$("input[name='questionId']:checked").click();		
	}
}

/**
 * 添加本章检测试题
 */
function addCourseQuestion(isAll){
	if(courseId==null || courseId==0){
		alert("没有指定章，不能添加试题！");
		return;
	}
	var _url = "";
	var _param;
	if(isAll){//添加所有试题
		_url = "/newGrammarManager/addAllCourseQuestion";
		_param = {
				"courseId" : courseId,
				"sourceTypeArr" : param.sourceTypeArr,
				"gradeArr" : param.gradeArr,
				"knowledgePointArr" : param.knowledgePointArr,
				"questionTypeArr" : param.questionTypeArr,
				"difficultyArr" : param.difficultyArr
			};
	}else{//添加选中试题
		if(questionIdArr.length==0){
			alert("请先选出要添加的试题！");
			return;
		}
		_url = "/newGrammarManager/addSelectCourseQuestion";
		_param = {
				"courseId": courseId,
				"questionIdArr":questionIdArr
		};
	}
	$.ajax({
		type: "POST",
        url: _url,
        data: _param,
        dataType: "json",
        success: function(data){
        	if(data){
        		alert("添加试题成功！");
        	}else{
        		alert("添加试题失败！");
        	}
        }
	});
}

/**
 * 返回
 */
function goBack(){
	window.history.back();
}