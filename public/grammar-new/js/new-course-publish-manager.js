var errorReportNum = 0;
var gradeArr = new Array();
gradeArr[30] = "小学";
gradeArr[40] = "初中";
gradeArr[50] = "高中";
$(function(){
	getCoursePublishList();
});


/**
 * 获取错误记录列表
 */
function getCoursePublishList(){
	$.post("/newGrammarManager/getCoursePublishList", null, function(data){
		if(data==null || data.length==0){
			$("#dataTable tbody").html("<tr><td style='color: red;' colspan='"+$("#dataTable thead th").length+"'>--没有记录--</td></tr>");
			return;
		}
		var _html = "";
		$(data).each(function(i,item){
			_html += "<tr>";
			_html += "<td>"+(i+1)+"</td>";
			_html += "<td>"+gradeArr[item.grade]+"</td>";
			_html += "<td>"+item.versionName+"</td>";
			_html += "<td>"+item.courseName+"</td>";
			_html += "<td>"+formatDate(item.applyTime)+"</td>";
			_html += "<td>"+(item.applicantName==null?item.applicantUserId:item.applicantName)+"</td>";
			_html += "<td>"+item.comment+"</td>";
			_html += "<td><a href='javascript:courseUpdateDetail("+item.courseId+");'>查看</a></td>";
			_html += "<td><button class='cancel-publish' onclick='refusePublish("+item.id+","+item.courseId+");'>拒绝发布</button>";
			_html += "<button class='ensure-publish' onclick='publishCourse("+item.id+","+item.courseId+");'>确定发布</button></td>";
		});
		$("#dataTable tbody").html(_html);
	},"json");
}


/**
 * 加载分页信息
 *//*
function loadPageInfo(){
	var pageLen = 1;
	if(errorReportNum!=undefined&&errorReportNum>1){
		if(param.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(errorReportNum/param.pageSize);					
		}
	}
	
	$('.pageTest').page({
      leng: pageLen,//分页总数
      activeClass: 'activP' , //active 类样式定义
      page_size:param.pageSize,
      nowPage:param.pageNum,
      selectPage:getErrorReportList
    });
}

*//**
点击排序按钮
**//*
function orderByQuery(str,obj){
	$(obj).find(".shang").toggleClass('none');
	$(obj).find(".xia").toggleClass('none');
	$(obj).siblings().find(".s-ico").addClass('qianhui');
	$(obj).find(".s-ico").removeClass('qianhui');
	
	param.orderBy=str+" "+$(obj).find(".s-ico span:visible").attr("sort");
	getErrorReportList(param.pageNum);
}
*/
/**
 * 查看详情
 * @param index
 */
function detailContent(index){
	var item = errorReportList[index];
	var _html = "<div><ul class='detail-ul'>";
	_html += "<li><label>上报时间：</label><span>"+formatDate(item.reportTime)+"</span><li>";
	_html += "<li><label>错误章：</label><span>"+gradeArr[item.grade]+" - "+item.versionName+" - "+item.courseName+"</span><li>";
	_html += "<li><label>错误节：</label><span>"+item.unitName+"</span><li>";
	_html += "<li><label>错误步骤：</label><span><a href='javascript:errorPosition("+item.unitId+","+item.id+","+item.status+");'>第"+item.position+"步</a></span><li>";
	_html += "<li><label>错误描述：</label><span style='max-width:430px;'>"+item.description+"</span><li>";
	_html += "<li><hr class='cut-line'/></li>";
	if(item.status==1){
		_html += "<li><label>处理人：</label><span>无</span><li>";
	}else{
		_html += "<li><label>处理人：</label><span>"+(item.solverName==null?item.solverId:item.solverName)+"</span><li>";
		_html += "<li><label>提交审核时间：</label><span>"+formatDate(item.auditTime)+"</span><li>";
	}
	switch(item.status){
		case 1: _html += "<li><label>处理结果：</label><span class='gray'>未处理</span></li>"; break;
		case 2: _html += "<li><label>处理结果：</label><span class='orange'>待审核</span></li>"; break;
		case 3: _html += "<li><label>处理结果：</label><span class='red'>审核未通过</span></li>"; break;
		case 4: _html += "<li><label>处理结果：</label><span class='green'>已处理</span></li>"; break;
	}
	if(item.status==3){
		_html += "<li class='red'><label>审核未通过原因：</label><span style='max-width:388px;'>"+item.notPassReason+"</span><li>";
	}
	if(item.status==4){
		_html += "<li><label>审核通过时间：</label><span>"+formatDate(item.passTime)+"</span><li>";
	}
	_html += "</ul></div>";
	var d = dialog({
		title: "详情",
		content: _html,
		padding: "20px",
		width: "500px",
	});
	d.showModal();
}

/**
 * 拒绝发布
 * @param coursePublishId
 */
function refusePublish(publishId, courseId){
	var _html = '<label>拒绝发布原因：</label><div><textarea class="refuse-reason"></textarea></div>';
	var d = dialog({
		title: "拒绝发布",
		content: _html,
		padding: "20px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			var refuseReason = $.trim($(".refuse-reason").val());
			if(refuseReason==""){
				alert("拒绝发布的原因不能为空！");
				return false;
			}
			var flag = false;
			$.ajax({
				type: "post",
				url: "/newGrammarManager/refusePublish",
				data: {"publishId": publishId, "courseId":courseId, "refuseReason": refuseReason},
				async: false,
				dataType: "json",
				success: function(data){
					flag = data;
					if(data){
						alert("拒绝发布操作成功！");
						getCoursePublishList();
					}else{
						alert("拒绝发布操作失败！");
					}
				}
			});
			return flag
		}
	});
	d.showModal();
}

/***
 * 确认发布
 * @param courseId
 */
function publishCourse(publishId, courseId){
	var d = dialog({
		title: "确认发布",
		content: "点击“确定”会将此条申请发布内容发布外网，是否确认发布？",
		padding: "20px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			var flag = false;
			$.ajax({
				type: "post",
				url: "/newGrammarManager/publishCourse",
				data: {"publishId": publishId,"courseId": courseId},
				async: false,
				dataType: "json",
				success: function(data){
					flag = data;
					if(data){
						alert("发布成功！");
						getCoursePublishList();
					}else{
						alert("发布失败！");
					}
				}
			});
			return flag
		}
	});
	d.showModal();
}


var typeArr = ["",
               "新增章",
               "章名修改",
               "章顺序调整",
               "新增节",
               "节名修改",
               "节顺序调整",
               "节删除",
               "卡片更新",
               "本节检测试题更新",
               "本章检测试题更新",
               "节复制",
               "章复制"
               ];
var detailMap = new Object();
var prefix = "c_";
/**
 * 查看章修改详情
 * @param courseId
 */
function courseUpdateDetail(courseId){
	var _content = '<ul class="detail-ul"></ul>';
	var d = dialog({
		title: "更新详情",
		content: _content,
		padding: "20px",
		width: "650px",
		zIndex: 8
	});
	d.showModal();
	var detail = detailMap[prefix+courseId];
	if(detail==null){
		$.ajax({
			type: "post",
			url: "/newGrammarManager/getCourseUpdateDetailList", 
			data: {"courseId": courseId},
			async: false,
			dataType: "json",
			success: function(data){
				detail = data;
				detailMap[prefix+courseId] = data;
			}
		});
	}
	var _html = "";
	$(detail).each(function(i, item){
		_html += "<li>"+(i+1)+"、";
		if(item.unitId!=null){
			_html += item.unitName + " ";
		}
		_html += typeArr[item.type];
		if(item.comment!=null){
			_html += ' <span style="color: red;">'+item.comment+'</span>';
		}
		if(item.type==8 || item.type==9 || item.type==10){
			_html += "<a class='fr' href='javascript:udpateDetail("+item.type+","+item.unitId+","+courseId+");'>查看详情</a>";
		}
		_html += '（'+(item.teachName==null?item.teachId:item.teachName)+'）';
		_html += "</li>";
	});
	$(".detail-ul").html(_html);
}

/**
 * 更新的详细内容查看
 * @param type
 * @param unitId
 */
function udpateDetail(type, unitId, courseId){
	switch(type){
	case 8:window.open("error-detail.html?unitId="+unitId); break;
	case 9:managerQuestion(unitId); break;
	case 10:managerLevelQuestion(courseId); break;
	}
}

/*************************查看本节检测试题***************************/
/**
 * 查看试题
 */
var questionTypeArr1 = ["","单选题","多选题","填空题"];
function managerQuestion(unitId){
	var content = "<div id='question-dialog'>"+$("#questionList").html()+"</div>";
	var d=dialog({
		title: "试题列表",
		content: content,
		width: "900px",
		padding: "0 20px",
		zIndex: 500,
		cancelValue: "关闭",
		ok: false,
		zIndex: 99
	});
	//初始化查询参数
	questionParam.unitId = unitId;
	questionParam.pageNum = 1;
	questionParam.pageSize = 10;
	
	showQuestionList();
	d.showModal();
}

/**
 * 展示试题列表
 */
var questionParam = {
		unitId: null,
		pageNum: 1,
		pageSize: 10
}
var questionNum = 0;
function showQuestionList(nowPage){
	questionParam.pageNum = nowPage || 1;
	if($("#pageSize").length>0){
		questionParam.pageSize = $("#pageSize").val();
	}
	var questionList;
	$.ajax({
		type: "get",
		url: "/newGrammarManager/getQuestionList",
		data: questionParam,
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			questionList = data.questionList;
			if(questionParam.pageNum==1){
				questionNum = data.questionNum;
				$("#question-dialog .totalCount>span").text(questionNum);
			}
		}
	});
	
	var _html = "";
	if(questionList==null || questionList.length==0){
		_html += "<tr><td colspan='"+$("#question-dialog thead th").length+"'class='red'>没有添加试题哦！</td></tr>"
	}
	$(questionList).each(function(i,item){
		_html += "<tr>";
		_html += "<td>"+((questionParam.pageNum-1)*questionParam.pageSize+i+1)+"</td>";
		_html += "<td>"+questionTypeArr1[item.questionType]+"</td>";
		_html+="<td class='text-left "+(this.parse==null?"'":("parse-tips' title='"+this.parse+"'"))+">";
		if(this.questionType==3){//填空题
			questionArr = $.trim(this.question).split(/_{3,}/g);
			$(questionArr).each(function(j,part){
				_html += part;
				if(j<questionArr.length-1){
					_html += "<u>&nbsp;"+(j+1)+"&nbsp;</u>";
				}
			});
			_html+="</td><td>";
			if(this.optionA!=null&&$.trim(this.optionA)!=""){
				_html+="1."+this.optionA;
			}
			if(this.optionB!=null&&$.trim(this.optionB)!=""){
				_html+="<br/>2."+this.optionB;
			}
			if(this.optionC!=null&&$.trim(this.optionC)!=""){
				_html+="<br/>3."+this.optionC;
			}
			if(this.optionD!=null&&$.trim(this.optionD)!=""){
				_html+="<br/>4."+this.optionD;
			}
			if(this.optionE!=null&&$.trim(this.optionE)!=""){
				_html+="<br/>5."+this.optionE;
			}
			_html+="</td>";
		}else{//单、多选题
			_html+=this.question+"<br>";
			if(this.optionA!=null&&$.trim(this.optionA)!=""){
				_html+="<span>A."+this.optionA+"</span>&emsp;";
			}
			if(this.optionB!=null&&$.trim(this.optionB)!=""){
				_html+="<span>B."+this.optionB+"</span>&emsp;";
			}
			if(this.optionC!=null&&$.trim(this.optionC)!=""){
				_html+="<span>C."+this.optionC+"</span>&emsp;";
			}
			if(this.optionD!=null&&$.trim(this.optionD)!=""){
				_html+="<span>D."+this.optionD+"</span>&emsp;";
			}
			if(this.optionE!=null&&$.trim(this.optionE)!=""){
				_html+="<span>E."+this.optionE+"</span>&emsp;";
			}
			_html+="</td>";
			_html+="<td>"+this.correctOption+"</td>";
		}
		_html += "</tr>"
	});
	$("#question-dialog tbody").html(_html);
	$(".parse-tips").tipso({
		useTitle: true,
		position: 'bottom',
		width: '300px'
	});
	loadPageInfo1();
}

/**
 * 加载分页信息
 */
function loadPageInfo1(){
	var pageLen = 1;
	if(questionNum!=undefined&&questionNum>1){
		if(questionParam.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(questionNum/questionParam.pageSize);					
		}
	}
	
	$('#question-dialog .pageTest').page({
	      leng: pageLen,//分页总数
	      activeClass: 'activP' , //active 类样式定义
	      page_size:questionParam.pageSize,
	      nowPage:questionParam.pageNum,
	      selectPage:showQuestionList
	    });
	
	$("#question-dialog .selectAll").prop("checked",false);	
}

/*************************查看本章检测试题***************************/
/**
 * 查看试题
 */
function managerLevelQuestion(cousreId){
	var content = "<div id='question-dialog'>"+$("#levelList").html()+"</div>";
	var d=dialog({
		title: "试题列表",
		content: content,
		width: "900px",
		padding: "0 20px",
		zIndex: 500,
		cancelValue: "关闭",
		ok: false,
		zIndex: 99
	});
	//初始化查询参数
	courseParam.courseId = cousreId;
	courseParam.pageNum = 1;
	courseParam.pageSize = 10;
	
	showCourseQuestionList();
	d.showModal();
}

/**
 * 展示试题列表
 */
var courseQuestionNum;
var courseParam={
		courseId: null,
		pageNum: 1,
		pageSize: 10
}
var levelNum = 20; //本章检测题最低限制量
function showCourseQuestionList(nowPage){
	if(nowPage==null){
		nowPage=1;
	}
	courseParam.pageNum = nowPage;
	if($("#pageSize").length>0){
		courseParam.pageSize = $("#pageSize").val();
	}
	var questionList;
	$.ajax({
		type: "get",
		url: "/newGrammarManager/getCourseQuestionList",
		data: courseParam,
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			questionList = data.courseQuestionList;
			if(courseParam.pageNum==1){
				data.questionTypeList
				courseQuestionNum = 0;
				if(data.questionTypeList!=null){
					var _html = "";
					$(data.questionTypeList).each(function(i, item){
						courseQuestionNum += item.num;
						_html += item.num + "道" + questionTypeArr[item.questionType];
						if(i<data.questionTypeList.length-1){
							_html += "，";
						}else{
							_html += "。";
						}
					});
				}
				if(courseQuestionNum>=levelNum){
					_html = "共<font style='font-weight: bold; color: green;'>"+courseQuestionNum+"</font>题，"+_html;
				}else if(courseQuestionNum>0){
					_html = "共<font style='font-weight: bold; color: red;'>"+courseQuestionNum+"</font>题，"+_html;
				}else {
					_html = "共<font style='font-weight: bold; color: red;'>"+courseQuestionNum+"</font>题。";
				}
				$("#question-dialog .tips-li").html(_html);
				$("#question-dialog .totalCount>span").text(courseQuestionNum);
			}
		}
	});
	
	var _html = "";
	if(questionList==null || questionList.length==0){
		_html += "<tr><td colspan='"+$("#question-dialog thead th").length+"'class='red'>没有添加试题哦！</td></tr>"
	}
	$(questionList).each(function(i,item){
		_html += "<tr>";
		_html += "<td>"+((courseParam.pageNum-1)*courseParam.pageSize+i+1)+"</td>";
		_html += "<td>"+grammarPointArr[this.grammarPoint]+"</td>";
		_html += "<td>"+questionTypeArr[item.questionType]+"</td>";
		_html+="<td class='text-left "+(this.parse==null?"'":("parse-tips' title='"+this.parse+"'"))+">";
		if(this.questionType==3){//判断题，不显示选项
			_html+=this.question+"</td>";
			_html+="<td>"+(this.correctOption=="T"?"√":"×")+"</td>";
		}else if(this.questionType==4){//填空题
			questionArr = $.trim(this.question).split(/_+/g);
			$(questionArr).each(function(i,item){
				_html += item;
				if(i<questionArr.length-1){
					_html += "<u>&nbsp;"+(i+1)+"&nbsp;</u>";
				}
			});
			_html+="</td><td>";
			if(this.optionA!=null&&$.trim(this.optionA)!=""){
				_html+="1."+this.optionA;
			}
			if(this.optionB!=null&&$.trim(this.optionB)!=""){
				_html+="<br/>2."+this.optionB;
			}
			if(this.optionC!=null&&$.trim(this.optionC)!=""){
				_html+="<br/>3."+this.optionC;
			}
			if(this.optionD!=null&&$.trim(this.optionD)!=""){
				_html+="<br/>4."+this.optionD;
			}
			if(this.optionE!=null&&$.trim(this.optionE)!=""){
				_html+="<br/>5."+this.optionE;
			}
			_html+="</td>";
		}else{
			_html+=this.question+"<br>";
			if(this.optionA!=null&&$.trim(this.optionA)!=""){
				_html+="<span>A."+this.optionA+"</span>&emsp;";
			}
			if(this.optionB!=null&&$.trim(this.optionB)!=""){
				_html+="<span>B."+this.optionB+"</span>&emsp;";
			}
			if(this.optionC!=null&&$.trim(this.optionC)!=""){
				_html+="<span>C."+this.optionC+"</span>&emsp;";
			}
			if(this.optionD!=null&&$.trim(this.optionD)!=""){
				_html+="<span>D."+this.optionD+"</span>&emsp;";
			}
			if(this.optionE!=null&&$.trim(this.optionE)!=""){
				_html+="<span>E."+this.optionE+"</span>&emsp;";
			}
			_html+="</td>";
			_html+="<td>"+this.correctOption+"</td>";
		}
		_html += "</tr>"
	});
	$("#question-dialog tbody").html(_html);
	$(".parse-tips").tipso({
		useTitle: true,
		position: 'bottom',
		width: '300px'
	});
	loadPageInfo2();
}

/**
 * 加载分页信息
 */
function loadPageInfo2(){
	var pageLen = 1;
	if(courseQuestionNum!=undefined&&courseQuestionNum>1){
		if(courseParam.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(courseQuestionNum/courseParam.pageSize);					
		}
	}
	
	$('#question-dialog .pageTest').page({
	      leng: pageLen,//分页总数
	      activeClass: 'activP' , //active 类样式定义
	      page_size:courseParam.pageSize,
	      nowPage:courseParam.pageNum,
	      selectPage:showCourseQuestionList
	    });
}


/**
 * 格式化时期时间
 * @param time
 * @returns
 */
function formatDate(time){
	return new Date(time).Format("yyyy-MM-dd hh:mm:ss");
}
