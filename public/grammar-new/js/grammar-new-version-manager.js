var versionMap = new Array();
$(function(){
	getVersionList();
});

/**
 * 加载版本列表
 */
var versionList = null;
function getVersionList(){
	var grade = $("#grade").val();
	if(versionMap[grade]==null){
		$.get({
			type: "get",
			url: "/newGrammarManager/getVersionList",
			cache: false,
			async: false,
			data: {"grade": grade},
			dataType: "json",
			success: function(data){
				versionMap[grade] = data;
			}
		});
	}
	versionList = versionMap[grade];
	if(versionList==null || versionList.length==0){
		$("#dataTable tbody").html("<tr><td style='color: red;' colspan='"+$("#dataTable thead th").length+"'>*没有数据*</td></tr>");
		return;
	}
	var operation = "", _html = "";
	$(versionList).each(function(i,item){
		operation = "";
		if(i==0){
			operation += "<a style='margin-left: 23px;' href='javascript:adjustVersion("+grade+","+i+",\"down\")'>↓</a>"; 
		}else if(i==versionList.length-1){
			operation += "<a style='margin-right: 23px;' href='javascript:adjustVersion("+grade+","+i+",\"up\")'>↑</a>"; 
		}else{
			operation += "<a href='javascript:adjustVersion("+grade+","+i+",\"up\")'>↑</a>"; 
			operation += "<a style='margin-left: 10px;' href='javascript:adjustVersion("+grade+","+i+",\"down\")'>↓</a>"; 
		}
		operation += "<a style='margin-left: 10px;' href='javascript:updateVersion("+i+")'>修改</a>"; 
		_html += '<tr>';
		_html += '<td>'+(i+1)+'</td>';
		_html += '<td>'+item.name+'</td>';
		_html += '<td>'+item.courseNum+'</td>';
		if(item.publishTime==null){
			_html += "<td><span class='tips-span no-publish'></span>未发布</td>";
			operation += "<a style='margin-left: 10px;' href='javascript:deleteVersion("+item.id+")'>删除</a>";
			operation += "<a style='margin-left: 10px;' href='javascript:publishVersion("+item.id+")'>发布</a>";
		}else if(item.modifyTime>item.publishTime){
			_html += "<td><span class='tips-span modified'></span>修改未发布</td>";
			operation += "<a style='margin-left: 10px;' href='javascript:publishVersion("+item.id+")'>发布</a>";
		}else{
			_html += "<td><span class='tips-span published'></span>已发布</td>";
		}
		operation += "<a style='margin-left: 10px;' href='javascript:correlateAccount("+item.id+")'>关联账号</a>";
		_html += '<td style="text-align: left; padding: 0 20px">'+operation+'</td>';
		_html += '</tr>';
	});
	$("#dataTbody").html(_html);
}


/**
 * 添加章
 * 
 */
function addVersion(){
	var _html = "<div id='version-dialog'>"+$("#version-div").html()+"<div>";
	var d = dialog({
		title: "添加版本",
		content: _html,
		padding: "20px",
		width: "400px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			var newVersion = new Object(); //添加后的新章
			newVersion.grade = $("#version-dialog .grade").val();
			newVersion.name = $.trim($("#version-dialog .version").val());
			if(newVersion.name==null || newVersion.name==""){
				alert("请输入版本名称！");
				return false;
			}
			var flag = false;
			$.ajax({
				 type: "POST",
		         url: "/newGrammarManager/addVersion",
		         async: false,
		         data: newVersion,
		         dataType: "json",
		         success: function(data){
		        	 if(data){
		        		 $("#grade option[value='"+grade+"']").prop("selected", true);
		        		 versionMap[newVersion.grade] = null;
		        		 getVersionList();
		        	 }else{
		        		 alert("添加版本失败！");
		        	 }
		        	 flag = data;
		         }
			});
			return flag;
		}
	});
	d.showModal();
	var grade = $("#grade").val();
	$("#version-dialog .grade option[value='"+grade+"']").prop("selected", true);
}
/**
 * 修改版本
 * @param order
 */
function updateVersion(index){
	var grade = $("#grade").val();
	var version = versionList[index];
	var newVersion = new Object();
	newVersion.id = version.id;
	var _html = "<div id='version-dialog'>"+$("#version-div").html()+"<div>";
	var d = dialog({
		title: "修改版本",
		content: _html,
		padding: "20px",
		width: "400px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			newVersion.name = $.trim($("#version-dialog .version").val());
			if(newVersion.name==null || newVersion.name==""){
				alert("请输入版本名称！");
				return false;
			}
			if(newVersion.name==version.name){ //修改时，并没有真正的修改
				return true;
			}
			var flag = false;
			$.ajax({
				type: "POST",
				url: "/newGrammarManager/updateVersion",
				async: false,
				data: newVersion,
				dataType: "json",
				success: function(data){
					if(data){
						versionMap[grade] = null;
						getVersionList();
					}else{
						alert("修改版本失败！");
					}
					flag = data;
				}
			});
			return flag;
		}
	});
	d.showModal();
	$("#version-dialog .grade option[value='"+grade+"']").prop("selected", true);
	$("#version-dialog .grade").prop("disabled", true);
	$("#version-dialog .version").val(version.name);
}



/**
 * 调整章顺序
 */
function adjustVersion(grade,index,direction){
	var srcOrder = versionList[parseInt(index)].disporder;
	var desOrder;
	if(direction == "up"){
		desOrder = versionList[parseInt(index)-1].disporder;
	}else{
		desOrder = versionList[parseInt(index)+1].disporder;
	}
	
	$.post("/newGrammarManager/adjustVersion",
			{"grade": grade, "srcOrder": srcOrder, "desOrder": desOrder},
			function(data){
				if(data){
					versionMap[grade] = null;
					getVersionList();
				}else{
					alert("调整失败！");
				}
			},"json");
}

/**
 * 删除章
 * @param versionId
 */
function deleteVersion(versionId){
	var d = dialog({
		title: "删除版本",
		content: "删除版本，该版本下的所有章信息也会一并删除。你确定要执行该操作吗？",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "确认",
		ok: function(){
			$.post("/newGrammarManager/deleteVersion",
					{"versionId": versionId},
					function(data){
						if(data){
							var grade = $("#grade").val();
							versionMap[grade] = null;
							getVersionList();
						}else{
							alert("删除失败！");
						}
					},"json");
		}
	});
	d.showModal();
}

/**
 * 关联编辑账号
 * @param versionId
 */
function correlateAccount(versionId){
	var _content = '<div id="correlateDia"><ul class="account-list"></ul></div>';
	var d = dialog({
		title: "关联账号",
		width: "300px",
		padding: "20px",
		content: _content,
		okValue: "确认",
		ok: function(){
			var userIdArr = new Array();
			$("#correlateDia .account-list :checked").each(function(i, item){
				userIdArr.push($(this).val());
			});
			$.post("/newGrammarManager/updateVersionEditor",
					{"versionId": versionId, "userIdArr": userIdArr},
					function(data){
						if(data){
							var grade = $("#grade").val();
							versionMap[grade] = null;
							getVersionList();
						}else{
							alert("删除失败！");
						}
					},"json");
		}
	});
	d.showModal();
	$.post("/newGrammarManager/getVersionEditorList",
			{"versionId": versionId},
			function(data){
				var _html = '';
				if(data==null || data.length==0){
					_html += '<li>*没有账号信息*</li>';
				}else{
					$(data).each(function(i, item){
						_html += '<li><label><input type="checkbox" value="'+item.userId+'" '+(item.versionId==null?'':'checked="checked"')+'/>'+item.username+'('+item.userId+')</label></li>';
					});
				}
				$("#correlateDia .account-list").html(_html);
			},"json");
	
}

/**
 * 申请发布章
 * @param index
 */
function publishVersion(versionId){
	var d = dialog({
		title: "发布版本",
		content: "该功能只发布版本，版本下的章需要老师单独申请发布！",
		padding: "20px",
		width: "300px",
		cancelValue: "取消",
		okValue: "发布",
		cancel: true,
		ok: function(){
			var flag = false;
			$.ajax({
				type: "post",
				url: "/newGrammarManager/publishVersion",
				async: false,
				data: {"versionId": versionId},
				dataType: "json",
				success: function(data){
					if(data){
						alert("发布成功！");
						var grade = $("#grade").val();
						versionMap[grade] = null;
						getVersionList();
					}else{
						alert("发布失败！");
					}
					flag = data;
				}
			});
			return flag;
		}
	});
	d.showModal();
}

/**
 * 展示发布详情
 * @param index
 */
function showPublishRecord(index){
	var version = versionList[index];
	var _html = "<div id='publish-record' style='overflow-y: auto;max-height: 350px;'>"+$("#publish-record-div").html()+"</div>"
	var d = dialog({
		title: "发布详情",
		content: _html,
		padding: "20px",
		width: "570px"
	});
	d.showModal();
	$("#publish-record .versionName").text(version.name);
	$("#publish-record .unitNum").text(version.unitNum);
	$("#publish-record .levelQuestionNum").text(version.levelQuestionNum+"题");
	if(version.levelQuestionNum>=levelNum){
		$("#publish-record .levelQuestionNum").css("color","green");
	}else{
		$("#publish-record .levelQuestionNum").css("color","red");
	}
	if(version.publishRecord==null){
		$.ajax({
			type: "get",
			url: "/newGrammarManager/getPublishRecord",
			cache: false,
			async: false,
			dataType: "json",
			data: {"versionId": version.id},
			success: function(data){
				version.publishRecord = data; //version与versionList[index]指向同一个对象，所以versionList[index]的值也会跟着修改
			}
		});
	}
	$("#publish-record .publishNum").text(version.publishRecord.length+"次");
	_html = "";
	$(version.publishRecord).each(function(i, item){
		if(i==7){
			_html += "<tr style='background-color: #fff;'><td style='text-align: center;' colspan='2'><a href='javascript:showAllPublishRecord("+index+");'>显示全部</a></td></tr>";
			return false;
		}
		_html += "<tr style='background-color: #fff;'>";
		_html +="<td style='vertical-align: top; width: 155px;'>"+new Date(item.applyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
		if(item.comment==null || $.trim(item.comment)==""){
			_html +="<td>--未填--</td>";
		}else{
			_html +="<td>"+item.comment+"</td>";
		}
		_html += "</tr>";
	});
	$("#publish-record .publishRecord").html(_html);
}

/**
 * 显示全部发布记录
 * @param index
 */
function showAllPublishRecord(index){
	var version = versionList[index];
	var _html = "";
	$(version.publishRecord).each(function(i, item){
		_html += "<tr style='background-color: #fff;'>";
		_html +="<td style='vertical-align: top; width: 155px;'>"+new Date(item.applyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
		if(item.comment==null || $.trim(item.comment)==""){
			_html +="<td>--未填--</td>";
		}else{
			_html +="<td>"+item.comment+"</td>";
		}
		_html += "</tr>";
	});
	$("#publish-record .publishRecord").html(_html);
};

