var unitId = JSON.parse(Request["unitId"]);
var eId = Request["eId"];
var userId;
var grammarCard = {
  id: unitId.articleId,
  name: "",
  inteNum: 0,
  grammarContentList: new Array(),
  courseId:unitId.courseId,
}
var chinese = /[\u4E00-\u9FA5，；。“”？！：]/g;
var uploadAddr = "";
//ckeditor按钮上传图片方式的路径
let baseUrl = location.hostname === '*************' || location.hostname === 'localhost' ? '/api/redbook-resource' : '/redbook-resource'
var filebrowserImageUploadUrl = uploadAddr + baseUrl + "/newGrammarManager/textUploadByPaste?unitId=" + unitId.unitId;
var filebrowserUploadUrl = uploadAddr + baseUrl + "/newGrammarManager/audioUploadByPaste?unitId=" + unitId.unitId + '&userId=' + userId;
//ckeditor粘贴或拖拽上传图片方式的路径
var uploadUrl = uploadAddr + baseUrl + "/newGrammarManager/textUploadByPaste?unitId=" + unitId.unitId;
var isChanged = false; //页面内容是否发生了改变
/**
 * 初始化事件
 */
$(function () {
  $(".select-title").click(function () {
    $(this).siblings(".example-list").toggle();
    $(this).parent().siblings("li").find(".example-list").hide();
  });
  $(".example-list>li").click(function () {
    $(this).parent().hide();
    addExampleTitle(this);
  });

  var left = $("#card").offset().left + 1010;
  $(".copy-box").css({
    "left": left
  });

  setInterval(function () {
    $.post("/newGrammarManager/editing");
  }, 1000 * 60 * 10);

  function getCookie(name) {
    var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if (arr = document.cookie.match(reg))
      return unescape(arr[2]);
    else
      return null;
  }
  userId = getCookie('userName')
  filebrowserImageUploadUrl = filebrowserImageUploadUrl + "&userId=" + userId;
  uploadUrl = uploadUrl + "&userId=" + userId;
  //获取用户id
  // $.get("/newGrammarManager/getUserId",null,function(data){
  // 	userId = data.userId;
  // 	filebrowserImageUploadUrl = filebrowserImageUploadUrl+"&userId="+userId;
  // 	uploadUrl = uploadUrl+"&userId="+userId;
  // },"json");
})

/*****************************获取并回显卡片内容部分*******************************/
$(function () {
  if (eId != null) {
    $("#total-title").text("错误处理");
    getErrorContent();
  }
  getGrammarCardDetail();
});

/**
 * 获取错误内容
 */
var position;

function getErrorContent() {
  $.ajax({
    type: "post",
    url: "/errorReportManager/getErrorContent",
    data: {
      "errorId": eId
    },
    async: false,
    dataType: "json",
    success: function (data) {
      if (!data || data.status == 4) {
        return;
      }
      position = data.position;
      var _html = '<div>申请人：<span>' + data.userId + '</span></div>';
      _html += '<div>联系方式：<span>' + data.phone + '</span></div>';
      _html += '<div>错误步骤：<span>' + position + '</span></div>';
      _html += '<div>错误描述：</div>';
      _html += '<div style="text-indent: 2em;">' + data.description + '</div>';
      if (data.status > 0) {
        if (data.status == 1) {
          _html += '<div>状态：<span>待发布</span></div>';
        } else if (data.status == 2) {
          _html += '<div>状态：<span>已解决</span></div>';
        } else if (data.status == 3) {
          _html += '<div>状态：<span>无效错误</span></div>';
        }
        _html += '<div>处理人：<span>' + (data.dealUserName == null ? data.dealUserId : data.dealUserName) + '</span></div>';
        _html += '<div>处理时间：</div>';
        _html += '<div>' + new Date(data.dealTime).Format("yyyy-MM-dd hh:mm:ss") + '</div>';
      } else {
        _html += '<div style="text-align: center; ">';
        _html += '<button style="margin-right: 20px;" class="save-submit" onclick="dealError();">已处理</button>';
        _html += '<button class="invalid" onclick="invalidError();">无效错误</button>';
        _html += '</div>';
      }
      $(".submit-div").html(_html);
      var _top = (window.innerHeight - $(".submit-div").outerHeight()) / 2;
      $(".submit-div").show().css("top", _top);
    }
  });
}


var oldCard;

function getGrammarCardDetail() {
  index = 0;
  // $.post("/newGrammarManager/getGrammarCardDetail",
  // 		{"unitId":unitId},
  // 		function(data){
  // 			oldCard = data;
  // 			grammarCard.name = data.name;
  // 			showGrammarCard();
  // 		},"json"); contentType:'multipart/form-data;charset=UTF-8',
  var form = new FormData();
  form.append("articleId", unitId.articleId);
  $.ajax({
    type: "POST",
    async: false,
    url: "/lexueKnowledge/content/getGrammarCardDetail",
    processData: false,
    contentType: false,
    data: form,
    success: function (data) {
      oldCard = data;
      grammarCard.name = data.name;
      showGrammarCard();
    }
  });
}

/**
 * 回显语法卡片
 */
var grammarContentArr = new Array();

function showGrammarCard() {
  console.log(oldCard, 'oldCard')
  $("#name").text(oldCard.name);
  $("#inteNum").text(oldCard.inteNum);
  grammarContentArr.length = 0;
  if (oldCard.titleList == null) {
    return;
  }
  initGrammarContent();
  showGrammarContent();
  var height = localStorage.getItem("Ltop");
  if (height) { //如果有高度就说明以前存储到。获取到给滚动条
    if (document.documentElement.scrollTop != undefined) {
      $("html")[0].scrollTop = height;
    } else {
      $("html")[0].scrollTop == height;
    }
  }
  window.addEventListener("scroll", function () { //监听滚动条
    var top = document.body.scrollTop || document.documentElement.scrollTop; //document.body是获取的body滚动高度，document.documentElement是根节点html的
    localStorage.setItem("Ltop", top);
  })

}

/**
 * 初始化出来语法内容
 */
function initGrammarContent() {
  if (oldCard.targetContentList != null && oldCard.targetContentList.length > 0) {
    grammarContentArr.push({
      "id": -1,
      "question": "学习目标",
      "questionType": 101
    });
    grammarContentArr = grammarContentArr.concat(oldCard.targetContentList);
  }

  $(oldCard.titleList).each(function (i, title1) {
    grammarContentArr.push({
      "id": title1.id,
      "question": title1.name,
      "questionType": 101
    });
    if (title1.subtitleList) {
      addSubtitleList(title1.subtitleList);
    }
    $(title1.titleList).each(function (j, title2) {
      grammarContentArr.push({
        "id": title2.id,
        "question": title2.name,
        "questionType": 102
      });
      if (title2.subtitleList) {
        addSubtitleList(title2.subtitleList);
      }
      $(title2.titleList).each(function (m, title3) {
        grammarContentArr.push({
          "id": title3.id,
          "question": title3.name,
          "questionType": 103
        });
        if (title3.subtitleList) {
          addSubtitleList(title3.subtitleList);
        }
      });
    });
  });

  if (oldCard.summaryContentList != null && oldCard.summaryContentList.length > 0) {
    grammarContentArr.push({
      "id": -2,
      "question": "知识点小结",
      "questionType": 101
    });
    grammarContentArr = grammarContentArr.concat(oldCard.summaryContentList);
  }
}

/**
 * 添加子标题内容到grammarContentArr中
 */
function addSubtitleList(_subtitleList) {
  if (_subtitleList == null) {
    return;
  }
  $(_subtitleList).each(function (i, item) {
    grammarContentArr.push({
      "id": item.id,
      "question": item.name,
      "questionType": 104
    });
    if (item.contentList != null) {
      grammarContentArr = grammarContentArr.concat(item.contentList);
    }
  });
}

/**
 * 回显语法卡片的内容
 */
var cIndex = 0;

function showGrammarContent() {
  index = 0;
  cIndex = 0;
  $(".order-div").siblings().remove();
  var curContent;
  var type = 0;
  $(grammarContentArr).each(function (i, content) {
    type = content.questionType;
    $("#card").append(getTemplate(type));
    curContent = $("#card>div:last");
    $(curContent).removeClass("selected").attr("data_i", cIndex++);
    setContentData(curContent, content);
  });
  $("#card .check-item :checkbox").change(function () {
    $("#selectedNum").text($("#card .check-item :checkbox:checked").length);
  });
  showBtnDiv();
  $('#card>.inte-learn-box .option input').iCheck({
    checkboxClass: 'icheckbox_minimal-green',
    radioClass: 'iradio_minimal-grey',
    increaseArea: '20%' // optional
  });
  $("#card>.inte-learn-box").each(function (i, item) {
    $(item).find(".option>li>label:last").css("margin-bottom", "0");
  });
  $(".question-select").bind("click", function (e) {
    e.stopPropagation();
    $(this).toggleClass("select-clicked").children("ul").toggle();
  });
  //错误定位
  if (position != null && position > 0) {
    var topStart, topEnd, top;
    if (position == 1) {
      topStart = 95;
    } else {
      topStart = $("#card>.pace-last-box").eq(position - 2).offset().top + $("#card>.pace-last-box").eq(position - 2).outerHeight();
      $("#card>.pace-last-box").eq(position - 2).addClass("border-red");
    }
    topEnd = $("#card>.pace-last-box").eq(position - 1).offset().top + $("#card>.pace-last-box").eq(position - 1).outerHeight();
    top = Math.round((topStart + topEnd) / 2) - 120;
    $("body,html").scrollTop(topStart - 100);
    $(".order-div").html('<div class="order-num">' + position + '</div>');
    $(".order-div>.order-num").css("top", top).parent().show();
    $("#card>.pace-last-box").eq(position - 1).addClass("border-red");
    position = -1;
  }
}

/**
 * 设置内容
 * @param curContent
 * @param content
 */
function setContentData(curContent, content) {
  var _html = "";
  var width = 0,
    length = 0,
    len = 0;
  switch (content.questionType) {
    case 1: //单选题
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".option-ul :radio").attr("name", "radio_" + index);
      _html = "";
      $(curContent).find(".show .question").html(content.question);
      if (content.optionA != null && content.optionA != "") {
        _html += '<li><label><input name="input_' + index + '" type="radio" value="A" ';
        _html += content.correctOption == "A" ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>A.' + content.optionA + '</label></li>';
      }
      if (content.optionB != null && content.optionB != "") {
        _html += '<li><label><input name="input_' + index + '" type="radio" value="B" ';
        _html += content.correctOption == "B" ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>B.' + content.optionB + '</label></li>';
      }
      if (content.optionC != null && content.optionC != "") {
        _html += '<li><label><input name="input_' + index + '" type="radio" value="C" ';
        _html += content.correctOption == "C" ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>C.' + content.optionC + '</label></li>';
      }
      if (content.optionD != null && content.optionD != "") {
        _html += '<li><label><input name="input_' + index + '" type="radio" value="D" ';
        _html += content.correctOption == "D" ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>D.' + content.optionD + '</label></li>';
      }
      $(curContent).find(".show .option").html(_html);
      _html = "";
      if (content.wrongTips1 != null && content.wrongTips1 != "") {
        _html += "1." + content.wrongTips1 + "<br>";
      }
      if (content.wrongTips2 != null && content.wrongTips2 != "") {
        _html += "2." + content.wrongTips2 + "<br>";
      }
      if (content.wrongTips3 != null && content.wrongTips3 != "") {
        _html += "3." + content.wrongTips3 + "<br>";
      }
      if (_html == "") {
        $(curContent).find(".show .tips").hide();
      } else {
        $(curContent).find(".show .tips>.tips-parse-text").html(_html);
      }
      if (content.parse == null || content.parse == "") {
        $(curContent).find(".show .parse").hide();
      } else {
        $(curContent).find(".show .parse>.tips-parse-text").html(content.parse);
      }
      break;
    case 2: //多选题
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show .question").html(content.question);
      _html = "";
      if (content.optionA != null && content.optionA != "") {
        _html += '<li><label><input name="input_' + index + '" type="checkbox" value="A" ';
        _html += content.correctOption.indexOf("A") >= 0 ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>A.' + content.optionA + '</label></li>';
      }
      if (content.optionB != null && content.optionB != "") {
        _html += '<li><label><input name="input_' + index + '" type="checkbox" value="B" ';
        _html += content.correctOption.indexOf("B") >= 0 ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>B.' + content.optionB + '</label></li>';
      }
      if (content.optionC != null && content.optionC != "") {
        _html += '<li><label><input name="input_' + index + '" type="checkbox" value="C" ';
        _html += content.correctOption.indexOf("C") >= 0 ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>C.' + content.optionC + '</label></li>';
      }
      if (content.optionD != null && content.optionD != "") {
        _html += '<li><label><input name="input_' + index + '" type="checkbox" value="D" ';
        _html += content.correctOption.indexOf("D") >= 0 ? 'checked="checked" ' : '';
        _html += 'disabled="disabled"/>D.' + content.optionD + '</label></li>';
      }
      $(curContent).find(".show .option").html(_html);
      _html = "";
      if (content.wrongTips1 != null && content.wrongTips1 != "") {
        _html += "1." + content.wrongTips1 + "<br>";
      }
      if (content.wrongTips2 != null && content.wrongTips2 != "") {
        _html += "2." + content.wrongTips2 + "<br>";
      }
      if (content.wrongTips3 != null && content.wrongTips3 != "") {
        _html += "3." + content.wrongTips3 + "<br>";
      }
      if (_html == "") {
        $(curContent).find(".show .tips").hide();
      } else {
        $(curContent).find(".show .tips>.tips-parse-text").html(_html);
      }
      if (content.parse == null || content.parse == "") {
        $(curContent).find(".show .parse").hide();
      } else {
        $(curContent).find(".show .parse>.tips-parse-text").html(content.parse);
      }
      break;
    case 3: //填空题
    case 7: //点填题
    case 8: //点选题
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      var questionArr = content.question.split(/_{3,}/);
      var answer = "";
      _html = "";
      for (i = 0; i < questionArr.length; i++) {
        _html += questionArr[i];
        if (i >= questionArr.length - 1) {
          break;
        }
        switch (i) {
          case 0:
            answer = content.optionA;
            break;
          case 1:
            answer = content.optionB;
            break;
          case 2:
            answer = content.optionC;
            break;
          case 3:
            answer = content.optionD;
            break;
          case 4:
            answer = content.optionE;
            break;
        }
        len = answer.length;
        if (chinese.test(answer)) {
          len += answer.match(chinese).length;
        }
        width = (len + 2) * 8;
        if (width < 80) {
          width = 80;
        }
        if (content.questionType == 3) {
          _html += '<input class="border" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
        } else {
          _html += '<input class="input-line" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
        }
      }
      $(curContent).find(".show .question").html(_html == "" ? "<span style='color:red;'>空</span>" : _html);
      var _html = "";
      if (content.wrongTips1 != null && content.wrongTips1 != "") {
        _html += "1." + content.wrongTips1 + "<br>";
      }
      if (content.wrongTips2 != null && content.wrongTips2 != "") {
        _html += "2." + content.wrongTips2 + "<br>";
      }
      if (content.wrongTips3 != null && content.wrongTips3 != "") {
        _html += "3." + content.wrongTips3 + "<br>";
      }
      if (_html == "") {
        $(curContent).find(".show .tips").hide();
      } else {
        $(curContent).find(".show .tips>.tips-parse-text").html(_html);
      }
      if (content.parse == null || content.parse == "") {
        $(curContent).find(".show .parse").hide();
      } else {
        $(curContent).find(".show .parse>.tips-parse-text").html(content.parse);
      }
      break;
    case 4: //文本
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).removeClass("selected").attr("data_i", index);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show .question").html(content.question);
      break;
    case 5: //选填题
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      //		var question = content.question.replace(/<p[^>]*>/g,"");
      //		question = question.replace(/<\/p>/g,"<br>");
      var question = content.question;
      var questionArr = question.split(/_{3,}/);
      var optionStr, optionArr, answer;
      var answerArr = content.correctOption.split(",");
      for (var i = 0; i < questionArr.length - 1; i++) {
        if (i == 0) {
          optionStr = content.optionA;
        } else if (i == 1) {
          optionStr = content.optionB;
        } else if (i == 2) {
          optionStr = content.optionC;
        } else if (i == 3) {
          optionStr = content.optionD;
        } else if (i == 4) {
          optionStr = content.optionE;
        } else {
          break;
        }
        answer = parseInt(answerArr[i]);
        len = 0;
        optionArr = optionStr.split("|");
        for (var j = 0; j < optionArr.length; j++) {
          length = optionArr[j].length;
          if (chinese.test(optionArr[j])) {
            length += optionArr[j].match(chinese).length;
          }
          if (len < length) {
            len = length;
          }
        }
        width = len * 9 + 20;
        if (width < 80) {
          width = 80;
        }
        _select = "<div class='question-select'>";
        _select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + $.trim(optionArr[answer]) + "</h3>";
        _select += "<ul>";
        $(optionArr).each(function (j, option) {
          if (option != null && option != "") {
            _select += "<li style='width:" + width + "px'>" + option + "</li>";
          }
        });
        _select += "</ul></div>";
        question = question.replace(/_{3,}/, _select);
      }
      $(curContent).find(".show .question").html(question);
      var _html = "";
      if (content.wrongTips1 != null && content.wrongTips1 != "") {
        _html += "1." + content.wrongTips1 + "<br>";
      }
      if (content.wrongTips2 != null && content.wrongTips2 != "") {
        _html += "2." + content.wrongTips2 + "<br>";
      }
      if (content.wrongTips3 != null && content.wrongTips3 != "") {
        _html += "3." + content.wrongTips3 + "<br>";
      }
      if (_html == "") {
        $(curContent).find(".show .tips").hide();
      } else {
        $(curContent).find(".show .tips>.tips-parse-text").html(_html);
      }
      if (content.parse == null || content.parse == "") {
        $(curContent).find(".show .parse").hide();
      } else {
        $(curContent).find(".show .parse>.tips-parse-text").html(content.parse);
      }
      break;
    case 6: //综合题
      $(curContent).find(".check-item :checkbox").val("content_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      //		var question = content.question.replace(/<p[^>]*>/g,"");
      //		question = question.replace(/<\/p>/g,"<br>");
      var question = content.question;
      var questionArr = question.split(/_{3,}/);
      var optionStr, optionArr, answer;
      var answerArr = content.correctOption.split(",");
      var _html = '';
      for (var i = 0; i < questionArr.length - 1; i++) {
        _html += questionArr[i];
        if (i >= questionArr.length - 1) {
          break;
        }
        if (i == 0) {
          optionStr = content.optionA;
        } else if (i == 1) {
          optionStr = content.optionB;
        } else if (i == 2) {
          optionStr = content.optionC;
        } else if (i == 3) {
          optionStr = content.optionD;
        } else if (i == 4) {
          optionStr = content.optionE;
        } else {
          break;
        }
        answer = parseInt(answerArr[i]);
        len = 0;
        optionArr = optionStr.split("|");
        for (var j = 0; j < optionArr.length; j++) {
          length = optionArr[j].length;
          if (chinese.test(optionArr[j])) {
            length += optionArr[j].match(chinese).length;
          }
          if (len < length) {
            len = length;
          }
        }
        width = len * 9 + 20;
        if (width < 80) {
          width = 80;
        }
        if (optionArr.length > 1) { //选填
          _select = "<div class='question-select'>";
          _select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + $.trim(optionArr[answer]) + "</h3>";
          _select += "<ul>";
          $(optionArr).each(function (j, option) {
            if (option != null && option != "") {
              _select += "<li style='width:" + width + "px'>" + option + "</li>";
            }
          });
          _select += "</ul></div>";
        } else { //填空
          _select = '<input class="border" style="width:' + width + 'px;" value="' + optionArr[0] + '" disabled="disabled"/>';
        }
        _html += _select;
      }
      $(curContent).find(".show .question").html(_html);
      var _html = "";
      if (content.wrongTips1 != null && content.wrongTips1 != "") {
        _html += "1." + content.wrongTips1 + "<br>";
      }
      if (content.wrongTips2 != null && content.wrongTips2 != "") {
        _html += "2." + content.wrongTips2 + "<br>";
      }
      if (content.wrongTips3 != null && content.wrongTips3 != "") {
        _html += "3." + content.wrongTips3 + "<br>";
      }
      if (_html == "") {
        $(curContent).find(".show .tips").hide();
      } else {
        $(curContent).find(".show .tips>.tips-parse-text").html(_html);
      }
      if (content.parse == null || content.parse == "") {
        $(curContent).find(".show .parse").hide();
      } else {
        $(curContent).find(".show .parse>.tips-parse-text").html(content.parse);
      }
      break;
    case 100: //分割线
      break;
    case 101: //一级标题
      $(curContent).find(".check-item :checkbox").val("title_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show h2").text(content.question);
      break;
    case 102: //二级标题
      $(curContent).find(".check-item :checkbox").val("title_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show").text(content.question);
      break;
    case 103: //三级标题
      $(curContent).find(".check-item :checkbox").val("title_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show").text(content.question);
      break;
    case 104: //四级标题
      $(curContent).find(".check-item :checkbox").val("subtitle_" + content.id);
      $(curContent).children(".edit,.show").toggle();
      $(curContent).find(".show").text("【" + content.question + "】");
      break;
  }
  index++;
}

/********************************编辑卡片部分*************************************/
/**
 * 添加模板标题
 */
function addExampleTitle(obj) {
  var type = parseInt($(obj).parent().attr("data"));
  addTemplate(type);
  var title = $(obj).text();
  $("#card .selected").children().toggle();
  $("#card .selected").find(".edit input[name='title']").val(title);
  switch (type) {
    case 101:
      $("#card .selected").find(".show h2").text(title);
      break;
    case 102:
    case 103:
      $("#card .selected").find(".show").text(title);
      break;
    case 104:
      $("#card .selected").find(".show").text("【" + title + "】");
      break;
  }
  showBtnDiv();
}


var editorPrefix = "editor_";

var index = 0;
var CKArr = new Array(); //存放editor对象的map
var spaceIndexName = ["第一空：", "第二空：", "第三空：", "第四空：", "第五空："];
/**
 * 添加模板
 * @param type
 */
function addTemplate(type) {
  var editorId = editorPrefix + index;
  var template = getTemplate(type);
  switch (type) {
    case 100: //分割线
    case 101: //一级标题
    case 102: //二级标题
    case 103: //三级标题
    case 104: //四级标题
      if ($("#card .selected").length > 0) {
        $("#card .selected").removeClass("selected").after(template);
      } else {
        $("#card").append(template);
      }
      showBtnDiv();
      break;
    case 1: //单选题
    case 2: //多选题
    case 3: //填空题
    case 4: //文本
    case 5: //选填题
    case 6: //综合题
    case 7: //点填题
    case 8: //点选题
      if ($("#card .selected").length > 0) {
        $("#card .selected").removeClass("selected").after(template);
      } else {
        $("#card").append(template);
      }
      $("#card .selected").find(".ckeditor").attr("id", editorId);
      var editor = CKEDITOR.replace(editorId);
      CKArr[editorId] = editor;
      editor.on("instanceReady", function () {
        $("#cke_" + editorId).find(".cke_button__textcolor").mouseenter(function () {
          this.click();
        });
      })
      showBtnDiv();
      if (type == 1) {
        $("#card .selected").find(".option-ul :radio").attr("name", "radio_" + index);
      } else if (type == 5 || type == 9) {
        editor.on("change", function () {
          addOption(this, editor.document.getBody().getText());
        });
      }
      break;
  }
  getInteNum();
  //对新添加的模块进行定位
  var top = $("#card .selected").offset().top - 300;
  if (top < 0) {
    top = 0;
  }
  $(window).scrollTop(top);
  index++;
}

/**
 * 增加选填题备选项输入框
 */
function addOption(curEditor, text) {
  var spaceNum = text.match(/_{3,}/g) == null ? 0 : text.match(/_{3,}/g).length;
  spaceNum = spaceNum > 5 ? 5 : spaceNum;
  var editor = $(curEditor.container.$.parentElement.parentElement.parentElement);
  var optionNum = editor.find(".option-ul>li").length;
  if (optionNum < spaceNum) {
    var _html = "";
    for (var i = 0; i < (spaceNum - optionNum); i++) {
      _html += '<li>' +
        '<span class="space-index">' + spaceIndexName[optionNum + i] + '</span>' +
        'A.<input name="optionA" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);"/>' +
        'B.<input name="optionB" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);"/>' +
        'C.<input name="optionC" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);"/>' +
        'D.<input name="optionD" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);"/>' +
        '正确答案：' +
        '<select class="select-answer" disabled="disabled">' +
        '<option value="-1">请选择</option>' +
        '<option value="0">A</option>' +
        '<option value="1">B</option>' +
        '<option value="2">C</option>' +
        '<option value="3">D</option>' +
        '</select>' +
        '<label class="delete-symbol" onclick="deleteOption(this);">×</label>' +
        '</li>';
    }
    editor.find(".option-ul").append(_html);
    editor.find(".delete-symbol").hide();
  } else if (optionNum > spaceNum) { //选项比空多，则允许删除指定的选项
    editor.find(".delete-symbol").show();
  } else {
    editor.find(".delete-symbol").hide();
  }
}

function checkInput(obj) {
  var num = 0;
  $(obj).parent().children(".select-option").each(function (i, item) {
    if ($.trim($(item).val()) != "") {
      num++;
    }
  });
  if (num > 1) {
    $(obj).siblings(".select-answer").prop("disabled", false);
  } else {
    $(obj).siblings(".select-answer").val("-1").prop("disabled", true);
  }
}

/**
 * 删除选填题的备选项输入框
 * @param obj
 */
function deleteOption(obj) {
  var editorId = $("#card .selected .edit textarea").attr("id");
  var text = $.trim(CKArr[editorId].document.getBody().getText());
  var spaceNum = text.match(/_{3,}/g) == null ? 0 : text.match(/_{3,}/g).length;
  spaceNum = spaceNum > 5 ? 5 : spaceNum;
  var optionUl = $(obj).parents(".option-ul");
  if ($(optionUl).children("li").length > spaceNum) {
    $(obj).parent().remove();
    $(optionUl).children("li").each(function (i, item) {
      $(item).children(".space-index").text(spaceIndexName[i]);
    });
  }
  if ($(optionUl).children("li").length <= spaceNum) {
    $(optionUl).find(".delete-symbol").hide();
  }
}


/**
 * 获取内容模板
 */
function getTemplate(type) {
  switch (type) {
    case 1: //单选题
      return $("#singleTemp").html();
    case 2: //多选题
      return $("#multiTemp").html();
    case 3: //填空题
      return $("#completionTemp").html();
    case 4: //文本
      return $("#textTemp").html();
    case 5: //选填题
      return $("#selectionTemp").html();
    case 6: //综合题
      return $("#synthesisTemp").html();
    case 7: //点填题
      return $("#clickCompletionTemp").html();
    case 8: //点选题
      return $("#clickSelectTemp").html();
    case 100: //分割线
      return $("#cutLineTemp").html();
    case 101: //一级标题
      return $("#firstTitleTemp").html();
    case 102: //二级标题
      return $("#secondTitleTemp").html();
    case 103: //三级标题
      return $("#thirdTitleTemp").html();
    case 104: //四级标题
      return $("#fourthTitleTemp").html();
  }
}

/**
 * 计算当前有多少张卡片
 */
function getInteNum() {
  var inteNum = $("#card").children(".text,.single-choice,.multi-choice,.completion,.selection").length;
  $("#inteNum").text(inteNum);
}

/**
 * 保存
 */
var numArr = ["一", "二", "三", "四", "五"];

function save() {
  if ($("#card .selected").length == 0) {
    alert("保存出错！");
    return;
  }

  var curIndex = $("#card>div").index($("#card .selected"));
  var content = $("#card .selected");
  var type = parseInt($(content).attr("data"));


  switch (type) {
    case 101: //一级标题
    case 102: //二级标题
    case 103: //三级标题
    case 104: //四级标题
      var title = $.trim($(content).find(".edit input[name='title']").val());
      if (title == null || title == "") {
        alert("请输入标题内容！");
        return;
      }
      if (type == 101) {
        $(content).find(".show h2").text(title);
      } else if (type == 102) {
        $(content).find(".show").text(title);
      } else if (type == 103) {
        $(content).find(".show").text(title);
      } else if (type == 104) {
        $(content).find(".show").text("【" + title + "】");
      }
      break;
    case 4: //文本
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入文本内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      $(content).find(".show .question").html(question); //带格式内容
      break;
    case 1: //单选题
      //获取题干
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入题干内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      $(content).find(".show .question").html(question);
      //获取选项
      var option, letter;
      var _html = "";
      $(content).find(".edit .input-option").each(function (i, item) {
        switch (i) {
          case 0:
            letter = "A";
            break;
          case 1:
            letter = "B";
            break;
          case 2:
            letter = "C";
            break;
          case 3:
            letter = "D";
            break;
        }
        option = $.trim($(item).val());
        if (option != null && option != "") {
          noOption = false;
          _html += '<li><label><input name="input_' + curIndex + '" type="radio" value="' + letter + '" disabled="disabled"/>' + letter + '.' + option + '</label></li>';
        }
      });
      if (noOption) {
        alert("请输入至少一个备选项！");
        return;
      }
      $(content).find(".show .option").html(_html);
      correctOption = $(content).find(".edit :radio:checked").val();
      if (correctOption == null) {
        alert("请指示出正确答案！");
        return;
      } else {
        option = $(content).find(".edit :radio:checked").parent().siblings("input").val();
        if (option == null || $.trim(option) == "") {
          alert("空答案不能为正确答案！");
          return;
        }
      }
      $(content).find(".show .option input[value='" + correctOption + "']").prop("checked", true);
      //获取错误提示
      var tips = "";
      var num = 1;
      $(content).find(".edit .wrongTips input").each(function (i, item) {
        if ($.trim($(item).val()) != "") {
          tips += num + "." + $.trim($(item).val()) + "<br>";
          num++;
        }
      });
      if (tips == "") {
        $(content).find(".show .tips").hide();
      } else {
        $(content).find(".show .tips").show().children(".tips-parse-text").html(tips);
      }
      //获取解析
      var parse = $.trim($(content).find(".edit .input-parse").val());
      if (parse == null || parse == "") {
        $(content).find(".show .parse").hide();
      } else {
        $(content).find(".show .parse").show().children(".tips-parse-text").text(parse);
      }
      break;
    case 2: //多选题
      //获取题干
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入题干内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      $(content).find(".show .question").html(question);
      //获取选项
      var option, letter;
      var noOption = true; //没有选项
      var _html = "";
      $(content).find(".edit .input-option").each(function (i, item) {
        switch (i) {
          case 0:
            letter = "A";
            break;
          case 1:
            letter = "B";
            break;
          case 2:
            letter = "C";
            break;
          case 3:
            letter = "D";
            break;
        }
        option = $.trim($(item).val());
        if (option != null && option != "") {
          noOption = false;
          _html += '<li><label><input type="checkbox" value="' + letter + '" disabled="disabled"/>' + letter + '.' + option + '</label></li>';
        }
      });
      if (noOption) {
        alert("请输入至少一个备选项！");
        return;
      } else {
        $(content).find(".show .option").html(_html);
      }
      //获取正确答案
      if ($(content).find(".edit :checkbox:checked").length == 0) {
        alert("请指示出正确答案！");
        return;
      }
      $(content).find(".edit :checkbox:checked").each(function (i, item) {
        if ($.trim($(item).parent().siblings("input").val()) == "") {
          alert("空答案不能为正确答案！");
          return;
        }
        $(content).find(".show .option input[value='" + $(item).val() + "']").prop("checked", true);
      });
      //获取错误提示
      var tips = "";
      var num = 1;
      $(content).find(".edit .wrongTips input").each(function (i, item) {
        if ($.trim($(item).val()) != "") {
          tips += num + "." + $.trim($(item).val()) + "<br>";
          num++;
        }
      });
      if (tips == "") {
        $(content).find(".show .tips").hide();
      } else {
        $(content).find(".show .tips").show().children(".tips-parse-text").html(tips);
      }
      //获取解析
      var parse = $.trim($(content).find(".edit .input-parse").val());
      if (parse == null || parse == "") {
        $(content).find(".show .parse").hide();
      } else {
        $(content).find(".show .parse").show().children(".tips-parse-text").text(parse);
      }
      break;
    case 3: //填空题
    case 7: //点填题
    case 8: //点选题
      //获取题干和答案
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入题干内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      var questionArr = question.split(/_{3,}/);
      var questionHtml = "";
      var width = 0,
        len = 0;
      var answer = "";
      for (var i = 0; i < questionArr.length; i++) {
        questionHtml += questionArr[i];
        if (i >= questionArr.length - 1) {
          break;
        }
        answer = $.trim($(content).find(".edit .input-option").eq(i).val());
        if (answer == "") {
          alert("答案不符合要求！");
          return;
        }
        len = answer.length;
        if (chinese.test(answer)) {
          len += answer.match(chinese).length;
        }
        width = (len + 2) * 8;
        if (width < 80) {
          width = 80;
        }
        if (type == 3) {
          questionHtml += '<input class="border" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
        } else {
          questionHtml += '<input class="input-line" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
        }
      }
      $(content).find(".show .question").html(questionHtml);
      //获取错误提示
      var tips = "";
      var num = 1;
      $(content).find(".edit .wrongTips input").each(function (i, item) {
        if ($.trim($(item).val()) != "") {
          tips += num + "." + $.trim($(item).val()) + "<br>";
          num++;
        }
      });
      if (tips == "") {
        $(content).find(".show .tips").hide();
      } else {
        $(content).find(".show .tips").show().children(".tips-parse-text").html(tips);
      }
      //获取解析
      var parse = $.trim($(content).find(".edit .input-parse").val());
      if (parse == null || parse == "") {
        $(content).find(".show .parse").hide();
      } else {
        $(content).find(".show .parse").show().children(".tips-parse-text").text(parse);
      }
      break;
    case 5: //选填题
      //获取题干
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入题干内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      //		question = question.replace(/<p[^>]*>/g,"");
      //		question = question.replace(/<\/p>/g,"<br>");
      var questionArr = question.split(/_{3,}/);
      if (questionArr.length <= 1) {
        alert("请给该题设置题空！");
        return;
      }
      //var optionNum = $(content).find(".option-ul").children("li").length;
      var optionUl = $(content).find(".option-ul");
      var curOption;
      var option, optionArr = new Array(),
        answer;
      var width = 0,
        len = 0,
        length = 0;
      var noOption = true,
        emptyIsAnswer = false; //没有选项
      var _select = "";
      for (var i = 0; i < questionArr.length - 1; i++) {
        if (i >= 5) {
          break;
        }
        noOption = true;
        emptyIsAnswer = false;
        curOption = $(optionUl).children("li:eq(" + i + ")");
        answer = parseInt($(curOption).find(".select-answer").val());
        if (answer < 0) {
          alert("在第" + numArr[i] + "空中，请指出正确答案！");
          return;
        }
        optionArr.length = 0;
        $(curOption).find(".select-option").each(function (j, item) {
          option = $.trim($(item).val());
          if (option != null && option != "") {
            noOption = false;
            length = option.length;
            if (chinese.test(option)) {
              length += option.match(chinese).length;
            }
            if (len < length) {
              len = length;
            }
          } else if (answer == j) {
            emptyIsAnswer = true;
            return false;
          }
          optionArr.push(option);
        });
        if (noOption) {
          alert("在第" + numArr[i] + "空中，请输入至少一个备选项！");
          return;
        }
        if (emptyIsAnswer) {
          alert("在第" + numArr[i] + "空中，空选项不能为正确答案！");
          return;
        }
        width = len * 9 + 20;
        if (width < 80) {
          width = 80;
        }
        _select = "<div class='question-select'>";
        _select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + $.trim(optionArr[answer]) + "</h3>";
        _select += "<ul>";
        $(optionArr).each(function (j, option) {
          if (option != null && option != "") {
            _select += "<li style='width:" + width + "px'>" + option + "</li>";
          }
        });
        _select += "</ul></div>";
        question = question.replace(/_{3,}/, _select);
      }
      $(content).find(".show .question").html(question);
      //获取错误提示
      var tips = "";
      var num = 1;
      $(content).find(".edit .wrongTips input").each(function (i, item) {
        if ($.trim($(item).val()) != "") {
          tips += num + "." + $.trim($(item).val()) + "<br>";
          num++;
        }
      });
      if (tips == "") {
        $(content).find(".show .tips").hide();
      } else {
        $(content).find(".show .tips").show().children(".tips-parse-text").html(tips);
      }
      //获取解析
      var parse = $.trim($(content).find(".edit .input-parse").val());
      if (parse == null || parse == "") {
        $(content).find(".show .parse").hide();
      } else {
        $(content).find(".show .parse").show().children(".tips-parse-text").text(parse);
      }
      break;
    case 6: //综合题
      //获取题干
      var editorId = $(content).find(".edit textarea").attr("id");
      if ($.trim(CKArr[editorId].document.getBody().getText()) == "") { //不带格式内容
        alert("请输入题干内容！");
        return;
      }
      var question = CKArr[editorId].getData();
      question = question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      question = question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      //		question = question.replace(/<p[^>]*>/g,"");
      //		question = question.replace(/<\/p>/g,"<br>");
      var questionArr = question.split(/_{3,}/);
      if (questionArr.length <= 1) {
        alert("请给该题设置题空！");
        return;
      }
      var optionUl = $(content).find(".option-ul");
      var curOption;
      var option, optionArr = new Array(),
        answer;
      var width = 0,
        len = 0,
        length = 0,
        optionNum = 0;
      var emptyIsAnswer = false; //没有选项
      var questionHtml = "";
      var _select = "";
      for (var i = 0; i < questionArr.length; i++) {
        questionHtml += questionArr[i];
        if (i >= questionArr.length - 1) {
          break;
        }
        if (i >= 5) {
          continue;
        }
        emptyIsAnswer = false;
        curOption = $(optionUl).children("li:eq(" + i + ")");
        answer = parseInt($(curOption).find(".select-answer").val());
        optionArr.length = 0;
        optionNum = 0;
        $(curOption).find(".select-option").each(function (j, item) {
          option = $.trim($(item).val());
          if (option != null && option != "") {
            length = option.length;
            if (chinese.test(option)) {
              length += option.match(chinese).length;
            }
            if (len < length) {
              len = length;
            }
            optionNum++;
          } else if (answer == j) {
            emptyIsAnswer = true;
            return false;
          }
          optionArr.push(option);
        });
        if (optionNum < 1) {
          alert("在第" + numArr[i] + "空中，请输入至少一个备选项！");
          return;
        }
        if (optionNum > 1 && answer < 0) {
          alert("在第" + numArr[i] + "空中，请指出正确答案！");
          return;
        }
        if (optionNum > 1 && emptyIsAnswer) {
          alert("在第" + numArr[i] + "空中，空选项不能为正确答案！");
          return;
        }
        width = len * 9 + 20;
        if (width < 80) {
          width = 80;
        }
        if (optionNum > 1) { //选填
          _select = "<div class='question-select'>";
          _select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + $.trim(optionArr[answer]) + "</h3>";
          _select += "<ul>";
          $(optionArr).each(function (j, option) {
            if (option != null && option != "") {
              _select += "<li style='width:" + width + "px'>" + option + "</li>";
            }
          });
          _select += "</ul></div>";
        } else { //填空
          $(optionArr).each(function (j, option) {
            if (option != null && option != "") {
              _select = '<input class="border" style="width:' + width + 'px;" value="' + option + '" disabled="disabled"/>';
              return false;
            }
          });
        }
        questionHtml += _select;
      }
      $(content).find(".show .question").html(questionHtml);
      //获取错误提示
      var tips = "";
      var num = 1;
      $(content).find(".edit .wrongTips input").each(function (i, item) {
        if ($.trim($(item).val()) != "") {
          tips += num + "." + $.trim($(item).val()) + "<br>";
          num++;
        }
      });
      if (tips == "") {
        $(content).find(".show .tips").hide();
      } else {
        $(content).find(".show .tips").show().children(".tips-parse-text").html(tips);
      }
      //获取解析
      var parse = $.trim($(content).find(".edit .input-parse").val());
      if (parse == null || parse == "") {
        $(content).find(".show .parse").hide();
      } else {
        $(content).find(".show .parse").show().children(".tips-parse-text").text(parse);
      }
      break;
  }
  $(content).children(".edit,.show").toggle();
  showBtnDiv();

  $('#card>.selected .option input').iCheck({
    checkboxClass: 'icheckbox_minimal-green',
    radioClass: 'iradio_minimal-grey',
    increaseArea: '20%' // optional
  });
  $("#card>.selected .option>li>label:last").css("margin-bottom", "0");
  $(content).find(".question-select").bind("click", function (e) {
    e.stopPropagation();
    $(this).toggleClass("select-clicked").children("ul").toggle();
  });
  isChanged = true;
  $("#save-card").attr("class", "button-yellow rounded right");
}

/**
 * 编辑
 */
function edit() {
  if ($("#card .selected").length == 0) {
    alert("编辑出错！");
    return;
  }
  var curContent = $("#card .selected");
  $(curContent).children(".edit,.show").toggle();
  var editorDiv = $(curContent).children(".edit");
  $(editorDiv).find("input[name='title']").focus().select();
  var index = $(curContent).attr("data_i");
  var edited = $(editorDiv).attr("data");
  var editorId = editorPrefix + index;
  if (index && !edited) {
    var content = grammarContentArr[index];
    $(editorDiv).attr("data", "edited");
    switch (content.questionType) {
      case 101: //一级标题
      case 102: //二级标题
      case 103: //三级标题
      case 104: //四级标题
        $(editorDiv).find("input").val(content.question).focus().select();
        break;
      case 4: //文本
        $(editorDiv).find("textarea").attr("id", editorId);
        var editor = CKEDITOR.replace(editorId);
        CKArr[editorId] = editor;
        editor.setData(content.question);
        break;
      case 1: //单选题
      case 2: //多选题
      case 3: //填空题
      case 7: //点填题
      case 8: //点选题
        //回显题干
        $(editorDiv).find("textarea").attr("id", editorId);
        var editor = CKEDITOR.replace(editorId);
        CKArr[editorId] = editor;
        editor.setData(content.question);
        //回显选项（答案）
        if (content.optionA != null) {
          $(editorDiv).find("input[name='optionA']").val(content.optionA);
        }
        if (content.optionB != null) {
          $(editorDiv).find("input[name='optionB']").val(content.optionB);
        }
        if (content.optionC != null) {
          $(editorDiv).find("input[name='optionC']").val(content.optionC);
        }
        if (content.optionD != null) {
          $(editorDiv).find("input[name='optionD']").val(content.optionD);
        }
        if (content.optionE != null) {
          $(editorDiv).find("input[name='optionE']").val(content.optionE);
        }
        //回显错题提示和解析
        $(editorDiv).find(".wrongTips input:eq(0)").val(content.wrongTips1);
        $(editorDiv).find(".wrongTips input:eq(1)").val(content.wrongTips2);
        $(editorDiv).find(".wrongTips input:eq(2)").val(content.wrongTips3);
        if (content.parse != null) {
          $(editorDiv).find(".input-parse").val(content.parse);
        }
        //回显选择题正确选项
        if (content.questionType == 1 || content.questionType == 5) {
          $(editorDiv).find("input[type='radio'][value='" + content.correctOption + "']").prop("checked", true);
        } else if (content.questionType == 2) {
          $(content.correctOption.split(",")).each(function (i, item) {
            $(editorDiv).find("input:checkbox[value='" + item + "']").prop("checked", true);
          });
        }
        break;
      case 5: //选填题
      case 6: //综合题
        //回显题干
        $(editorDiv).find("textarea").attr("id", editorId);
        var editor = CKEDITOR.replace(editorId);
        CKArr[editorId] = editor;
        editor.setData(content.question);
        //回显选项（答案）
        var optionStr, optionArr, answer;
        var answerArr = content.correctOption.split(",");
        var _html = "";
        for (var i = 0; i < 5; i++) {
          switch (i) {
            case 0:
              optionStr = content.optionA;
              break;
            case 1:
              optionStr = content.optionB;
              break;
            case 2:
              optionStr = content.optionC;
              break;
            case 3:
              optionStr = content.optionD;
              break;
            case 4:
              optionStr = content.optionE;
              break;
          }
          if (optionStr != null && optionStr != "") {
            optionArr = optionStr.split("|");
            answer = parseInt(answerArr[i]);
            _html += '<li>' +
              '<span class="space-index">' + spaceIndexName[i] + '</span>' +
              'A.<input name="optionA" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);" value="' + (optionArr[0] == null ? "" : optionArr[0]) + '"/>' +
              'B.<input name="optionB" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);" value="' + (optionArr[1] == null ? "" : optionArr[1]) + '"/>' +
              'C.<input name="optionC" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);" value="' + (optionArr[2] == null ? "" : optionArr[2]) + '"/>' +
              'D.<input name="optionD" class="select-option" type="text" maxlength="64" onkeyup="checkInput(this);" value="' + (optionArr[3] == null ? "" : optionArr[3]) + '"/>' +
              '正确答案：' +
              '<select class="select-answer"' + (optionArr.length == 1 ? ' disabled="disabled"' : '') + '>' +
              '<option value="-1">请选择</option>' +
              '<option value="0" ' + (answer == 0 ? 'selected="selected"' : '') + '>A</option>' +
              '<option value="1" ' + (answer == 1 ? 'selected="selected"' : '') + '>B</option>' +
              '<option value="2" ' + (answer == 2 ? 'selected="selected"' : '') + '>C</option>' +
              '<option value="3" ' + (answer == 3 ? 'selected="selected"' : '') + '>D</option>' +
              '</select>' +
              '<label class="delete-symbol" onclick="deleteOption(this);">×</label>' +
              '</li>';
          }
        }
        $(editorDiv).find(".option-ul").html(_html);
        //回显错题提示和解析
        $(editorDiv).find(".wrongTips input:eq(0)").val(content.wrongTips1);
        $(editorDiv).find(".wrongTips input:eq(1)").val(content.wrongTips2);
        $(editorDiv).find(".wrongTips input:eq(2)").val(content.wrongTips3);
        if (content.parse != null) {
          $(editorDiv).find(".input-parse").val(content.parse);
        }
        editor.on("change", function () { //添加编辑器监听事件
          addOption(this, editor.document.getBody().getText());
        });
        break;
    }
    if (CKArr[editorId] != null) {
      CKArr[editorId].on("instanceReady", function () {
        $("#cke_" + editorId).find(".cke_button__textcolor").mouseenter(function () {
          this.click();
        });
      })
    }
  }
  showBtnDiv();
}

/**
 * 删除
 */
function deleteContent() {
  if ($("#card .selected").length == 0) {
    alert("删除出错！");
    return;
  }
  var d2 = dialog({
    title: "删除操作",
    content: "你确定要删除选中的内容吗？<span style='color:red;'>删除后无法恢复！</span>",
    okValue: "确认并删除",
    cancelValue: "我再想想",
    ok: function () {
      //直接删除内容
      var _index = $("#card .selected").attr("data_i");
      if (_index != null && _index != "") {
        var _questionType = parseInt($("#card .selected").attr("data"));
        var _id = grammarContentArr[parseInt(_index)].id;
        if (_questionType > 0 && _id > 0) {
          $.ajax({
            type: "POST",
            async: false,
            url: "/lexueKnowledge/content/deleteContent",
            data: {
              "articleId": unitId.articleId,
              "questionType": _questionType,
              "contentId": _id
            },
            dataType: "json",
            success: function (data) {
              $("#card .selected").remove();
              showBtnDiv();
              getInteNum();
              isChanged = true;
              $("#save-card").attr("class", "button-yellow rounded right");
            }
          });
          $("#card .selected").remove();
          showBtnDiv();
          getInteNum();
          isChanged = true;
          $("#save-card").attr("class", "button-yellow rounded right");
        } else {
          console.log("???1111")
        }
      } else {
        console.log("???2222", _index)
        $("#card .selected").remove();
        showBtnDiv();
        getInteNum();
        isChanged = true;
        $("#save-card").attr("class", "button-yellow rounded right");

      }
    },
    cancel: true
  });
  d2.showModal();
}

/**
 * 上移
 */
function moveUp() {
  if ($("#card .selected").length == 0) {
    alert("移动出错！");
    return;
  }
  var content = $("#card .selected");
  var prev = $(content).prev();
  $(content).after($(prev).clone());
  $(prev).remove();
  showBtnDiv();
  isChanged = true;
  $("#save-card").attr("class", "button-yellow rounded right");
}

/**
 * 下移
 */
function moveDown() {
  if ($("#card .selected").length == 0) {
    alert("移动出错！");
    return;
  }
  var content = $("#card .selected");
  var next = $(content).next();
  $(content).before($(next).clone());
  $(next).remove();
  showBtnDiv();
  isChanged = true;
  $("#save-card").attr("class", "button-yellow rounded right");
}

/**
 * 复制
 */
var copyContent = null;

function copy() {
  if ($("#card .selected").length == 0) {
    alert("复制出错！");
    return;
  }
  var idArr = new Array();
  idArr.push($("#card .selected .check-item :checkbox").val());
  $.post("/newGrammarManager/saveCopiedIdArr", {
    "copiedIdArr": idArr
  }, function (data) {
    $("#copiedNum").text(data);
    $("#stickBth").text("粘贴(" + data + ")");
    $("#copy-tips").fadeIn().delay(1000).fadeOut();
  }, "json");


  /*var content = $("#card .selected");
	copyContent = $(content).clone();
	$(copyContent).removeClass("selected");
	if(!$(copyContent).hasClass("copy")){
		$(copyContent).addClass("copy");
	}
	//$(content).after(copyContent);
	showBtnDiv();
	//isChanged = true;
*/
}

/**
 * 粘贴
 */
function stick() {
  if ($("#card .selected").length == 0) {
    alert("请选择要粘贴的位置！（会粘贴到指定内容后面）");
    return;
  }
  $.post("/newGrammarManager/getCopiedContentList", null, function (data) {
    if (data == null || data.length == 0) {
      alert("未获取到复制的内容！");
      return;
    }
    grammarContentArr = grammarContentArr.concat(data);
    var preContent = $("#card .selected"),
      curContent = null;
    $(data).each(function (i, content) {
      preContent.after(getTemplate(content.questionType));
      curContent = preContent.next();
      $(curContent).removeClass("selected").addClass("copy").attr("data_i", cIndex++);
      setContentData(curContent, content);
      preContent = curContent;
    });
    getInteNum();
    $("#copiedNum").text("0");
    $("#stickBth").text("粘贴(0)");
    // $(".selected").remove();
    alert("粘贴成功！请先保存所有卡片后再进行修改，避免错误删除！");
  }, "json");

  isChanged = true;
  $("#save-card").attr("class", "button-yellow rounded right");
}


/**
 * 显示（重置）按钮
 */
function showBtnDiv() {
  if ($("#card .selected").length == 0) {
    $(".btn-div").hide();
    return;
  }
  $(".btn-div button").show();
  var type = parseInt($("#card .selected").attr("data"));
  if (type == 0) { //分割线不显示保存和编辑按钮
    $("#saveBth,#editBth").hide();
  } else if ($("#card .selected>.show").is(":visible")) { //展示状态不显示保存按钮
    $("#saveBth").hide();
  } else { //编辑状态不显示编辑、复制、粘贴按钮
    $("#editBth,#copyBth,#stickBth").hide();
  }
  /*if(copyContent==null){//没有复制内容，不显示粘贴按钮
  	$("#stickBth").hide();
  }*/
  if ($("#card .selected").is("#card>div:first")) { //第一内容不显示上移按钮
    $("#moveUpBth").hide();
  }
  if ($("#card .selected").is("#card>div:last")) { //最后一个内容不显示下移按钮
    $("#downDownBth").hide();
  }
  var top = $("#card .selected").offset().top;
  var left = $("#card .selected").offset().left + 1005;
  $(".btn-div").css({
    "top": top,
    "left": left
  }).show();
}

/**
 * 上传图片
 * @param btn
 */
function uploadImage(btn) {
  var flag = false;
  var d = dialog({
    title: "上传图片",
    content: "<form id='form'><input name='upload' type='file'/></form>",
    cancelValue: "取消",
    okValue: "确认",
    ok: function () {
      $("#form").ajaxSubmit({
        type: "POST",
        url: uploadAddr + "/newGrammarManager/uploadImage?userId=" + userId + '&unitId=' + unitId.unitId,
        cache: false,
        async: false,
        dataType: "json",
        success: function (data) {
          if (data.message == "ok") {
            var _html = "<img src='" + data.imageUrl + "'/>"
            $(btn).prev("input").val(_html);
            flag = true;
          } else if (data.message == "Size is too big") {
            alert("图片尺寸过大！");
            flag = false;
            return
          } else if (data.message == "Format is wrong") {
            alert("文件格式有误！");
            flag = false;
            return;
          }
        }
      });
      return flag;
    }
  });
  d.showModal();
}

/**
 * 给当前div添加选中样式
 * @param obj
 */
function selectThis(obj) {
  if ($(obj).hasClass("selected") || $(obj).hasClass("hot-border")) {
    return;
  }
  $(obj).addClass("selected").siblings().removeClass("selected");
  showBtnDiv();
}

/**
 * 已处理错误
 */
function dealError() {
  var d = dialog({
    title: "已处理",
    content: "确定已处理该问题了吗？",
    cancelValue: "取消",
    okValue: "确定",
    cancel: true,
    ok: function () {
      $.post("/errorReportManager/dealError", {
        "errorId": eId
      }, function (data) {
        if (data) {
          getErrorContent();
          window.opener.getErrorReportList(window.opener.param.pageNum);
        } else {
          alert("操作失败！");
        }
      }, "json");
      return true;
    }
  });
  d.showModal();
}


/**
 * 无效错误
 */
function invalidError() {
  var d = dialog({
    title: "无效错误",
    content: "确定将该错误设为无效错误码？",
    cancelValue: "取消",
    okValue: "确定",
    cancel: true,
    ok: function () {
      $.post("/errorReportManager/invalidError", {
        "errorId": eId
      }, function (data) {
        if (data) {
          getErrorContent();
          window.opener.getErrorReportList(window.opener.param.pageNum);
        } else {
          alert("操作失败！");
        }
      }, "json");
      return true;
    }
  });
  d.showModal();
}


/**
 * 保存卡片
 */
var submiting = false;

function saveCard() {
  if (submiting) { //避免重复提交
    return;
  }
  if (!getAndCheckCard()) {
    return;
  }
  $("body").append(
    '<div id="saveBg" tabindex="0" style="opacity: 0.8; background: rgb(0, 0, 0); filter:alpha(opacity=80);position: fixed; left: 0px; top: 0px; width: 100%; height: 100%; overflow: hidden; user-select: none; z-index: 10000;"></div>'
  );
  $("body").append(
    '<div id="saveLoading" style="z-index: 10001;position: absolute;top: 246px;width:220px;left:50%;margin-left:-110px;color:#ffffff;font-size:16px;"><img style="margin: 0 0 10px 76px;" src="/grammar-new/images/default/loading.gif"/><div>正在保存，请稍后......</div></div>'
  );
  submiting = true;
  $.ajax({
    type: "POST",
    url: "/lexueKnowledge/content/saveGrammarCard",
    data: JSON.stringify(grammarCard),
    contentType: "application/json",
    dataType: "json",
    success: function (data) {
      console.log(data,'data')
      $("#saveBg").remove();
      $("#saveLoading").remove();
      if (data.success) {
        if (grammarCard.errorId != null) {
          if (data.submitFlag) {
            window.opener.getErrorReportList(window.opener.param.pageNum);
            alert("保存并提交审核成功！");
          } else {
            alert("保存成功，提交审核出现异常！");
          }
        } else {
          try {
            window.opener.getUnitList();
          } catch (err) {}
          alert("保存成功！");
        }
        isChanged = false;
        //    			getGrammarCardDetail();
      } else {
        alert("保存失败！");
      }
      setTimeout(function () {
        window.location.reload();
      }, 1000);
    },
    complete: function () {
      submiting = false;
    }
  })
}


/**
 * 获取卡片的内容，并进行检查
 */
function getAndCheckCard() {
  if ($("#card .edit:visible").length > 0) {
    alert("有内容处于编辑状态，请退出编辑状态后再保存卡片！");
    return false;
  }
  var type = 0;
  var index;
  var editorId;
  var answer;
  grammarCard.grammarContentList.length = 0;
  grammarCard.inteNum = parseInt($("#inteNum").text());
  $("#card>div").each(function (i, item) {
    var grammarContent = new Object();
    type = parseInt($(item).attr("data"));
    grammarContent.questionType = type;
    if (type == 100) { //分割线
      grammarContent.questionType = 0;
      grammarCard.grammarContentList.push(grammarContent);
      return true;
    }
    index = $(item).attr("data_i");
    if (index == null || index == "") {
      index = null;
    } else {
      index = parseInt(index);
    }
    if (index != null) {
      if ($(item).hasClass("copy")) {
        if ($(item).find(".edit").attr("data") == "edited") {
          //do nothing
        } else {
          var newContent = new Object();
          for (var field in grammarContentArr[index]) {
            newContent[field] = grammarContentArr[index][field];
          }
          newContent.id = null;
          grammarCard.grammarContentList.push(newContent);
          return true;
        }
      } else {
        if ($(item).find(".edit").attr("data") == "edited") { //编辑过
          grammarContent.id = grammarContentArr[index].id;
        } else { //未编辑
          grammarCard.grammarContentList.push(grammarContentArr[index]);
          return true;
        }
      }
    }
    if (type == 4) { //文本
      grammarContent.question = $(item).find(".show .question").html();
    } else if (type == 101 || type == 102 || type == 103 || type == 104) { //一级标题、二级标题、三级标题、四级标题
      grammarContent.question = $(item).find(".edit input").val();
    } else {
      editorId = $(item).find(".edit textarea").attr("id");
      grammarContent.question = CKArr[editorId].getData();
      grammarContent.question = grammarContent.question.replace(/color:(?!#ff6600|#008c72)\s*#[0-9a-f]{3,6}/g, ""); //替换颜色
      grammarContent.question = grammarContent.question.replace(/(<div[^>]*>(&nbsp;)?<\/div>)+/g, ""); //去除空行
      grammarContent.wrongTips1 = $.trim($(item).find(".edit .wrongTips input:eq(0)").val());
      grammarContent.wrongTips2 = $.trim($(item).find(".edit .wrongTips input:eq(1)").val());
      grammarContent.wrongTips3 = $.trim($(item).find(".edit .wrongTips input:eq(2)").val());
      grammarContent.parse = $.trim($(item).find(".edit .input-parse").val());
      if (type == 1) { //单选
        grammarContent.optionA = $.trim($(item).find(".edit .input-option:eq(0)").val());
        grammarContent.optionB = $.trim($(item).find(".edit .input-option:eq(1)").val());
        grammarContent.optionC = $.trim($(item).find(".edit .input-option:eq(2)").val());
        grammarContent.optionD = $.trim($(item).find(".edit .input-option:eq(3)").val());
        grammarContent.correctOption = $(item).find(".edit :radio:checked").val();
      } else if (type == 2) { //多选
        grammarContent.optionA = $.trim($(item).find(".edit .input-option:eq(0)").val());
        grammarContent.optionB = $.trim($(item).find(".edit .input-option:eq(1)").val());
        grammarContent.optionC = $.trim($(item).find(".edit .input-option:eq(2)").val());
        grammarContent.optionD = $.trim($(item).find(".edit .input-option:eq(3)").val());
        grammarContent.correctOption = "";
        $(item).find(".edit :checkbox:checked").each(function (num, each) {
          if (num > 0) {
            grammarContent.correctOption += ",";
          }
          grammarContent.correctOption += $(each).val();
        });
      } else if (type == 3 || type == 7 || type == 8) { //填空
        var option = "";
        var maxNum = grammarContent.question.split(/_{3,}/).length - 1;
        $(item).find(".edit .input-option").each(function (num, obj) {
          if (num >= maxNum) {
            return false;
          }
          option = $.trim($(obj).val());
          if (option == null || option == "") {
            return true;
          }
          switch (num) {
            case 0:
              grammarContent.optionA = option;
              break;
            case 1:
              grammarContent.optionB = option;
              break;
            case 2:
              grammarContent.optionC = option;
              break;
            case 3:
              grammarContent.optionD = option;
              break;
            case 4:
              grammarContent.optionE = option;
              break;
          }
        });
      } else if (type == 5) { //选填题
        var option = "";
        answer = "";
        $(item).find(".edit .option-ul>li").each(function (num, obj) {
          option = $.trim($(obj).find("input[name='optionA']").val()) + "|" +
            $.trim($(obj).find("input[name='optionB']").val()) + "|" +
            $.trim($(obj).find("input[name='optionC']").val()) + "|" +
            $.trim($(obj).find("input[name='optionD']").val());
          answer += $(obj).find(".select-answer").val() + ",";
          switch (num) {
            case 0:
              grammarContent.optionA = option;
              break;
            case 1:
              grammarContent.optionB = option;
              break;
            case 2:
              grammarContent.optionC = option;
              break;
            case 3:
              grammarContent.optionD = option;
              break;
            case 4:
              grammarContent.optionE = option;
              break;
          }
        });
        grammarContent.correctOption = answer.substring(0, answer.length - 1);
      } else if (type == 6) { //综合题
        var option = "",
          optionNum = 0;
        var optionArr = new Array();
        answer = "";
        $(item).find(".edit .option-ul>li").each(function (num, obj) {
          optionArr.length = 0;
          optionNum = 0;
          $(obj).find(".select-option").each(function (k, optItem) {
            if ($.trim($(optItem).val()) != "") {
              optionNum++;
              option = $.trim($(optItem).val());
            }
            optionArr.push($.trim($(optItem).val()));
          });
          if (optionNum == 1) {
            answer += "-1,";
          } else {
            option = optionArr.join("|");
            answer += $(obj).find(".select-answer").val() + ",";
          }
          switch (num) {
            case 0:
              grammarContent.optionA = option;
              break;
            case 1:
              grammarContent.optionB = option;
              break;
            case 2:
              grammarContent.optionC = option;
              break;
            case 3:
              grammarContent.optionD = option;
              break;
            case 4:
              grammarContent.optionE = option;
              break;
          }
        });
        grammarContent.correctOption = answer.substring(0, answer.length - 1);
      }
    }
    grammarCard.grammarContentList.push(grammarContent);
  });
  if (grammarCard.grammarContentList.length == 0) {
    alert("请编辑卡片内容!");
    return false;
  }
  return true;
}


/**-----------批量复制功能-----------**/

$(function () {
  $.post("/newGrammarManager/getCopiedNum", null, function (data) {
    $("#copiedNum").text(data);
    $("#stickBth").text("粘贴(" + data + ")");
  }, "json");
});

/**
 * 批量复制
 */
function toBatchCopy(obj) {
  if ($(".edit:visible").length > 0) {
    alert("请先保存正在编辑的内容，或退出编辑！");
    return;
  }
  $("#copy-box>div").toggle();
  $("#card>div").removeClass("selected").addClass("hot-border");
}

/**
 * 复制全选
 */
function copySelectAll() {
  $("#card .check-item :checkbox").prop("checked", true);
  $("#selectedNum").text($("#card .check-item :checkbox:checked").length);
}

/**
 * 确认复制
 */
function batchCopy() {
  if ($("#card .check-item :checkbox:checked").length == 0) {
    alert("请选择要复制的内容！");
    return;
  }
  var idArr = new Array();
  $("#card .check-item :checkbox:checked").each(function (i, item) {
    idArr.push($(item).val());
  });
  $.post("/newGrammarManager/saveCopiedIdArr", {
    "copiedIdArr": idArr
  }, function (data) {
    $("#copiedNum").text(data);
    $("#stickBth").text("粘贴(" + data + ")");
  }, "json");
  $("#copy-box>div").toggle();
  $("#card .check-item :checkbox").prop("checked", false);
  $("#selectedNum").text("0");
  $("#card>div").removeClass("hot-border");
}


/**
 * 取消复制
 */
function copyCancel() {
  $("#copy-box>div").toggle();
  $("#card .check-item :checkbox").prop("checked", false);
  $("#selectedNum").text("0");
  $("#card>div").removeClass("hot-border");
}


/**
 * 退出
 */
function exit() {
  window.top.close()
}

window.onbeforeunload = function () {
  if (isChanged) {
    return "有修改未保存，是否确认离开页面？";
  }
}
