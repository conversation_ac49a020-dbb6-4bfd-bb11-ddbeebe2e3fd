var courseId = Request["courseId"];
var levelNum = 20; //本章检测题最低限制量
var questionTypeArr = [];
var difficultyArr = [];
var _knowledgePointListMap = null;
var gradeId;
$(function(){
	loadCourseName();
	loadConfigInfo();
	$.ajax({
		type: "GET",
		url: "/questionConfig/getQuestionTypeList",
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			if(data!=null){
				$(data).each(function(i, item){
					questionTypeArr[item.id] = item.name;
				});
			}
		}
	});
	$.ajax({
		type: "GET",
		url: "/questionConfig/getQuestionDifficultyList",
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			if(data!=null){
				$(data).each(function(i, item){
					difficultyArr[item.id] = item.name;
				});
			}
		}
	});
	getCourseQuestionList();
});

function loadCourseName(){
	$.post("/newGrammarManager/getCourseFullNameById",{"courseId": courseId},
			function(data){
		if(data){
			gradeId=data.gradeId;
			$("#courseName").text(data.versionName+"-"+data.courseName);
		}
	},"json");
}


/**
 * 获取章试题列表
 */
var courseQuestionNum;
var courseParam={
		courseId: courseId,
		pageNum: 1,
		pageSize: 10
}
var courseQuestionList;
function getCourseQuestionList(nowPage, pageIndex){
	if(nowPage==null){
		nowPage=1;
	}
	courseParam.pageNum = nowPage;
	if(pageIndex==null || pageIndex==1){
		if($("#pageSize1").length>0){
			courseParam.pageSize = parseInt($("#pageSize1").val());
		}
	}else{
		if($("#pageSize2").length>0){
			courseParam.pageSize = parseInt($("#pageSize2").val());
		}
	}
	$.post("/newGrammarManager/getCourseQuestionList",courseParam, function(data){
		courseQuestionList = data.courseQuestionList;
		if(courseParam.pageNum==1){
			data.questionTypeList
			courseQuestionNum = 0;
			var difficultyNumArr = [0, 0, 0, 0];
			var totalNum = 0;
			if(data.questionTypeList!=null){
				var _html = "";
				$(data.questionTypeList).each(function(i, item){
					courseQuestionNum += item.num;
					difficultyNumArr[item.difficulty] = item.num;
					totalNum += item.num;
					if(i==data.questionTypeList.length-1 || data.questionTypeList[i+1].questionType!=item.questionType){
						_html += totalNum +"("+difficultyNumArr[1]+"/"+difficultyNumArr[2]+"/"+difficultyNumArr[3]+")道" + questionTypeArr[item.questionType];
						if(i<data.questionTypeList.length-1){
							_html += "，";
						}else{
							_html += "。";
						}
						difficultyNumArr = [0, 0, 0, 0];
						totalNum = 0;
					}
				});
			}
			if(courseQuestionNum>=levelNum){
				_html = "共<font style='font-weight: bold; color: green;'>"+courseQuestionNum+"</font>题，"+_html;
			}else if(courseQuestionNum>0){
				_html = "共<font style='font-weight: bold; color: red;'>"+courseQuestionNum+"</font>题，"+_html;
			}else {
				_html = "共<font style='font-weight: bold; color: red;'>"+courseQuestionNum+"</font>题。";
			}
			$("#tips-li").html(_html);
			$(".totalCount>span").text(courseQuestionNum);
		}
		showCourseQuestionList(data.courseQuestionList);
	},"json")
}

/**
 * 展示章试题列表
 */
function showCourseQuestionList(courseQuestionList){
	$("#dataTbody").children().remove();
	var contentHtml = "";
	if(courseQuestionList==null||courseQuestionList.length==0){
		tip = "*没有数据*";
		contentHtml="<tr><td colspan='"+$("#dataTable thead tr th").length+"' style='color:red'>"+tip+"</td></tr>";
		$("#dataTbody").append(contentHtml);
	}else{
		var questionArr = null;
		$(courseQuestionList).each(function(index){
			contentHtml ="<tr>";
			contentHtml+="<td><input name='questionId' value='"+this.id+"' type='checkbox'/></td>";
			contentHtml+="<td>"+(courseParam.pageSize*(courseParam.pageNum-1)+index+1)+"</td>";
			contentHtml+="<td>"+getKnowledgePointName(this.knowledgePoint)+"</td>";
			contentHtml+="<td>"+questionTypeArr[this.questionType]+"</td>";
			contentHtml+="<td>"+difficultyArr[this.difficulty]+"</td>";
			contentHtml+="<td class='text-left "+(this.parse==null?"'":("parse-tips' title='"+this.parse+"'"))+">";
			if(this.questionType==3){//判断题，不显示选项
				contentHtml+=this.question+"</td>";
				contentHtml+="<td>"+(this.correctOption=="T"?"√":"×")+"</td>";
			}else if(this.questionType==4){//填空题
				questionArr = $.trim(this.question).split(/_+/g);
				$(questionArr).each(function(i,item){
					contentHtml += item;
					if(i<questionArr.length-1){
						contentHtml += "<u>&nbsp;"+(i+1)+"&nbsp;</u>";
					}
				});
				contentHtml+="</td><td>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="1."+this.optionA;
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<br/>2."+this.optionB;
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<br/>3."+this.optionC;
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<br/>4."+this.optionD;
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<br/>5."+this.optionE;
				}
				contentHtml+="</td>";
			}else{
				contentHtml+=this.question+"<br>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="<span>A."+this.optionA+"</span>&emsp;";
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<span>B."+this.optionB+"</span>&emsp;";
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<span>C."+this.optionC+"</span>&emsp;";
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<span>D."+this.optionD+"</span>&emsp;";
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<span>E."+this.optionE+"</span>&emsp;";
				}
				contentHtml+="</td>";
				contentHtml+="<td>"+this.correctOption+"</td>";
			}
			contentHtml+="<td><button type='button' class='btn btn-primary btn-xs' onclick='toUpdateQuestion("+index+");'>修改</button>&nbsp<button type='button' class='btn btn-primary btn-xs' onclick='deleteCourseQuestion("+this.id+");'>删除</button></td>";
			
			contentHtml+="</tr>";
			$("#dataTbody").append(contentHtml);
			$(".parse-tips").tipso({
				useTitle: true,
				position: 'bottom',
				width: '300px'
			});
		});	
	}
	loadPageInfo();
}

/**
 * 删除本章检测试题
 */
var deleteIdArr = new Array();
function deleteCourseQuestion(id){
	deleteIdArr.length = 0;
	var content = "";
	if(id!=null){
		deleteIdArr.push(id);
		content = "您确定要删除该试题吗？";
	}else{
		$("input[name='questionId']:checked").each(function(){
			deleteIdArr.push($(this).val());
			content = "您确定要删除选中的试题吗？";
		});
	}
	var d = dialog({
		title: "删除试题",
		content: content,
		okValue: "确定",
		cancelValue: "取消",
		cancel: true,
		ok: function(){
			$.ajax({
				type : "post",
				url : "/newGrammarManager/deleteCourseQuestion",
				data : {"courseId": courseId, "deleteIdArr":deleteIdArr},
				dataType : "json",
				success : function(data){
					if(data){
						alert("删除成功！");
						getCourseQuestionList();
					}else{
						alert("删除失败！");
					}
				}
			});
		}
	});
	d.showModal();
}


/**
 * 加载分页信息
 */
function loadPageInfo(){
	var pageLen = 1;
	if(courseQuestionNum!=undefined&&courseQuestionNum>1){
		if(courseParam.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(courseQuestionNum/courseParam.pageSize);					
		}
	}
	
	$('.pageTest:eq(0)').page({
	      leng: pageLen,//分页总数
	      activeClass: 'activP' , //active 类样式定义
	      page_size:courseParam.pageSize,
	      nowPage:courseParam.pageNum,
	      selectPage:getCourseQuestionList1
	    });
	$("#pageSize").attr("id","pageSize1");
	$('.pageTest:eq(1)').page({
		leng: pageLen,//分页总数
		activeClass: 'activP' , //active 类样式定义
		page_size:courseParam.pageSize,
		nowPage:courseParam.pageNum,
		selectPage:getCourseQuestionList2
	});
	$("#pageSize").attr("id","pageSize2");
	
	$(".selectAll").prop("checked",false);	
}

function getCourseQuestionList1(nowPage){
	getCourseQuestionList(nowPage, 1);
}
function getCourseQuestionList2(nowPage){
	getCourseQuestionList(nowPage, 2);
}


/**
 * 全选按钮点击事件
 * @param obj
 */
function selectAll(obj){
	if($(obj).prop("checked")){
		$("input[name='questionId']").prop("checked",true);
		$(".selectAll").prop("checked",true);
	}else{
		$("input[name='questionId']").prop("checked",false);		
		$(".selectAll").prop("checked",false);
	}
}

/**
 * 跳转到添加试题页面
 */
function toAddCourseQuestion(){
	window.location.href="grammar-new-level-add.html?courseId="+courseId;
}

/**
 * 返回
 */
function goBack(){
	window.history.back();
}

function changeKnowLegdeLevel1(obj){
	console.log($(obj).val())
	console.log(_knowledgePointListMap)
	var knowledgeLevel2=_knowledgePointListMap[$(obj).val()];
	console.log(knowledgeLevel2)
		var html="<option value='-1'>未选择</option>";
		$.each(knowledgeLevel2, function(index, item){
			html+="<option value='"+item.id+"'>"+item.name+"</option>"
		})
		console.log($("#knowledgeLevel1").eq(0).html())
		console.log($("#knowledgeLevel1").eq(1).html())
	$("#knowledgeLevel2").eq(0).html(html);
	$("#knowledgeLevel2").eq(1).html(html);
	$("#knowledgeLevel2").html(html);
}

function changeKnowLegdeLevel2(obj){
	var knowLedgeLevel3=_knowledgePointListMap[$(obj).val()];
		var html="<option value='-1'>未选择</option>";
		$.each(knowLedgeLevel3, function(index, item){
			html+="<option value='"+item.id+"'>"+item.name+"</option>"
		})
	$("#knowledgeLevel3").html(html);
}

/**
 * 单题添加
 */
var questionTypeIdArr = new Array("single-choice", "multiple-choice", "", "completion", "selection", "synthesis", "click-completion", "click-select");
var questionTypeHtmlArr = new Array();
var commonHtml = '';

function addQuestion(){
	if(_knowledgePointListMap==null){
		$.ajax({
			type: "post",
			url: "/questionConfig/getKnowledgePointMap",
			cache: false,
			async: false,
			dataType: "json",
			success: function(data){
				_knowledgePointListMap = data;
				var knowLedgeLevel1=_knowledgePointListMap[gradeId];
				var html="<option value='-1'>未选择</option>";
				$.each(knowLedgeLevel1, function(index, item){
					html+="<option value='"+item.id+"'>"+item.name+"</option>"
				})
				$("#knowledgeLevel1").html(html);
				$(".knowDiv").css("display","block");
				commonHtml='<div id="common">'+$("#common").html()+'</div>';
			}
		});
	}
	if(questionTypeHtmlArr.length<=0){
		$(questionTypeIdArr).each(function(i,item){
			console.log(item)
			questionTypeHtmlArr.push($("#"+item).html());
		});
	}
	var _html = '<div id="dialog-div">'+
				'<div>'+
				'<label>题型：</label>'+
			   	'<select id="questionType" onchange="selectQuestionType();">'+
					'<option value="1">单选题</option>'+
					'<option value="2">多选题</option>'+
					'<option value="4">填空题</option>'+
				'</select>'+
				'</div>'+
				'<div id="addQuestionDiv" class="question-dia"></div>'+
				'</div>';
	var d = dialog({
        title: '单题添加',
        content: _html,
        width: "600px",
        zIndex: 999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	return updateOrAddQuestion();
        },
        cancelValue: '取消',
        cancel: function () {}
        });
	selectQuestionType();
    d.showModal();
}

function selectQuestionType(){
	var questionType = parseInt($("#questionType").val());
	var _html = questionTypeHtmlArr[questionType-1]+commonHtml;
	$("#common").remove();
	$("#addQuestionDiv").html(_html);
	bindCommonEvent();
}

function bindCommonEvent(){
	var obj = $("#dialog-div");
	$(obj).find(".know-btn").click(function(){
		if($(this).next(".knowDia").is(":visible")){
			$(this).next(".knowDia").hide();
		}else{
			$(this).next(".knowDia").show();
		}
	});
	$(obj).find(".close").click(function(){
		$(this).parent(".knowDia").hide();
	});
	$(obj).find("input[name='titleId']").change(function(){
		if($(this).prop("checked")){
			$(this).parent().parent().parents("li").children("label").children("input").prop("checked", false);
			$(this).parent().siblings("ul").find("input").prop("checked", false);
		}
		var _html = '';
		$(obj).find(".knowDia input:checked").each(function(i, item){
			_html += '<span class="knowledge-span" data="'+$(item).val()+'">'+$(item).parent().text()+'</span>';
		});
		$(obj).find(".knowledge-list").html(_html);
	});
}

/**
 * 打开修改试题界面
 */
var questionTypeIdArr = new Array("single-choice", "multiple-choice", "", "completion", "selection", "synthesis", "click-completion", "click-select");
function toUpdateQuestion(index){
	var item = courseQuestionList[index];
	var questionTypeId = questionTypeIdArr[item.questionType-1];
	if(commonHtml==""){
		commonHtml='<div id="common">'+$("#common").html()+'</div>';
	}
	var _html = "<div id='dialog-div' class='question-dia'>"+$("#"+questionTypeId).html()+commonHtml+"</div>";
	var d = dialog({
        title: '修改试题',
        content: _html,
        width: "600px",
        zIndex: 999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	return updateOrAddQuestion(item);
        },
        cancelValue: '取消',
        cancel: function () {}
        });
    d.showModal();
    bindCommonEvent();
    //回显试题
    showOldQuestion(item);
}

/**
 * 回显试题
 * @param question
 * @param contianer
 */
function showOldQuestion(oldQuesiton){
	var contianer = $("#dialog-div");
	contianer.find("textarea[name='question']").val(oldQuesiton.question.replace(/<br>/g,"\r\n"));
    contianer.find(".parse").val(oldQuesiton.parse==null?"":oldQuesiton.parse.replace(/<br>/g,"\r\n"));
    contianer.find(".difficulty").val(oldQuesiton.difficulty);
    if(oldQuesiton.source!=null){
    	contianer.find(".source").val(oldQuesiton.source);
    }
    if(oldQuesiton.year!=null){
    	contianer.find(".year").val(oldQuesiton.year);
    }
    if(oldQuesiton.aid!=null){
    	contianer.find(".area").val(oldQuesiton.aid);
    }
    var _html = '';
    $(oldQuesiton.titleIdList).each(function(i, titleId){
    	contianer.find(".knowDia input[value='"+titleId+"']").prop("checked", true);
    	$(knowList).each(function(j, know){
    		if(titleId==know.id){
    			_html += '<span class="knowledge-span" data="'+titleId+'">'+know.name+'</span>';
    		}
    	});
    });
    contianer.find(".knowledge-list").html(_html);
	$(".knowDiv").css("display","none");
    contianer.find(".correct-option[value='"+oldQuesiton.correctOption+"']").prop("checked",true);
	$("[name='answerTime'] option[value='"+oldQuesiton.answerTime+"']").attr("selected", true);
    switch(oldQuesiton.questionType){
    case 1:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	contianer.find(".correct-option[value='"+oldQuesiton.correctOption+"']").prop("checked",true);
    	break;
    case 2:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	var optionArr = $.trim(oldQuesiton.correctOption).split(",");
		$(optionArr).each(function(){
			contianer.find(".correct-option[value='"+this+"']").prop("checked",true);
		});
    	break;
    case 4:
    case 7:
    case 8:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	break;
    case 5:
    case 6:
    	var correctArr = oldQuesiton.correctOption.split(",");
    	var answer = null;
    	$(correctArr).each(function(i, cIndex){
    		cIndex = parseInt(cIndex);
    		switch(i){
    		case 0: answer = oldQuesiton.optionA; break;
    		case 1: answer = oldQuesiton.optionB; break;
    		case 2: answer = oldQuesiton.optionC; break;
    		case 3: answer = oldQuesiton.optionD; break;
    		case 4: answer = oldQuesiton.optionE; break;
    		}
    		if(cIndex<0){
    			contianer.find(".option-ul>li").eq(i).find("input[name='optionA']").val(answer);
    		}else{
    			var answerArr = answer.split("|");
    			$(answerArr).each(function(j, answerItem){
    				contianer.find(".option-ul>li").eq(i).find(":text").eq(j).val(answerItem);
    			});
    			contianer.find(".option-ul>li").eq(i).find(".select-item").val(cIndex);
    		}
    	});
    	break;
    }
}

/**
 * 加配置信息
 */
function loadConfigInfo(){
	//加载年份
	$.post("/questionConfig/getSystemTime", null, function(data){
		var year = new Date(data).getFullYear();
		var _html = '';
		while(year>=2015){
			_html += '<option value="'+year+'">'+year+'</option>';
			year--;
		}
		$("#common .year").html(_html);
	}, "json");
	
	//加载地区
	$.post("/questionConfig/getAreaList", {"parentId": 0}, function(data){
		var _html = '';
		$(data).each(function(i, item){
			_html += '<option value="'+item.id+'">'+item.name+'</option>';
		});
		$("#common .area").append(_html);
	}, "json");
}


/*
 * 修改试题
 */
function updateOrAddQuestion(item){
	var knowledgePoint=null;
	var type = 1;
	if(item==null){
		item = new Object();
		item.questionType = parseInt($("#questionType").val());
		item.courseId=courseId;
		type = 1;
		if($("#knowledgeLevel1").val()==-1){
			alert("请选择关联知识点");
			return false;
		}else if($("#knowledgeLevel3").val()!=-1 && $("#knowledgeLevel3").val()!=null){
			knowledgePoint=$("#knowledgeLevel3").val();
		}else if($("#knowledgeLevel2").val()!=-1 && $("#knowledgeLevel2").val()!=null){
			knowledgePoint=$("#knowledgeLevel2").val();
		}else{
			knowledgePoint=$("#knowledgeLevel1").val();
		}
		item.knowledgePoint=knowledgePoint;
	}else if(item.id==null){
		type = 3;
	}else{
		type = 2;
	}
	//获取试题参数
	if(!getQuestionParam(item)){
		return false;
	}
	
	//发送请求
	var flag = false;
	var url="", tips="";
	switch(type){
	case 1:
		url = "/newGrammarManager/addCourseQuestionList";
		tips = "添加试题";
		break;
	case 2:
		url = "/newGrammarManager/updateCourseQuestion";
		tips = "修改试题";
		break;
	case 3:
		url = "/newGrammarManager/addQuestionList";
		tips = "复制试题";
		break;
	}
	$.ajax({
		url: url,
		type: "post",
		data: JSON.stringify(item),
        contentType:"application/json",
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			flag = data;
			if(data){
				alert(tips+"成功！");
				getCourseQuestionList();
			}else{
				alert(tips+"失败！");
			}
		}
	});
	return flag;
}

function getQuestionParam(item){
	//获取并验证内容
	item.question = $("#dialog-div textarea[name='question']").val();
	item.question = $.trim(item.question);
	item.grade = gradeId;
	console.log(gradeId);
	if(item.question==null || $.trim(item.question)==""){
		alert("请输入题干内容");
		return false;
	}
	item.question = item.question.replace(/(\n)|(\r\n)/g,"<br>");
	item.question = item.question.replace(/(<br>){2,}/g,"<br>");
	item.answerTime = $("#dialog-div select[name='answerTime']").val();
	switch(item.questionType){
    case 1: //单选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	item.correctOption = $("#dialog-div .correct-option:checked").val();
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		if((item.correctOption=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
				(item.correctOption=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
				(item.correctOption=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
				(item.correctOption=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
				(item.correctOption=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
			alert("没有内容的答案不能为正确答案");
			return false;
		}
    	break;
    case 2: //多选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	item.correctOption = "";
		$("#dialog-div .correct-option:checked").each(function(){
			item.correctOption += $(this).val()+",";
			if(($(this).val()=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
					($(this).val()=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
					($(this).val()=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
					($(this).val()=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
					($(this).val()=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
				alert("没有内容的答案不能为正确答案");
				return false;
			}
		});
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		item.correctOption = item.correctOption.substring(0,item.correctOption.length-1);
    	break;
 	case 4: //填空
    case 7: //点填
    case 8: //点选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	break;
    case 5: //选填
    case 6: //综合
    	var questionArr = item.question.split(/_{3,}/);
		if(questionArr.length<=1){
			alert("请给该题设置题空！");
			return false;
		}
		var emptyIsAnswer = false;
		var optionUl = $("#dialog-div .option-ul");
		var curOption, answer, optionNum;
		var optionArr = new Array();
		var correctArr = new Array();
		for(var i=0; i<questionArr.length; i++){
			if(i>=questionArr.length-1){
				break;
			}
			if(i>=5){
				continue;
			}
			emptyIsAnswer = false;
			curOption = $(optionUl).children("li:eq("+i+")");
			answer = parseInt($(curOption).find(".select-item").val());
			optionArr.length = 0;
			optionNum = 0;
			$(curOption).find(".input-item").each(function(j, item){
				var option = $.trim($(item).val());
				if(option != null && option!=""){
					optionNum++;
				}else if(answer==j){
					emptyIsAnswer = true;
					return false;
				}
				optionArr.push(option);
			});
			if(optionNum<1){
				alert("在第"+(i+1)+"空中，请输入至少一个备选项！");
				return false;
			}
			if(optionNum>1 && emptyIsAnswer){
				alert("在第"+(i+1)+"空中，空选项不能为正确答案！");
				return false;
			}
			var optionStr = '';
			if(optionNum>1){//选填
				optionStr = optionArr.join("|");
				correctArr.push(answer);
			}else{//填空
				if(item.questionType==5){
					alert("选填题至少要有两个备选项！");
					return false;
				}
				optionStr = optionArr[0];
				correctArr.push("-1");
			}
			switch(i){
			case 0: item.optionA = optionStr; break;
			case 1: item.optionB = optionStr; break;
			case 2: item.optionC = optionStr; break;
			case 3: item.optionD = optionStr; break;
			case 4: item.optionE = optionStr; break;
			}
		}
		item.correctOption = correctArr.join(",");
    	break;
    }
	item.parse = $("#dialog-div .parse").val();
	item.parse = $.trim(item.parse);
	item.parse = item.parse.replace(/(\n)|(\r\n)/g,"<br>");
	item.parse = item.parse.replace(/(<br>){2,}/g,"<br>");
	item.difficulty = $("#dialog-div .difficulty").val();
	item.year = $("#dialog-div .year").val();
	item.aid = $("#dialog-div .area").val();
	if($("#dialog-div .knowledge-list>.knowledge-span").length>0){
		item.titleIdList = new Array();
		$("#dialog-div .knowledge-list>.knowledge-span").each(function(i, span){
			item.titleIdList.push($(span).attr("data"));
		});
	}
	return true;
}