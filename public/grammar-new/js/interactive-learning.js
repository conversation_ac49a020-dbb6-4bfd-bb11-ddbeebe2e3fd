var courseId = Request["courseId"];
var unitId = Request["unitId"];
var isFirst = Request["isFirst"];
var chinese = /[\u4E00-\u9FA5，；。“”？！：、（）]/g;
var _position;
var userContentHelpNum = 0;
var grammarContentList = new Array();
var showMode = 1;
var learnProgress = 0;
var totalProgress = 0;
var showLearned = true; //首次加载，显示已学内容的标志
$(function() {
	//切换展示形式
	$("#select_showType").click(function() {
		//切换展示形式
		if ($(this).hasClass("card")) {
			showMode = 2;
			$(this).removeClass("card").addClass("screen").parent().css("top", "8px");
			$("#card-box").hide().siblings("#fullScreen_mode").show();
			$("#card-top").hide().siblings("#screen-top").show();
		} else {
			showMode = 1;
			$(this).removeClass("screen").addClass("card").parent().css("top", "17px");
			$("#card-box").show().siblings("#fullScreen_mode").hide();
			$("#card-top").show().siblings("#screen-top").hide();
		}
	});

	getGrammarCard();

	$(".common-options li").click(function() {
		$(this).addClass("checked").siblings().removeClass("checked");
	});

	// 关闭指引页
	$("#close_help").click(function() {
		$(this).parents(".help-page").remove();
	});

//	$("#efficiencyHelper").load("/insideHtml/newEfficiencyHelper.html");
	//知识体系
	// var knowlegeTop = (window.innerHeight - $("#knowlege .knowlege-system-box").outerHeight()) / 2;
	// $("#knowlege .knowlege-system-box").css("top", knowlegeTop);
	$("#knowlege .knowlege-system").mouseenter(function() {
		if (!$(this).hasClass("k-active")) {
			$(this).addClass("k-active");
			$("#knowlege .title-list-div").show();
		}
	});
	$("#knowlege").mouseleave(function() {
		$(".knowlege-system").removeClass("k-active");
		$(".title-list-div").hide();
	});

	//子标题导航
	$(".subtitleList>li").click(function() {
		if (!$(this).hasClass("did") || $(this).hasClass("active")) {
			return;
		}
		$(this).addClass("active").siblings().removeClass("active");
		var index = $(this).index();
		$(this).parents(".main-content").find(".tab").eq(index).show().siblings(".tab").hide();
	});

	//小题导航
	$(".c-index-list").on("click", "li", function(e) {
		if ($(this).hasClass("active")) {
			return;
		}
		$(this).addClass("active").siblings(".active").removeClass("active");
		var index = $(this).index();
		$(this).parent().next(".tab-content").find(".card-item").children().eq(index).show().siblings().hide();
	});

	setInterval(function() {
		$(".animate-arrow").animate({
			top: '17px'
		}, 750);
		$(".animate-arrow").animate({
			top: '8px'
		}, 750);
	}, 1500);

	$(document).keyup(function(e) {
		switch (e.keyCode) {
			case 13: //回车
				$(".submit-btn,.next-btn,.inte-learn-submit,#next-bar").filter(":visible").click();
				break;
		}
	});
});

var grammarCard;
var titleStatusList;
var contentStatusList;

function getGrammarCard() {
	$.post("/newGrammarManager/getGrammarCardDetail", {
			"unitId": unitId,
			"isFirst": isFirst
		},
		function(data) {
			grammarCard = data;
			progress = 0;
			pIndex = 0;
			totalProgress = 0;
			showLearned = true;
			showGrammarTitle();
			showGrammarCard();
		}, "json");
}

/**
 * 显示语法标题
 */
function showGrammarTitle() {
	var _html = '';
	$(grammarCard.titleList).each(function(i, title1) {
		grammarContentList.push({
			"id": title1.id,
			"question": title1.name,
			"questionType": 101
		});
		if (title1.subtitleList) {
			knowledgeArr.push({
				"titleId": title1.id,
				"titleName": title1.name,
				"subtitleList": title1.subtitleList
			});
			addSubtitleList(title1.subtitleList);
		}
		_html += '<li id="t_' + title1.id + '" class="knowledge-item knowledge-not-learn">';
		_html += '<h2 class="title-text knowledge-title">' + title1.name + '</h2>';
		if (title1.basic) {
			_html += '<i id="kDrop_' + title1.id + '" class="water-drop water-drop-null"></i>';
		}
		_html += '<ul class="knowledge-map-sm">';
		$(title1.titleList).each(function(j, title2) {
			grammarContentList.push({
				"id": title2.id,
				"question": title2.name,
				"questionType": 102
			});
			if (title2.subtitleList) {
				knowledgeArr.push({
					"titleId": title2.id,
					"titleName": title1.name + ">" + title2.name,
					"subtitleList": title2.subtitleList
				});
				addSubtitleList(title2.subtitleList);
			}
			_html += '<li id="t_' + title2.id + '" class="knowledge-item-sm knowledge-not-learn">';
			_html += '<h2 class="title-text knowledge-title-sm">' + title2.name + '</h2>';
			if (title2.basic) {
				_html += '<i id="kDrop_' + title2.id + '" class="water-drop water-drop-null"></i>';
			}
			if (title2.titleList) {
				_html += '<ul>';
				$(title2.titleList).each(function(m, title3) {
					grammarContentList.push({
						"id": title3.id,
						"question": title3.name,
						"questionType": 103
					});
					if (title3.subtitleList) {
						knowledgeArr.push({
							"titleId": title3.id,
							"titleName": title1.name + ">" + title2.name + ">" + title3.name,
							"subtitleList": title3.subtitleList
						});
						addSubtitleList(title3.subtitleList);
					}
					_html += '<li id="t_' + title3.id + '" class="knowledge-item-xs knowledge-not-learn clear">';
					//						_html += '<span>1</span>';
					_html += '<strong class="title-text">' + title3.name + '</strong>';
					if (title3.basic) {
						_html += '<i id="kDrop_' + title3.id + '" class="water-drop water-drop-null"></i>';
					}
					_html += '</li>';
				});
				_html += '</ul>';
			}
			_html += '</li>';
		});
		_html += '</ul>';
		_html += '</li>';
	});
	$("#titleList").html(_html);
	$("#titleList .water-drop").click(function() {
		if ($(this).hasClass("water-drop-null") || $(this).hasClass("water-drop-active")) {
			return;
		}
		var index = $(this).index("#titleList .water-drop");
		$("#titleList .water-drop").removeClass("water-drop-active");
		$(this).addClass("water-drop-active");
		$("#progress-list>li").eq(index).addClass("water-drop-active").siblings().removeClass("water-drop-active");
		if ($(this).hasClass("water-drop-learning")) {
			$("#card_mode_temp").hide();
			$("#card_mode").show();
		} else {
			$("#card_mode_temp").show();
			$("#card_mode").hide();
			showTempKnowledge(index);
		}
	});
	// var knowlegeTop = (window.innerHeight - $(".title-list-div").outerHeight()) / 2;
	// $(".title-list-div").css("top", knowlegeTop);

	//处理grammarContentList末尾只是标题的情况
	while (grammarContentList.length) {
		var lastContent = grammarContentList[grammarContentList.length - 1];
		if (lastContent.questionType >= 100) { //标题
			grammarContentList.pop();
		} else {
			break;
		}
	}

}

/**
 * 添加子标题内容到grammarContentList中
 */
function addSubtitleList(_subtitleList) {
	if (_subtitleList == null) {
		return;
	}
	$(_subtitleList).each(function(i, item) {
		grammarContentList.push({
			"id": item.id,
			"question": item.name,
			"questionType": 104
		});
		if (item.contentList != null) {
			grammarContentList = grammarContentList.concat(item.contentList);
			totalProgress += item.contentList.length;
		}
	});
}


/**
 * 显示语法卡片内容
 */
function showGrammarCard() {
	//显示子标题状态列表
	var _html = '';
	$(knowledgeArr).each(function(i, item) {
		_html += '<li id="drop_' + item.titleId + '" class="water-drop water-drop-null"></li>';
	});
	$("#progress-list").html(_html);
	$(titleStatusList).each(function(i, item) {
		switch (item.learnStatus) {
			case 1:
				$("#drop_" + item.titleId).attr("class", "water-drop water-drop-fail");
				$("#kDrop_" + item.titleId).attr("class", "water-drop water-drop-fail");
				break;
			case 2:
				$("#drop_" + item.titleId).attr("class", "water-drop water-drop-warning");
				$("#kDrop_" + item.titleId).attr("class", "water-drop water-drop-warning");
				break;
			case 3:
				$("#drop_" + item.titleId).attr("class", "water-drop water-drop-pass");
				$("#kDrop_" + item.titleId).attr("class", "water-drop water-drop-pass");
				break;
		}
	});
	//显示学习目标
	if (grammarCard.targetContentList != null && grammarCard.targetContentList.length > 0) {
		$("#learnTarget").show();
		_html = '';
		$(grammarCard.targetContentList).each(function(i, item) {
			_html += '<li>' + _newQuestion(item.question) + '</li>';
		});
		$("#learnTargetDia .main").html(_html);
	}
	
	//对描述文字加粗
	function _newQuestion(oldQuestion) {
		var desc = oldQuestion.match(/\【(.+?)\】/g); //匹配学习目标的中括号和其中的描述文字
		return oldQuestion.replace(desc, '<span>' + desc + '</span>');
	}

	//显示学习小结
	if (grammarCard.summaryContentList != null && grammarCard.summaryContentList.length > 0) {
		$("#learnSummary").show();
		_html = '';
		$(grammarCard.summaryContentList).each(function(i, item) {
			_html += '<li>' + item.question + '</li>';
		});
		$("#learnSummaryDia .main").html(_html);
	}

	$("#inteTotalNum").text(totalProgress);
	//标题导航
	$("#progress-list>li").click(function() {
		if ($(this).hasClass("water-drop-null") || $(this).hasClass("water-drop-active")) {
			return;
		}
		var index = $(this).index();
		$("#titleList .water-drop").removeClass("water-drop-active");
		$("#titleList .water-drop").eq(index).addClass("water-drop-active");
		$(this).addClass("water-drop-active").siblings().removeClass("water-drop-active");
		if ($(this).hasClass("water-drop-learning")) {
			$("#card_mode_temp").hide();
			$("#card_mode").show();
		} else {
			$("#card_mode_temp").show();
			$("#card_mode").hide();
			showTempKnowledge(index);
		}
	});

	$(".bottom-btn-div").hide();
	showKnowledge();
}

/**
 * 显示知识点
 */
var kIndex = 0;
var knowledgeArr = new Array();
var kAnswerNum = 0; //知识点中试题的题数
var kErrorNum = 0; //知识点中答题错误的题数
//var canUpdateKStatus = false;
function showKnowledge(showType, isLearned) {
	if (!showLearned) { //保存知识点的学习状态
		updateUserKnowledgeStatus();
	}
	//全屏模式下显示学过的知识点
	while (!showLearned && kIndex < knowledgeArr.length && !$("#progress-list").children().eq(kIndex).hasClass(
			"water-drop-null")) {
		var curKnowledge = knowledgeArr[kIndex++];
		$(curKnowledge.subtitleList).each(function(i, subtitle) {
			$(subtitle.contentList).each(function(j, content) {
				showContent2(true);
			});
		});
	}
	if (kIndex >= knowledgeArr.length) {
		return;
	}
	if (!showLearned) {
		$("#progress-list").children().eq(kIndex).attr("class", "water-drop water-drop-learning water-drop-active");
		$("#titleList .water-drop").eq(kIndex).attr("class", "water-drop water-drop-learning");
	}

	var curKnowledge = knowledgeArr[kIndex++];
	var titleName = curKnowledge.titleName;
	$("#progress-num").text('（' + kIndex + '/' + knowledgeArr.length + '）');
	$("#card_mode .cur-title-name").text(titleName);
	$("#card_mode .subtitleList>li").attr("class", "");
	$("#card_mode .c-index-list").html("");
	$("#card_mode .card-item").html("");
	//更新知识点体系学习进度
	var titleId = "t_" + curKnowledge.titleId;
	$("#titleList .knowledge-not-learn").each(function(i, item) {
		if ($(item).attr("id") == titleId) {
			$(item).removeClass("knowledge-not-learn");
			return false;
		} else {
			$(item).removeClass("knowledge-not-learn");
		}
	});

	kAnswerNum = 0;
	kErrorNum = 0;
	sIndex = 0;
	subtitleList = curKnowledge.subtitleList;
	showSubtitle(showType, isLearned);
}

/**
 * 更新用户知识点状态
 */
function updateUserKnowledgeStatus() {
	var titleId = knowledgeArr[kIndex - 1].titleId;
	//检测当前知识点是否已有状态，有状态的不更新
	if(titleStatusList!=null && titleStatusList.length>0){
		for(var i=0; i<titleStatusList.length; i++){
			if(titleStatusList[i].titleId==titleId && titleStatusList[i].learnStatus>0){
				return;
			}
		}
	}
	var learnStatus = 0;
	if (kErrorNum == 0) { //已掌握
		learnStatus = 3;
	} else if (kAnswerNum == kErrorNum) { //未掌握
		learnStatus = 1;
	} else { //未完全掌握
		learnStatus = 2;
	}
	switch (learnStatus) {
	case 1:
		$("#drop_" + titleId).attr("class", "water-drop water-drop-fail");
		$("#kDrop_" + titleId).attr("class", "water-drop water-drop-fail");
		break;
	case 2:
		$("#drop_" + titleId).attr("class", "water-drop water-drop-warning");
		$("#kDrop_" + titleId).attr("class", "water-drop water-drop-warning");
		break;
	case 3:
		$("#drop_" + titleId).attr("class", "water-drop water-drop-pass");
		$("#kDrop_" + titleId).attr("class", "water-drop water-drop-pass");
		break;
	}
	if (kIndex >= knowledgeArr.length && pIndex >= grammarContentList.length) {
		$(".bottom-btn-div").show();
		$("#titleList .knowledge-not-learn").removeClass("knowledge-not-learn");
		$("#progress-num").text('（' + knowledgeArr.length + '/' + knowledgeArr.length + '）');
	}
}


/**
 * 显示临时的知识点内容
 * @param index
 */
function showTempKnowledge(index) {
	var curKnowledge = knowledgeArr[index];
	var titleName = curKnowledge.titleName;
	$("#card_mode_temp .cur-title-name").text(titleName);
	$("#card_mode_temp .subtitleList>li").attr("class", "");
	$("#card_mode_temp .tab").first().show().siblings(".tab").hide();
	$("#card_mode_temp .c-index-list").html("");
	$("#card_mode_temp .card-item").html("");
	$(curKnowledge.subtitleList).each(function(i, subtitle) {
		$("#card_mode_temp .subtitleList>li").first().attr("class", "did active").nextAll().attr("class", "did");
		if (subtitle.contentList == null) {
			return true;
		}
		var _html = '',
			_indexHtml = '';
		$(subtitle.contentList).each(function(j, content) {
			_html += getContentHtml1(content, true);
			_indexHtml += '<li>' + (j + 1) + '</li>';
		});
		if(subtitle.contentList.length>1){
			$("#card_mode_temp .c-index-list").eq(i).html(_indexHtml);
			$("#card_mode_temp .c-index-list").eq(i).children().last().addClass("active");
		}
		$("#card_mode_temp .card-item").eq(i).html(_html);
		$("#card_mode_temp .card-item").eq(i).children().last().siblings().hide();
		$('.inte-learn-box :radio,.inte-learn-box :checkbox').iCheck({
			checkboxClass: 'icheckbox_minimal-green',
			radioClass: 'iradio_minimal-grey',
			increaseArea: '20%' // optional
		});
	});
}

/**
 * 显示子标题
 */
var sIndex = 0;
var subtitleList = null;
function showSubtitle(showType, isLearned) {
	if (subtitleList == null || sIndex >= subtitleList.length) {
		showKnowledge(showType, isLearned);
		return;
	}
	var subtitle = subtitleList[sIndex++];
	$("#card_mode>.main-content>.tab").eq(subtitle.type - 1).show().siblings(".tab").hide();
	$("#card_mode .subtitleList>li").eq(subtitle.type - 1).attr("class", "did active").prevAll().attr("class", "did");
	cIndex = 0;
	contentList = subtitle.contentList;
	if(contentList==null){
		showSubtitle(showType, isLearned);
		return;
	}
	if (showType != null) {
		showContent(showType, isLearned);
		return;
	}
	//首次加载，定位到上次学到的位置
	if (learnProgress < 0) {
		while (progress < totalProgress) {
			showContent(null, true);
		}
		showLearned = false;
		if ($("#progress-list>.water-drop-pass").length < $("#progress-list>.water-drop").length) {
			$("#fillVacancy").show();
			showFillVacancyDia();
		} else {
			$("#learnAgain").show();
			showLearnAgainDia();
		}
		$(".bottom-btn-div").show();
		$("#titleList .knowledge-not-learn").removeClass("knowledge-not-learn");
	} else {
		while (progress < learnProgress) {
			showContent(null, true);
		}
		showLearned = false;
		if($("#progress-list").children().eq(kIndex - 1).hasClass("water-drop-null")){
			$("#progress-list").children().eq(kIndex - 1).attr("class", "water-drop water-drop-learning water-drop-active");
			$("#titleList .water-drop").eq(kIndex - 1).attr("class", "water-drop water-drop-learning");
		}
		showContent();
	}
}

function showContent(showType, isLearned) {
	if (showType == null) {
		showContent1(isLearned);
		showContent2(isLearned);
	} else if (showType == 1) {
		showContent1(isLearned);
	} else {
		showContent2(isLearned);
	}
}

/**
 * 显示卡片内容
 */
var cIndex = 0;
var contentList = null;
var isLearned = false;
function showContent1(isLearned) {
	if (cIndex >= contentList.length) {
		showSubtitle(1, isLearned);
		return;
	}
	if (contentList.length > 1) {
		var indexList = $("#card_mode .s-container" + subtitleList[sIndex - 1].type).find(".c-index-list");
		indexList.children(".active").removeClass("active");
		indexList.append('<li class="active">' + (cIndex + 1) + '</li>');
	}
	var content = contentList[cIndex++];
	var _html = getContentHtml1(content, isLearned);
	var container = $("#card_mode .s-container" + subtitleList[sIndex - 1].type).find(".tab-content");
	container.find(".card-item").children().last().hide();
	container.find(".card-item").append(_html);
	if (isLearned) {
		container.find("button").hide();
	} else {
		if (content.questionType == 4) {
			container.find(".next-btn").show().siblings("button").hide();
		} else {
			container.find(".submit-btn").show().siblings("button").hide();
		}
		bindQuestionEvent(container.find(".card-item").children().last(), 1);
	}
	$('.inte-learn-box :radio,.inte-learn-box :checkbox').iCheck({
		checkboxClass: 'icheckbox_minimal-green',
		radioClass: 'iradio_minimal-grey',
		increaseArea: '20%' // optional
	});
	if (showLearned) {
		$(contentStatusList).each(function(i, item) {
			if (content.id = item.contentId) {
				if (item.errorNum > 0) {
					kErrorNum++;
				}
				kAnswerNum++;
				return false;
			}
		});
	}
}

/**
 * 获取内容的html
 * @param content
 * @returns {String}
 */
function getContentHtml1(content, isLearned) {
	if (content == null) {
		return '';
	}
	var _html = '';
	switch (content.questionType) {
		case 1: //单选题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + content.question + '</div>';
				_html += '<ul class="option inte-learn-radio">';
				var index = new Date().getTime() + Math.random();
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="A" ';
					_html += content.correctOption == "A" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>A. ' + content.optionA + '</label></li>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="B" ';
					_html += content.correctOption == "B" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>B. ' + content.optionB + '</label></li>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="C" ';
					_html += content.correctOption == "C" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>C. ' + content.optionC + '</label></li>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="D" ';
					_html += content.correctOption == "D" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>D. ' + content.optionD + '</label></li>';
				}
				_html += '</ul>';
			} else {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + content.question;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input name="input_' + pIndex + '" type="radio" value="A"/>A. ' + content.optionA +
						'</label>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="B"/>B. ' + content.optionB +
						'</label>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="C"/>C. ' + content.optionC +
						'</label>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="D"/>D. ' + content.optionD +
						'</label>';
				}
				_html += '</ul>';
				_html += '</li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 2: //多选题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			var question = content.question.replace(/<\/p>(\s)*$/, "<span style='color: #999;'>（多选）</span></p> ");
			if(question==content.question){
				question = content.question.replace(/<\/div>(\s)*$/,"<span style='color: #999;'>（多选）</span></div>");
			}
			if (isLearned) {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + question + '</div>';
			} else {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + question;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			if (isLearned) {
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input type="checkbox" value="A" ';
					_html += content.correctOption.indexOf("A") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>A. ' + content.optionA + '</label></li>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '<li><label><input type="checkbox" value="B" ';
					_html += content.correctOption.indexOf("B") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>B. ' + content.optionB + '</label></li>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '<li><label><input type="checkbox" value="C" ';
					_html += content.correctOption.indexOf("C") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>C. ' + content.optionC + '</label></li>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '<li><label><input type="checkbox" value="D" ';
					_html += content.correctOption.indexOf("D") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>D. ' + content.optionD + '</label></li>';
				}
				_html += '</ul>';
			} else {
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input type="checkbox" value="A"/>A. ' + content.optionA + '</label>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '</li><li><label><input type="checkbox" value="B"/>B. ' + content.optionB + '</label>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '</li><li><label><input type="checkbox" value="C"/>C. ' + content.optionC + '</label>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '</li><li><label><input type="checkbox" value="D"/>D. ' + content.optionD + '</label>';
				}
				_html += '</ul>';
				_html += '</li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 3: //填空题
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			for (var i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					len = correctAnswer.length;
					if (chinese.test(correctAnswer)) {
						len += correctAnswer.match(chinese).length;
					}
					width = (len + 2) * 10;
					if (width < 80) {
						width = 80;
					}
					if (isLearned) {
						questionHtml += '<input class="border" style="width:' + width + 'px;" value="' + correctAnswer +
							'" disabled="disabled"/>';
					} else {
						questionHtml += "<input class='border' style='width:" + width + "px;'/>";
					}
				}
			}
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 4: //文本
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			_html += '<div class="question inte-learn-sm-text inte-learn-mid-text">' + content.question;
			_html += '</div>';
			_html += '</div></div>';
			break;
		case 5: //下拉选择题
			var _select = "";
			var width = 0,
				length = 0,
				len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			// var questionHtml = content.question.replace(/<p[^>]*>/g, "");
			var questionHtml = content.question
			questionHtml = questionHtml.replace(/<\/p>/g, "<br>");
			questionHtml = questionHtml.replace(/<br>+\s*$/, "");
			var questionArr = questionHtml.split(/_{3,}/);
			for (var i = 0; i < questionArr.length - 1; i++) {
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				} else {
					break;
				}
				optionArr = optionStr.split("|");
				answer = optionArr[parseInt(answerIndexArr[i])];
				if (isLearned) {
					if (chinese.test(answer)) {
						width = (answer.length + answer.match(chinese).length) * 10 + 40;
					} else {
						width = answer.length * 10 + 40;
					}
					if (width < 80) {
						width = 80;
					}
					_select = "<div class='question-select s-disable'>";
					_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + answer +
						"</h3>";
					_select += "</div>";
				} else {
					len = 0;
					for (var j = 0; j < optionArr.length; j++) {
						length = optionArr[j].length;
						if (chinese.test(optionArr[j])) {
							length += optionArr[j].match(chinese).length;
						}
						if (len < length) {
							len = length;
						}
					}
					width = len * 10 + 40;
					if (width < 80) {
						width = 80;
					}
					_select = "<div class='s-active question-select'>";
					_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>请选择</h3>";
					_select += "<ul>";
					$(optionArr).each(function(j, option) {
						if (option != null && option != "") {
							_select += "<li data='" + j + "' style='width:" + width + "px'>" + option + "</li>";
						}
					});
					_select += "</ul></div>";
				}
				questionHtml = questionHtml.replace(/_{3,}/, _select);
			}
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 6: //综合题
			var _select = "";
			var questionHtml = "";
			var width = 0,
				length = 0,
				len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			// var questionTemp = content.question.replace(/<p[^>]*>/g, "");
			var questionTemp = content.question
			questionTemp = questionTemp.replace(/<\/p>/g, "<br>");
			questionTemp = questionTemp.replace(/<br>+\s*$/, "");
			var questionArr = questionTemp.split(/_{3,}/);
			for (var i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i >= questionArr.length - 1) {
					break;
				}
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				} else {
					break;
				}
				optionArr = optionStr.split("|");
				if (isLearned) {
					if (optionArr.length == 1) {
						answer = optionArr[0];
					} else {
						answer = optionArr[parseInt(answerIndexArr[i])];
					}
					if (chinese.test(answer)) {
						width = (answer.length + answer.match(chinese).length) * 10 + 40;
					} else {
						width = answer.length * 10 + 40;
					}
					if (width < 80) {
						width = 80;
					}
					if (optionArr.length == 1) {
						_select = '<input class="border" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
					} else {
						_select = "<div class='question-select s-disable'>";
						_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + answer +
							"</h3>";
						_select += "</div>";
					}
				} else {
					len = 0;
					for (var j = 0; j < optionArr.length; j++) {
						length = optionArr[j].length;
						if (chinese.test(optionArr[j])) {
							length += optionArr[j].match(chinese).length;
						}
						if (len < length) {
							len = length;
						}
					}
					width = len * 10 + 20;
					if (width < 80) {
						width = 80;
					}
					if (optionArr.length == 1) {
						_select = "<input class='border' style='width:" + width + "px;'/>";
					} else {
						_select = "<div class='s-active question-select'>";
						_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>请选择</h3>";
						_select += "<ul>";
						$(optionArr).each(function(j, option) {
							if (option != null && option != "") {
								_select += "<li data='" + j + "' style='width:" + width + "px'>" + option + "</li>";
							}
						});
						_select += "</ul></div>";
					}
				}
				questionHtml += _select;
			}
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 7: //点填题
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			for (i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					len = correctAnswer.length;
					if (chinese.test(correctAnswer)) {
						len += correctAnswer.match(chinese).length;
					}
					width = (len + 2) * 10;
					if (width < 80) {
						width = 80;
					}
					if (isLearned) {
						questionHtml += '<input class="input-line" style="width:' + width + 'px;" value="' + correctAnswer +
							'" disabled="disabled"/>';
					} else {
						questionHtml += "<input class='input-line' style='width:" + width + "px;'/>";
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/, "<span style='color: #999;'>（可以划选单词填空）</span></p>");
			_html += '<div class="inte-learn-box dian-tian-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 8: //点选题
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			var answerArr = new Array();
			for (i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					answerArr.push(correctAnswer);
					if (isLearned) {
						questionHtml += '<span class="span-line">' + correctAnswer + '</span>';
					} else {
						questionHtml += '<span class="span-line"></span>';
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/,"<span style='color: #999;'>（可以划选单词填空）</span></p>");
			_html += '<div class="inte-learn-box dian-tian-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			answerArr.sort(function() {
				return Math.random() > 0.5 ? -1 : 1;
			}); //随机排序
			_html += '<div class="answer-item-div">备选项：</div>';
			_html += '<ul class="answer-ul">';
			for (var i = 0; i < answerArr.length; i++) {
				_html += '<li><span>' + answerArr[i] + '</span></li>';
			}
			_html += '</ul>';
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
	}
	return _html;
}

/**
 * 绑定试题中的事件
 * @param qObj
 */
function bindQuestionEvent(qObj, type) {
	
	/*if($(qObj).find(".answer-result").prev().find("img").length==0){
		$(qObj).find(".answer-result").prev().css({
			"display": "inline-block"
		});
	}*/
	if(qObj.find(".question .answer-result").length==0){
		if(type==1){
			var lastObj = qObj.find(".question").children(":last");
			if(lastObj.is(".question-select") || lastObj.is(":text")){
				qObj.find(".question").append('<div class="answer-result"></div>');
			}else{
				lastObj.append('<div class="answer-result"></div>');
			}
		}else{
			var lastObj = qObj.find(".question:last").children().not("button").last();
			if(lastObj.is(".question-select") || lastObj.is(":text")){
				qObj.find(".question:last").children("button").first().before('<div class="answer-result"></div>');
			}else{
				lastObj.append('<div class="answer-result"></div>');
			}
		}
	}
	
	$(qObj).find(".s-active").bind("click", function(e) {
		e.stopPropagation();
		$(this).toggleClass("select-clicked").children("ul").toggle();
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
		addSpentTime();
	});

	$(qObj).find(".s-active li").click(function() {
		$(this).parent().siblings("h3").text($(this).text()).attr("data", $(this).attr("data"));
	});

	$(qObj).find(".option label").click(function() {
		if ($(this).find('input').prop("disabled")) {
			return;
		}
		if ($(this).find('input:radio').length > 0 && $(this).children(".checked").length > 0) {
			return;
		}
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
		addSpentTime();
	});
	$(qObj).find(".border").focus(function() {
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
		addSpentTime();
	});

	if (!isLearned) {
		if ($(qObj).hasClass("dian-tian-box")) {
			$(qObj).find(".question").mouseup(function() {
				var txt = window.getSelection ? window.getSelection().toString() : document.selection.createRange().text;
				if ($(this).find(".select-input").length > 0) {
					var value = $(this).find(".select-input").val();
					$(this).find(".select-input").val(value + txt);
				}
			});
			$(qObj).find(".question").find(".input-line,.inte-learn-submit").mouseup(function(e) {
				e = e || window.event;
				e.stopPropagation();
			});
			$(qObj).find(".input-line").click(function() {
				if ($(this).hasClass("select-input")) {
					return;
				}
				$(qObj).find(".input-line").removeClass("select-input");
				$(this).addClass("select-input");
			});
			$(qObj).find(".input-line").eq(0).click(); //默认第一个空为选中状态
		} else if ($(qObj).hasClass("dian-xuan-box")) {
			$(qObj).find(".answer-ul>li").click(function() {
				if (!$(this).children("span").hasClass("v-none")) {
					$(this).children("span").addClass("v-none");
					var text = $(this).children("span").text();
					$(qObj).find(".span-line").each(function(i, item) {
						if ($(item).text() == "") {
							$(item).text(text);
							return false;
						}
					});
				}
			});
			$(qObj).find(".span-line").click(function() {
				var text = $(this).text();
				if (!text == "") {
					$(qObj).find(".answer-ul>li").each(function(i, item) {
						if ($(item).children("span").text() == text) {
							$(item).children("span").removeClass("v-none");
							return false;
						}
					});
					$(this).text("");
				}
			});
		}
	}
}

/**
 * 提交答题结果
 */
function submitAnswer1(btn) {
	var curQuestion = $(btn).parent().prev("div").children().last();
	var i = cIndex - 1;
	var content = contentList[i];
	var otherQuestion = $("#contentList").children().last();
	if (!parseContent(curQuestion, content, otherQuestion)) { //答对
		kAnswerNum++;
		$(btn).hide().siblings(".next-btn").show().siblings(".help-btn").hide();
		otherQuestion.find(".inte-learn-submit").hide().siblings(".inte-learn-help").hide();
		$("#next-bar").show();
	} else {
		if (content.errorNum == null) {
			kErrorNum++;
			content.errorNum = 0;
		}
		content.errorNum++;
		if (content.errorNum >= 3) {
			$(btn).siblings(".help-btn").show();
			otherQuestion.find(".inte-learn-help").show();
		}
	}
}

/**
 * 解析答题结果
 * @param curQuestion
 * @param content
 */
function parseContent(curQuestion, content, otherQuestion) {
	var isWrong = false;
	var wrongTips = "";
	var tipsArr = new Array();
	if (content.wrongTips1 != null && content.wrongTips1 != "") {
		tipsArr.push(content.wrongTips1);
	}
	if (content.wrongTips2 != null && content.wrongTips2 != "") {
		tipsArr.push(content.wrongTips2);
	}
	if (content.wrongTips3 != null && content.wrongTips3 != "") {
		tipsArr.push(content.wrongTips3);
	}
	if (tipsArr.length > 0) {
		var tipsIndex = $(curQuestion).attr("data_ti");
		if (tipsIndex == null || tipsIndex == "") {
			tipsIndex = 0;
		} else if (tipsIndex >= tipsArr.length) {
			tipsIndex = tipsArr.length - 1;
		}
		wrongTips = tipsArr[tipsIndex];
		$(curQuestion).attr("data_ti", ++tipsIndex);
	}
	switch (content.questionType) {
		case 1: //单选
			var answer = $(curQuestion).find("input:checked").val();
			answer = $.trim(answer);
			if (answer == null || answer != content.correctOption) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
				$(curQuestion).find(".option input").prop("disabled", true);
//				otherQuestion.find(".option input[value='" + content.correctOption + "']").prop("checked", true);
				otherQuestion.find(".option input[value='" + content.correctOption + "']").prop("checked", true).parent().addClass("checked");
				otherQuestion.find(".option input").prop("disabled", true);
			}
			break;
		case 2: //多选
			var answer = "";
			$(curQuestion).find("input:checked").each(function(i, item) {
				answer += $(item).val() + ",";
			});
			answer = answer.substring(0, answer.length - 1);
			if (answer != content.correctOption) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
				$(curQuestion).find(".option input").prop("disabled", true);
				$(content.correctOption.split(",")).each(function(j, optionItem) {
					otherQuestion.find(".option input[value='" + optionItem + "']").prop("checked", true).parent().addClass("checked");
				});
				otherQuestion.find(".option input").prop("disabled", true);
			}
			break;
		case 3: //填空
		case 7:
			var completionWrong = false;
			$(curQuestion).find("input").each(function(i, item) {
				if ($(item).prop("disabled")) {
					return true;
				}
				if (i == 0 && !checkCompletionAnswer($(item).val(), content.optionA) ||
					i == 1 && !checkCompletionAnswer($(item).val(), content.optionB) ||
					i == 2 && !checkCompletionAnswer($(item).val(), content.optionC) ||
					i == 3 && !checkCompletionAnswer($(item).val(), content.optionD) ||
					i == 4 && !checkCompletionAnswer($(item).val(), content.optionE)) {
					completionWrong = true;
					$(item).addClass("input-error");
					return true;
				} else {
					$(item).blur().prop("disabled", true).css("color", "#008C72").val(tempRightAnswer).removeClass("input-error");
					otherQuestion.find("input").eq(i).prop("disabled", true).css("color", "#008C72").val(tempRightAnswer).removeClass(
						"input-error");
				}
			});
			if (completionWrong) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
				if (content.questionType == 10) {
					$(curQuestion).find(".question").unbind("mouseup");
					$(curQuestion).find(".input-line").removeClass("select-input").unbind("click");
				}
			}
			break;
		case 4: //文本
			break;
		case 5: //选填题
			var selectionWrong = false;
			var answerArr = content.correctOption.split(",");
			var result;
			$(curQuestion).find(".question-select").each(function(i, item) {
				if ($(item).hasClass("s-disabled")) {
					return true;
				}
				result = $(item).children("h3").attr("data");
				if (result == answerArr[i]) {
					$(item).unbind("click").removeClass("s-active").removeClass("input-error").addClass("s-disable");
					$(item).children("h3").css("color", "#008C72");
					$(item).children("ul").hide();

					var otherItem = otherQuestion.find(".question-select").eq(i);
					$(otherItem).find("ul>li[data='" + result + "']").click();
					$(otherItem).unbind("click").removeClass("s-active").removeClass("input-error").addClass("s-disable");
					$(otherItem).children("h3").css("color", "#008C72");
					$(otherItem).children("ul").hide();
				} else {
					$(item).addClass("input-error");
					selectionWrong = true;
				}
			});
			if (selectionWrong) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
			}
			break;
		case 6: //综合题
			var selectionWrong = false;
			var answerArr = content.correctOption.split(",");
			var result, answer = '';
			$(curQuestion).find(".question-select,:text").each(function(i, item) {
				var otherItem = otherQuestion.find(".question-select,:text").eq(i);
				if ($(item).is(":text")) { //填空
					if ($(item).prop("disabled")) {
						return true;
					}
					switch (i) {
						case 0:
							answer = content.optionA;
							break;
						case 1:
							answer = content.optionB;
							break;
						case 2:
							answer = content.optionC;
							break;
						case 3:
							answer = content.optionD;
							break;
						case 4:
							answer = content.optionE;
							break;
					}
					answer = $.trim(answer).replace(/\s+/g, " ");
					if (answer == $.trim($(item).val()).replace(/\s+/g, " ")) {
						$(item).blur().prop("disabled", true).css("color", "#008C72").val(answer).removeClass("input-error");
						$(otherItem).prop("disabled", true).css("color", "#008C72").val(answer).removeClass("input-error");
					} else {
						$(item).addClass("input-error");
						selectionWrong = true;
					}
				} else { //选填
					if ($(item).hasClass("s-disabled")) {
						return true;
					}
					result = $(item).children("h3").attr("data");
					if (result == answerArr[i]) {
						$(item).unbind("click").removeClass("s-active").removeClass("input-error").addClass("s-disable");
						$(item).children("h3").css("color", "#008C72");
						$(item).children("ul").hide();

						$(otherItem).find("ul>li[data='" + result + "']").click();
						$(otherItem).unbind("click").removeClass("s-active").removeClass("input-error").addClass("s-disable");
						$(otherItem).children("h3").css("color", "#008C72");
						$(otherItem).children("ul").hide();
					} else {
						$(item).addClass("input-error");
						selectionWrong = true;
					}
				}
			});
			if (selectionWrong) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
				$(curQuestion).find(".select-input").removeClass("select-input");
			}
			break;
		case 8:
			var completionWrong = false;
			$(curQuestion).find(".span-line").each(function(i, item) {
				if (i == 0 && !checkCompletionAnswer($(item).text(), content.optionA) ||
					i == 1 && !checkCompletionAnswer($(item).text(), content.optionB) ||
					i == 2 && !checkCompletionAnswer($(item).text(), content.optionC) ||
					i == 3 && !checkCompletionAnswer($(item).text(), content.optionD) ||
					i == 4 && !checkCompletionAnswer($(item).text(), content.optionE)) {
					completionWrong = true;
					return false;
				}
			});
			if (completionWrong) {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-error");
				if (wrongTips != null && wrongTips != "") {
					$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
				}
				$(curQuestion).find(".parse").hide();
				$(curQuestion).find(".span-line").click(); //归位
				isWrong = true;
			} else {
				$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				otherQuestion.find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass(
					"inte-learn-pass");
				$(curQuestion).find(".parse").show();
				$(curQuestion).find(".tips").hide();
				//解除绑定事件
				$(curQuestion).find(".span-line").unbind("click").css("color", "#008C72");
				$(curQuestion).find(".answer-ul>li>span").removeClass("v-none");
				$(curQuestion).find(".answer-ul>li").unbind("click");

				otherQuestion.find(".span-line").each(function(i, item) {
					switch (i) {
						case 0:
							$(item).text(content.optionA);
							break;
						case 1:
							$(item).text(content.optionB);
							break;
						case 2:
							$(item).text(content.optionC);
							break;
						case 3:
							$(item).text(content.optionD);
							break;
						case 4:
							$(item).text(content.optionE);
							break;
					}
				});
				otherQuestion.find(".span-line").unbind("click").css("color", "#008C72");
				otherQuestion.find(".answer-ul>li>span").removeClass("v-none");
				otherQuestion.find(".answer-ul>li").unbind("click");
			}
			break;
	}
	return isWrong;
}

/**
 * 检查填空题答案
 * @param item
 * @param answer
 * @returns {Boolean}
 */
var tempRightAnswer = "";

function checkCompletionAnswer(item, answer) {
	var reg1 = /\s+/g;
	var reg2 = /\s+(?=[!\?,\.'";:\+\-\*\/！？，。’“；：、])/g;
	item = $.trim(item);
	item = item.replace(reg1, " ");
	item = item.replace(reg2, "");
	var answerArr = answer.split("|");
	var answerItem;
	for(var i = 0; i<answerArr.length; i++){
		answerItem = answerArr[i];
		answerItem = $.trim(answerItem);
		answerItem = answerItem.replace(reg1, " ");
		answerItem = answerItem.replace(reg2, "");
		if(item==answerItem){
			tempRightAnswer = answerArr[i];
			return true;
		}
	}
	return false;
}

/*********************传统显示模式***********************/
var progress = 0; //正在学习的内容进度
var pIndex = 0; //正在学习内容的索引
function showContent2(isLearned) {
	if (pIndex >= grammarContentList.length) {
		return;
	}
	var content = grammarContentList[pIndex++];
	var _html = '';
	while (content.questionType >= 100) { //标题
		_html += getContentHtml2(content);
		if (pIndex >= grammarContentList.length) {
			content = null;
			break;
		}
		content = grammarContentList[pIndex++];
	}
	progress++;

	$("#progress").text(progress);
	var width = Math.round(progress * 100 / totalProgress) + "%";
	$(".progress-box .progress-inner").css({
		"width": width
	});
	_html += getContentHtml2(content, isLearned);
	$("#contentList").append(_html);
	if (!isLearned) {
		if (content.questionType == 4) {
			$("#next-bar").show();
		}
		bindQuestionEvent($("#contentList").children().last(), 2);
	}
	$('.inte-learn-box :radio,.inte-learn-box :checkbox').iCheck({
		checkboxClass: 'icheckbox_minimal-green',
		radioClass: 'iradio_minimal-grey',
		increaseArea: '20%' // optional
	});
}

function getContentHtml2(content, isLearned) {
	var _html = "";
	switch (content.questionType) {
		case 1: //单选题
			haveQuestion = true;
			_html += '<div class="inte-learn-box pace-last-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + content.question + '</div>';
				_html += '<ul class="option inte-learn-radio">';
				var index = new Date().getTime() + Math.random();
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="A" ';
					_html += content.correctOption == "A" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>A. ' + content.optionA + '</label></li>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="B" ';
					_html += content.correctOption == "B" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>B. ' + content.optionB + '</label></li>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="C" ';
					_html += content.correctOption == "C" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>C. ' + content.optionC + '</label></li>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '<li><label><input name="input_' + index + '" type="radio" value="D" ';
					_html += content.correctOption == "D" ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>D. ' + content.optionD + '</label></li>';
				}
				_html += '</ul>';
			} else {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + content.question;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input name="input_' + pIndex + '" type="radio" value="A"/>A. ' + content.optionA +
						'</label>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="B"/>B. ' + content.optionB +
						'</label>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="C"/>C. ' + content.optionC +
						'</label>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '</li><li><label><input name="input_' + pIndex + '" type="radio" value="D"/>D. ' + content.optionD +
						'</label>';
				}
				_html += '</ul>';
				_html +=
					'<button style="margin-left: 80px;" class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 2: //多选题
			haveQuestion = true;
			_html += '<div class="inte-learn-box pace-last-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			var question = content.question.replace(/<\/p>(\s)*$/, "<span style='color: #999;'>（多选）</span></p> ");
			if (isLearned) {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + question + '</div>';
			} else {
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">' + question;
//				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			if (isLearned) {
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input type="checkbox" value="A" ';
					_html += content.correctOption.indexOf("A") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>A. ' + content.optionA + '</label></li>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '<li><label><input type="checkbox" value="B" ';
					_html += content.correctOption.indexOf("B") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>B. ' + content.optionB + '</label></li>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '<li><label><input type="checkbox" value="C" ';
					_html += content.correctOption.indexOf("C") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>C. ' + content.optionC + '</label></li>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '<li><label><input type="checkbox" value="D" ';
					_html += content.correctOption.indexOf("D") >= 0 ? 'checked="checked" ' : '';
					_html += 'disabled="disabled"/>D. ' + content.optionD + '</label></li>';
				}
				_html += '</ul>';
			} else {
				_html += '<ul class="option inte-learn-radio">';
				if (content.optionA != null && content.optionA != "") {
					_html += '<li><label><input type="checkbox" value="A"/>A. ' + content.optionA + '</label>';
				}
				if (content.optionB != null && content.optionB != "") {
					_html += '</li><li><label><input type="checkbox" value="B"/>B. ' + content.optionB + '</label>';
				}
				if (content.optionC != null && content.optionC != "") {
					_html += '</li><li><label><input type="checkbox" value="C"/>C. ' + content.optionC + '</label>';
				}
				if (content.optionD != null && content.optionD != "") {
					_html += '</li><li><label><input type="checkbox" value="D"/>D. ' + content.optionD + '</label>';
				}
				_html += '</ul>';
				_html +=
					'<button style="margin-left: 80px;" class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 3: //填空题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			for (var i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					len = correctAnswer.length;
					if (chinese.test(correctAnswer)) {
						len += correctAnswer.match(chinese).length;
					}
					width = (len + 2) * 10;
					if (width < 80) {
						width = 80;
					}
					if (isLearned) {
						questionHtml += '<input class="border" style="width:' + width + 'px;" value="' + correctAnswer +
							'" disabled="disabled"/>';
					} else {
						questionHtml += "<input class='border' style='width:" + width + "px;'/>";
					}
				}
			}
			_html += '<div class="inte-learn-box pace-last-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 4: //文本
			haveQuestion = true;
			_html += '<div class="inte-learn-box pace-last-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			_html += '<div class="question inte-learn-sm-text inte-learn-mid-text">' + content.question;
			_html += '</div>';
			_html += '</div></div>';
			break;
		case 5: //下拉选择题
			haveQuestion = true;
			var _select = "";
			var width = 0,
				length = 0,
				len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			// var questionHtml = content.question.replace(/<p[^>]*>/g, "");
			var questionHtml = content.question
			questionHtml = questionHtml.replace(/<\/p>/g, "<br>");
			questionHtml = questionHtml.replace(/<br>+\s*$/, "");
			var questionArr = questionHtml.split(/_{3,}/);
			for (var i = 0; i < questionArr.length - 1; i++) {
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				} else {
					break;
				}
				optionArr = optionStr.split("|");
				answer = optionArr[parseInt(answerIndexArr[i])];
				if (isLearned) {
					if (chinese.test(answer)) {
						width = (answer.length + answer.match(chinese).length) * 10 + 40;
					} else {
						width = answer.length * 10 + 40;
					}
					if (width < 80) {
						width = 80;
					}
					_select = "<div class='question-select s-disable'>";
					_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + answer +
						"</h3>";
					_select += "</div>";
				} else {
					len = 0;
					for (var j = 0; j < optionArr.length; j++) {
						length = optionArr[j].length;
						if (chinese.test(optionArr[j])) {
							length += optionArr[j].match(chinese).length;
						}
						if (len < length) {
							len = length;
						}
					}
					width = len * 10 + 40;
					if (width < 80) {
						width = 80;
					}
					_select = "<div class='s-active question-select'>";
					_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>请选择</h3>";
					_select += "<ul>";
					$(optionArr).each(function(j, option) {
						if (option != null && option != "") {
							_select += "<li data='" + j + "' style='width:" + width + "px'>" + option + "</li>";
						}
					});
					_select += "</ul></div>";
				}
				questionHtml = questionHtml.replace(/_{3,}/, _select);
			}
			_html += '<div class="inte-learn-box pace-last-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 6: //综合题
			haveQuestion = true;
			var _select = "";
			var questionHtml = "";
			var width = 0,
				length = 0,
				len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			// var questionTemp = content.question.replace(/<p[^>]*>/g, "");
			var questionTemp = content.question
			questionTemp = questionTemp.replace(/<\/p>/g, "<br>");
			questionTemp = questionTemp.replace(/<br>+\s*$/, "");
			var questionArr = questionTemp.split(/_{3,}/);
			for (var i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i >= questionArr.length - 1) {
					break;
				}
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				} else {
					break;
				}
				optionArr = optionStr.split("|");
				if (isLearned) {
					if (optionArr.length == 1) {
						answer = optionArr[0];
					} else {
						answer = optionArr[parseInt(answerIndexArr[i])];
					}
					if (chinese.test(answer)) {
						width = (answer.length + answer.match(chinese).length) * 10 + 40;
					} else {
						width = answer.length * 10 + 40;
					}
					if (width < 80) {
						width = 80;
					}
					if (optionArr.length == 1) {
						_select = '<input class="border" style="width:' + width + 'px;" value="' + answer + '" disabled="disabled"/>';
					} else {
						_select = "<div class='question-select s-disable'>";
						_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>" + answer +
							"</h3>";
						_select += "</div>";
					}
				} else {
					len = 0;
					for (var j = 0; j < optionArr.length; j++) {
						length = optionArr[j].length;
						if (chinese.test(optionArr[j])) {
							length += optionArr[j].match(chinese).length;
						}
						if (len < length) {
							len = length;
						}
					}
					width = len * 10 + 20;
					if (width < 80) {
						width = 80;
					}
					if (optionArr.length == 1) {
						_select = "<input class='border' style='width:" + width + "px;'/>";
					} else {
						_select = "<div class='s-active question-select'>";
						_select += "<h3 data='' style='width:" + width + "px; background-position-x: " + (width - 10) + "px;'>请选择</h3>";
						_select += "<ul>";
						$(optionArr).each(function(j, option) {
							if (option != null && option != "") {
								_select += "<li data='" + j + "' style='width:" + width + "px'>" + option + "</li>";
							}
						});
						_select += "</ul></div>";
					}
				}
				questionHtml += _select;
			}
			_html += '<div class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 7: //点填题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			for (i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					len = correctAnswer.length;
					if (chinese.test(correctAnswer)) {
						len += correctAnswer.match(chinese).length;
					}
					width = (len + 2) * 10;
					if (width < 80) {
						width = 80;
					}
					if (isLearned) {
						questionHtml += '<input class="input-line" style="width:' + width + 'px;" value="' + correctAnswer +
							'" disabled="disabled"/>';
					} else {
						questionHtml += "<input class='input-line' style='width:" + width + "px;'/>";
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/, "<span style='color: #999;'>（可以划选单词填空）</span></p>");
			_html += '<div class="inte-learn-box pace-last-box dian-tian-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 8: //点选题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0,
				len = 0;;
			var correctAnswer = "";
			var answerArr = new Array();
			for (i = 0; i < questionArr.length; i++) {
				questionHtml += questionArr[i];
				if (i < questionArr.length - 1) {
					switch (i) {
						case 0:
							correctAnswer = content.optionA;
							break;
						case 1:
							correctAnswer = content.optionB;
							break;
						case 2:
							correctAnswer = content.optionC;
							break;
						case 3:
							correctAnswer = content.optionD;
							break;
						case 4:
							correctAnswer = content.optionE;
							break;
					}
					answerArr.push(correctAnswer);
					len = correctAnswer.length;
					if (chinese.test(correctAnswer)) {
						len += correctAnswer.match(chinese).length;
					}
					width = (len + 2) * 10;
					if (width < 80) {
						width = 80;
					}
					if (isLearned) {
						questionHtml += '<span class="span-line">' + correctAnswer + '</span>';
					} else {
						questionHtml += '<span class="span-line"></span>';
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/, "<span style='color: #999;'>（选词填空）</span></p>");
			_html += '<div class="inte-learn-box pace-last-box dian-xuan-box">';
			_html += '<div class="content inte-learn-mid-text-wrap">';
			if (isLearned) {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml + '</div>';
			} else {
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">' +
					questionHtml;
//				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-yello rounded" onclick="submitAnswer2(this);">提交</button>';
				_html +=
					'<button style="margin-left: 30px;" class="inte-learn-help my-btn btn-green rounded" onclick="seekHelp(this);">求助</button>';
				_html += '</div>';
			}
			answerArr.sort(function() {
				return Math.random() > 0.5 ? -1 : 1;
			}); //随机排序
			_html += '<div class="answer-item-div">备选项：</div>';
			_html += '<ul class="answer-ul">';
			for (var i = 0; i < answerArr.length; i++) {
				_html += '<li><span>' + answerArr[i] + '</span></li>';
			}
			_html += '</ul>';
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += '<div class="tips-parse">提示：</div>';
			_html += '<div class="tips-parse-text"></div>';
			_html += '<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if (content.parse != null && content.parse != "") {
				_html += '<div class="tips-parse">解析：</div>';
				_html += '<div class="tips-parse-text">' + content.parse + '</div>';
				_html += '<br style="clear: both;"/>';
			}
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 100: //分割线
			_html += "<div class='cut-line-div'></div>";
			break;
		case 101: //一级标题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="inte-learn-big-title">';
			_html += '<div class="inte-learn-big-title-inner">';
			_html += '<span></span>';
			_html += '<h2>' + content.question + '</h2>';
			_html += '</div>';
			_html += '</div></div>';
			break;
		case 102: //二级标题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="inte-learn-mid-title">' + content.question + '</div>';
			_html += '</div>';
			break;
		case 103: //三级标题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="inte-learn-third-title">' + content.question + '</div>';
			_html += '</div>';
			break;
		case 104: //四级标题
			_html += '<div class="inte-learn-box">';
			_html += '<div class="inte-learn-sm-title">【' + content.question + '】</div>';
			_html += '</div>';
			break;
	}
	return _html;
}



/**
 * 提交大题结果
 * @param btn
 */
function submitAnswer2(btn) {
	var curQuestion = $(btn).parents(".content");
	var i = pIndex - 1;
	var content = grammarContentList[i];
	var otherQuestion = $("#card_mode .s-container" + subtitleList[sIndex - 1].type).find(".card-item").children().last();
	if (!parseContent(curQuestion, content, otherQuestion)) { //答对
		kAnswerNum++;
		$(btn).hide().siblings(".inte-learn-help").hide();
		$("#next-bar").show();
		otherQuestion.parent().siblings(".next-btn").show().siblings("button").hide();
	} else {
		if (content.errorNum == null) {
			kErrorNum++;
			content.errorNum = 0;
		}
		content.errorNum++;
		if (content.errorNum >= 3) {
			$(btn).siblings(".inte-learn-help").show();
			otherQuestion.parent().siblings(".help-btn").show();
		}
	}
}


/**
 * 显示接下来的内容
 */
function showNext() {
	$("#card_mode .s-container" + subtitleList[sIndex - 1].type).find(".next-btn").hide();
	$("#next-bar").hide();
	$("#contentList .answer-result").remove();
	$("#card_mode .answer-result").remove();
	if (pIndex >= grammarContentList.length) {
		updateUserKnowledgeStatus();
	} else {
		showContent();
	}
}

/***********************求助功能***********************/
/**
 * 求助
 * @param btn
 */
function seekHelp(btn) {
	var content;
	if (showMode == 1) {
		content = contentList[cIndex - 1];
	} else {
		content = grammarContentList[pIndex - 1];
	}
	showContentAnswer(content);
}

/**
 * 显示求助结果
 * @param content
 */
function showContentAnswer(content) {
	var _html = '<div class="contentAnswerDia">';
	_html += '<div style="margin-bottom: 10px;">求助成功！</div>';
	switch (content.questionType) {
		case 1: //单选题
		case 2: //多选题
			_html += '<div>正确答案为：' + content.correctOption + '</div>';
			break;
		case 3: //填空题
		case 7:
		case 8:
			_html += '<div>正确答案为：';
			_html += '<ul class="dia-answer-ul">';
			if (content.optionA != null && content.optionA != "") {
				_html += '<li>' + content.optionA + '</li>';
			}
			if (content.optionB != null && content.optionB != "") {
				_html += '<li>' + content.optionB + '</li>';
			}
			if (content.optionC != null && content.optionC != "") {
				_html += '<li>' + content.optionC + '</li>';
			}
			if (content.optionD != null && content.optionD != "") {
				_html += '<li>' + content.optionD + '</li>';
			}
			if (content.optionE != null && content.optionE != "") {
				_html += '<li>' + content.optionE + '</li>';
			}
			_html += '</ul>';
			_html += '</div>';
			break;
		case 5: //选填题
			var answerArr = content.correctOption.split(",");
			var optionStr = '',
				optionArr;
			_html += '<div>正确答案为：';
			_html += '<ul class="dia-answer-ul">';
			$(answerArr).each(function(i, item) {
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				}
				if(optionStr=="|||"){
					return true;
				}
				optionArr = optionStr.split("|");
				_html += '<li>' + optionArr[parseInt(item)] + '</li>';
			});
			_html += '</ul>';
			_html += '</div>';
			break;
		case 6: //综合题
			var answerArr = content.correctOption.split(",");
			var optionStr = '',
			optionArr;
			_html += '<div>正确答案为：';
			_html += '<ul class="dia-answer-ul">';
			$(answerArr).each(function(i, item) {
				if (i == 0) {
					optionStr = content.optionA;
				} else if (i == 1) {
					optionStr = content.optionB;
				} else if (i == 2) {
					optionStr = content.optionC;
				} else if (i == 3) {
					optionStr = content.optionD;
				} else if (i == 4) {
					optionStr = content.optionE;
				}
				if(optionStr=="|||"){
					return true;
				}
				if(item<0){
					_html += '<li>' + optionStr + '</li>';
				}else{
					optionArr = optionStr.split("|");
					_html += '<li>' + optionArr[parseInt(item)] + '</li>';
				}
			});
			_html += '</ul>';
			_html += '</div>';
			break;
	}
	_html += '</div>';
	var d = dialog({
		title: "系统消息",
		content: _html,
		width: "500px",
		padding: "20px",
		okValue: "我知道啦",
		ok: true
	});
	d.showModal();
}

/**
 * 查漏补缺
 */
function toFillVacancy() {
	var d = dialog({
		title: "查漏补缺",
		content: "查漏补缺后将会清除未完全掌握的知识点学习状态！确定执行该操作吗？",
		okValue: "确定",
		cancelValue: "取消",
		cancel: true,
		ok: function() {
			fillVacancy();
			return true;
		}
	});
	d.showModal();
}

function showFillVacancyDia() {
	var _content = '<div> 你已经学完了本节所有内容，其中已掌握知识点<span style="color:#00b48b">';
	_content += $("#progress-list>.water-drop-pass").length;
	_content += '</span>个，未掌握知识点<span style="color:#d6523c">';
	_content += $("#progress-list>.water-drop-fail").length;
	_content += '</span>个，尚未完全掌握的知识点<span style="color:#ffa442">';
	_content += $("#progress-list>.water-drop-warning").length;
	_content += '</span>个，是否趁热打铁，立即对弱点和盲点知识点进行“查漏补缺”？</div>';
	_content += '<div> 注："查漏补缺"会将你本节中盲点和弱点的知识点学习状态清空。</div>';
	var d = dialog({
		title: "温馨提示",
		content: _content,
		width: "400px",
		padding: "20px",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "查漏补缺",
		ok: function() {
			fillVacancy();
			return true;
		}
	});
	d.showModal();
}

function fillVacancy() {
	$.post("/grammar/learn/fillVacancy", {
		"unitId": unitId
	}, function(data) {
		if (data) {
			$("#fillVacancy,#learnAgain").hide();
			isFirst = false;
			kIndex = 0;
			knowledgeArr = new Array();
			grammarContentList = new Array();
			$("#contentList").html("");
			getGrammarCard();
		} else {
			alert("查漏补缺出现错误！");
		}
		againFlag = true;
	}, "json");
}

/**
 * 再学一遍
 */
var againFlag = true;

function toLearnAgain() {
	if (!againFlag) {
		return;
	}
	againFlag = false;
	var d = dialog({
		title: "再学一遍",
		content: "再学一遍后将会清除该节的互动学练学习状态！你确定要再学一遍“" + grammarCard.name + "”吗？",
		okValue: "确定",
		cancelValue: "取消",
		cancel: true,
		ok: function() {
			learnAgain();
			return true;
		}
	});
	d.showModal();
}

function showLearnAgainDia() {
	var d = dialog({
		title: "温馨提示",
		content: "恭喜你已经学完了本节所有内容，并且已经掌握所有知识点，是否进行“再学一遍”，巩固加深记忆？",
		width: "400px",
		padding: "20px",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "再学一遍",
		ok: function() {
			learnAgain();
			return true;
		}
	});
	d.showModal();
}

function learnAgain() {
	if (!againFlag) {
		return;
	}
	$.post("/grammar/learn/learnAgain", {
		"unitId": unitId
	}, function(data) {
		if (data) {
			$("#fillVacancy,#learnAgain").hide();
			isFirst = false;
			kIndex = 0;
			knowledgeArr = new Array();
			grammarContentList = new Array();
			$("#contentList").html("");
			getGrammarCard();
		} else {
			alert("再学一遍出现错误！");
		}
		againFlag = true;
	}, "json");
}

var last_add_time = new Date().getTime();
var invalidTime = 0;

function addSpentTime() {
	var nowTime = new Date().getTime();
	var addTime = Math.round((nowTime - last_add_time) / 1000);
	if (addTime > 30) {
		invalidTime += addTime;
	}
	last_add_time = nowTime;
}

function goBack() {
//	exitGrammarLearn();
//	if (_position != null) {
	window.top.close()
//	} else {
//		window.location.href = "/grammar/jump/toLearningCenter?courseId=" + courseId + "&unitId=" + unitId;
//	}
}

window.onbeforeunload = function() {
	exitGrammarLearn();
};

var exited = false;
function exitGrammarLearn() {
	if (!exited) {
		exited = true;
		$.ajax({
			url: '/grammar/learn/exitGrammarLearn',
			cache: false,
			async: false,
			type: "post",
			dataType: "json",
			success: function(data) {}
		});
	}
}

function goTop() {
	$("html,body").animate({
		'scrollTop': 0
	}, 1000);
}


/***************每日计划****************/
//显示每日计划进度
function showDailyPlanProgress(planProgress){
	if(typeof(setDailyPlanProgress)=="undefined"){
		$("body").append("<div id='dailyPlanProgress'></div>");
		$("#dailyPlanProgress").load("/insideHtml/learn-progress.html",function(){setDailyPlanProgress(planProgress);});
	}else{
		setDailyPlanProgress(planProgress);
	}
}

/**
 * 计划完成弹窗
 */
function showPlanOverTip(haveNextPlan){
	if(haveNextPlan){
		var d = dialog({
			title: "每日计划",
			content: "恭喜你已经完成该计划条目，是否进行下一条计划条目？",
			okValue: "确定",
			cancelValue: "返回",
			ok: function(){
				exitGrammarLearn();
				nextPlanContent();
			},
			cancel: function(){
				exitGrammarLearn();
				goDailyPlanCenter();
			}
		});
		d.showModal();
	}else{
		var d = dialog({
			title: "每日计划",
			content: "恭喜你已经完成了该计划的所有内容！",
			cancel: false,
			okValue: "确定",
			ok: function(){
				exitGrammarLearn();
				goDailyPlanCenter();
			}
		});
		d.showModal();
	}
}

function goDailyPlanCenter(){
	window.location.href = "/user/goDailyPlanCenter";
}
function nextPlanContent(){
	window.location.href = "/user/nextPlanContent";
}

