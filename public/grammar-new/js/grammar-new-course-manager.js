var versionMap = new Array();
var levelNum = 20; //本章检测题最低限制量
$(function(){
	var courseId = Request["courseId"];
	if(courseId==null){
		return;
	}
	$.post("/newGrammarManager/getCourseFullNameById",{"courseId": courseId},
			function(data){
		if(data){
			$("#grade option[value='"+data.gradeId+"']").prop("selected", true);
			getVersionList(data.versionId);
			getCourseList();
		}
	},"json");
});

/**
 * 加载版本列表
 */
function getVersionList(versionId){
	var option = "<option value='0'>选择版本</option>";
	var grade = $("#grade").val();
	if(grade==0){
		$("#version").html(option);
		return;
	}
	if(versionMap[grade]==null){
		$.get({
			type: "get",
			url: "/newGrammarManager/getVersionList",
			cache: false,
			async: false,
			data: {"grade": grade},
			dataType: "json",
			success: function(data){
				versionMap[grade] = data;
			}
		});
	}
	$(versionMap[grade]).each(function(i,item){
		option += "<option value='"+item.id+"'>"+item.name+"（"+item.courseNum+"）</option>";
	});
	$("#version").html(option);
	if(versionId){
		$("#version option[value='"+versionId+"']").prop("selected", true);
	}
}


/**
 * 获取编辑章列表
 */
var courseList;
var courseGrade = 0;//章的年级
function getCourseList(){
	courseGrade = $("#grade").val();
	var versionId = $("#version").val();
	if(versionId==null || versionId==0){
		return;
	}
	$.post("/newGrammarManager/getCourseList",{"versionId": versionId},function(data){
		courseList = data;
		if(data==null || data.length==0){
			$("#dataTable tbody").html("<tr><td style='color: red;' colspan='"+$("#dataTable thead th").length+"'>还没有章哦！</td></tr>");
			return;
		}
		var content = "";
		var statusCss = "", operation = "";
		$(data).each(function(i,item){
			operation = "";
			if(i==0){
				operation += "<a style='margin-left: 23px;' href='javascript:adjustCourse("+versionId+","+i+",\"down\")'>↓</a>"; 
			}else if(i==data.length-1){
				operation += "<a style='margin-right: 23px;' href='javascript:adjustCourse("+versionId+","+i+",\"up\")'>↑</a>"; 
			}else{
				operation += "<a href='javascript:adjustCourse("+versionId+","+i+",\"up\")'>↑</a>"; 
				operation += "<a style='margin-left: 10px;' href='javascript:adjustCourse("+versionId+","+i+",\"down\")'>↓</a>"; 
			}
			operation += "<a style='margin-left: 10px;' href='javascript:updateCourse("+i+")'>修改</a>"; 
//			operation += "<a style='margin-left: 10px;' href='javascript:copyCourse("+item.id+")'>复制</a>"; 
			if(!item.isApply && item.publishTime==null){
				operation += "<a style='margin-left: 10px;' href='javascript:deleteCourse("+item.id+")'>删除</a>";
				operation += "<a style='margin-left: 10px;' href='javascript:applyPublishCourse("+i+")'>申请发布</a>";
			}else if(!item.isApply && item.modifyTime>item.publishTime){
				operation += "<a style='margin-left: 10px;' href='javascript:applyPublishCourse("+i+")'>申请发布</a>";
			}
			
			content += "<tr>";
			content += "<td>"+(i+1)+"</td>";
			content += "<td>"+item.name+"</td>";
			if(userType==2){
				content += "<td style='text-align: left; padding: 0 20px'>"+operation+"</td>";
			}
			item.levelQuestionNum = item.levelQuestionNum1 + item.levelQuestionNum2 + item.levelQuestionNum3;
			if(item.levelQuestionNum<levelNum){
				content += "<td style='color: red;'>"+item.levelQuestionNum+"题（"+item.levelQuestionNum1+"/"+item.levelQuestionNum2+"/"+item.levelQuestionNum3+"）</td>";
			}else{
				content += "<td style='color: green;'>"+item.levelQuestionNum+"题（"+item.levelQuestionNum1+"/"+item.levelQuestionNum2+"/"+item.levelQuestionNum3+"）</td>";
			}
			content += "<td><a href='javascript:levelQuestionManager("+item.id+");'>进入</a></td>";
			
			content += "<td>"+item.unitNum+"个</td>";
			content += "<td><a href='javascript:unitManager("+item.id+");'>进入</a></td>";
			if(item.isApply){
				content += "<td><span class='tips-span applyed' title='已申请发布，等待处理中。' onclick='showPublishRecord("+i+");'></span>发布申请中</td>";
			}else if(item.publishTime==null){
				content += "<td><span class='tips-span no-publish' title='未发布'></span>未发布</td>";
			}else if(item.modifyTime>item.publishTime){
				content += "<td><span class='tips-span modified' title='有修改的内容未发布，点击查看发布详情。' onclick='showPublishRecord("+i+");'></span>修改未发布</td>";
			}else{
				content += "<td><span class='tips-span published' title='已发布，点击查看发布详情。' onclick='showPublishRecord("+i+");'></span>已发布</td>";
			}
			content += "<td>"+new Date(item.modifyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
			content += "<td>"+(item.publishTime==null? "--" : new Date(item.publishTime).Format("yyyy-MM-dd hh:mm:ss"))+"</td>";
			content += '</tr>';
		});
		$("#dataTable tbody").html(content);
		$(".tips-span").tipso({
			useTitle: true,
			position: 'right',
			width: '200px'
		});
	},"json");
}

/**
 * 添加章
 * 
 */
function addCourse(){
	var _html = "<div id='course-dialog'>"+$("#course-div").html()+"<div>";
	var d = dialog({
		title: "添加章",
		content: _html,
		padding: "20px",
		width: "400px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			var grade = $("#course-dialog .grade").val();
			if(grade==0){
				alert("请选择学段！");
				return false;
			}
			var newCourse = new Object(); //添加后的新章
			newCourse.versionId = $("#course-dialog .version").val();
			if(newCourse.versionId==0){
				alert("请选择版本！");
				return false;
			}
			newCourse.name = $.trim($("#course-dialog .course").val());
			if(newCourse.name==null || newCourse.name==""){
				alert("请输入章名称！");
				return false;
			}
			var flag = false;
			$.ajax({
				 type: "POST",
		         url: "/newGrammarManager/addCourse",
		         async: false,
		         data: newCourse,
		         dataType: "json",
		         success: function(data){
		        	 if(data){
		        		 $("#grade option[value='"+grade+"']").prop("selected", true);
		        		 versionMap[grade] = null;
		        		 getVersionList(newCourse.versionId);
		        		 getCourseList();
		        	 }else{
		        		 alert("添加章失败！");
		        	 }
		        	 flag = data;
		         }
			});
			return flag;
		}
	});
	d.showModal();
	var grade = $("#grade").val();
	if(grade>0){
		$("#course-dialog .grade option[value='"+grade+"']").prop("selected", true);
	}
	var versionId = $("#version").val();
	if(versionId>0){
		$("#course-dialog .version").html($("#version").html());
		$("#course-dialog .version option[value='"+versionId+"']").prop("selected", true);
	}
}
/**
 * 修改章
 * @param order
 */
function updateCourse(index){
	var course = courseList[index];
	var newCourse = new Object();
	newCourse.id = course.id;
	var _html = "<div id='course-dialog'>"+$("#course-div").html()+"<div>";
	var d = dialog({
		title: "修改章",
		content: _html,
		padding: "20px",
		width: "400px",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		ok: function(){
			var newGrade = $("#course-dialog .grade").val();
			if(newGrade==0){
				alert("请选择学段！");
				return false;
			}
			newCourse.versionId = $("#course-dialog .version").val();
			if(newCourse.versionId==0){
				alert("请选择版本！");
				return false;
			}
			newCourse.name = $.trim($("#course-dialog .course").val());
			if(newCourse.name==null || newCourse.name==""){
				alert("请输入章名称！");
				return false;
			}
			if(newCourse.versionId==course.versionId && 
					newCourse.name==course.name){ //修改时，并没有真正的修改
				return true;
			}
			var flag = false;
			$.ajax({
				type: "POST",
				url: "/newGrammarManager/updateCourse",
				async: false,
				data: newCourse,
				dataType: "json",
				success: function(data){
					if(data){
						$("#grade option[value='"+newGrade+"']").prop("selected", true);
						versionMap[courseGrade] = null;
						versionMap[newGrade] = null;
						getVersionList(newCourse.versionId);
						getCourseList();
					}else{
						alert("修改章失败！");
					}
					flag = data;
				}
			});
			return flag;
		}
	});
	d.showModal();
	$("#course-dialog .grade option[value='"+courseGrade+"']").prop("selected", true);
	$("#course-dialog .version").html($("#version").html());
	$("#course-dialog .version option[value='"+course.versionId+"']").prop("selected", true);
	$("#course-dialog .course").val(course.name);
}

/**
 * 章复制功能
 * @param courseId
 */
function copyCourse(courseId){
	var d = dialog({
		title: "复制章",
		content: "确定复制该章及章下面的所有内容吗？",
		cancelValue: "取消",
		okValue: "确认",
		cancel: true,
		zIndex: 999,
		ok: function(){
			var flag = false;
			$.ajax({
				type: "POST",
				url: "/newGrammarManager/copyCourse",
				async: false,
				data: {"courseId": courseId},
				dataType: "json",
				success: function(data){
					if(data){
						getCourseList();
					}else{
						alert("复制章操作失败！");
					}
					flag = data;
				}
			});
			return flag;
		}
	});
	d.showModal();
}


/**
 * 弹窗内部加载版本列表
 */
function getDialogVersionList(){
	var option = "<option value='0'>选择版本</option>";
	var grade = $("#course-dialog .grade").val();
	if(grade==0){
		$("#course-dialog .version").html(option);
		return;
	}
	if(versionMap[grade]==null){
		$.post("/newGrammarManager/getVersionList",{"grade": grade},function(data){
			versionMap[grade] = data;
			$(data).each(function(i,item){
				option += "<option value='"+item.id+"'>"+item.name+"（"+item.courseNum+"）</option>";
			});
			$("#course-dialog .version").html(option);
		},"json");
	}else{
		$(versionMap[grade]).each(function(i,item){
			option += "<option value='"+item.id+"'>"+item.name+"（"+item.courseNum+"）</option>";
		});
		$("#course-dialog .version").html(option);
	}
}



/**
 * 调整章顺序
 */
function adjustCourse(versionId,index,direction){
	var srcOrder = courseList[parseInt(index)].disporder;
	var desOrder;
	if(direction == "up"){
		desOrder = courseList[parseInt(index)-1].disporder;
	}else{
		desOrder = courseList[parseInt(index)+1].disporder;
	}
	
	$.post("/newGrammarManager/adjustCourse",
			{"versionId": versionId, "srcOrder": srcOrder, "desOrder": desOrder},
			function(data){
				if(data){
					getCourseList();
				}else{
					alert("调整失败！");
				}
			},"json");
}

/**
 * 删除章
 * @param versionId
 */
function deleteCourse(courseId){
	var d = dialog({
		title: "删除章",
		content: "删除章，该章下面的节、本章检测试题等信息也会一并删除。你确定要执行该操作吗？",
		cancelValue: "我再想想",
		cancel: true,
		okValue: "确认",
		ok: function(){
			$.post("/newGrammarManager/deleteCourse",
					{"courseId": courseId},
					function(data){
						if(data){
							versionMap[courseGrade] = null;
							var versionId = $("#version").val();
							getVersionList(versionId);
							getCourseList();
						}else{
							alert("删除失败！");
						}
					},"json");
		}
	});
	d.showModal();
}

/**
 * 申请发布章
 * @param index
 */
function applyPublishCourse(index){
	var course = courseList[index];
	var _html = "<div id='publish-dia'>"+$("#publish-div").html()+"</div>"
	var d = dialog({
		title: "申请发布",
		content: _html,
		padding: "20px 20px 0",
		width: "550px",
		cancelValue: "取消",
		okValue: "确定",
		cancel: true,
		ok: function(){
			var comment = $.trim($("#publish-dia .comment").val());
			var courseId = course.id;
			var flag = false;
			$.ajax({
				type: "post",
				url: "/newGrammarManager/applyPublishCourse",
				async: false,
				data: {"courseId": courseId, "comment": comment},
				dataType: "json",
				success: function(data){
					if(data){
						alert("申请发布成功！");
						getCourseList();
					}else{
						alert("申请发布失败！");
					}
					flag = data;
				}
			});
			return flag;
		}
	});
	$("#publish-dia .courseName").text(course.name);
	$("#publish-dia .unitNum").text(course.unitNum);
	$("#publish-dia .levelQuestionNum").text(course.levelQuestionNum+"题");
	if(course.levelQuestionNum>=levelNum){
		$("#publish-dia .levelQuestionNum").css("color","green");
	}else{
		$("#publish-dia .levelQuestionNum").css("color","red");
	}
	d.showModal();
}

/**
 * 展示发布详情
 * @param index
 */
function showPublishRecord(index){
	var course = courseList[index];
	var _html = "<div id='publish-record' style='overflow-y: auto;max-height: 350px;'>"+$("#publish-record-div").html()+"</div>"
	var d = dialog({
		title: "发布详情",
		content: _html,
		padding: "20px",
		width: "570px"
	});
	d.showModal();
	$("#publish-record .courseName").text(course.name);
	$("#publish-record .unitNum").text(course.unitNum);
	$("#publish-record .levelQuestionNum").text(course.levelQuestionNum+"题");
	if(course.levelQuestionNum>=levelNum){
		$("#publish-record .levelQuestionNum").css("color","green");
	}else{
		$("#publish-record .levelQuestionNum").css("color","red");
	}
	if(course.publishRecord==null){
		$.ajax({
			type: "get",
			url: "/newGrammarManager/getPublishRecord",
			cache: false,
			async: false,
			dataType: "json",
			data: {"courseId": course.id},
			success: function(data){
				course.publishRecord = data; //course与courseList[index]指向同一个对象，所以courseList[index]的值也会跟着修改
			}
		});
	}
	$("#publish-record .publishNum").text(course.publishRecord.length+"次");
	_html = "";
	$(course.publishRecord).each(function(i, item){
		if(i==7){
			_html += "<tr style='background-color: #fff;'><td style='text-align: center;' colspan='2'><a href='javascript:showAllPublishRecord("+index+");'>显示全部</a></td></tr>";
			return false;
		}
		_html += "<tr style='background-color: #fff;'>";
		_html +="<td style='vertical-align: top; width: 155px;'>"+new Date(item.applyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
		if(item.comment==null || $.trim(item.comment)==""){
			_html +="<td>--未填--</td>";
		}else{
			_html +="<td>"+item.comment+"</td>";
		}
		_html += "</tr>";
	});
	$("#publish-record .publishRecord").html(_html);
}

/**
 * 显示全部发布记录
 * @param index
 */
function showAllPublishRecord(index){
	var course = courseList[index];
	var _html = "";
	$(course.publishRecord).each(function(i, item){
		_html += "<tr style='background-color: #fff;'>";
		_html +="<td style='vertical-align: top; width: 155px;'>"+new Date(item.applyTime).Format("yyyy-MM-dd hh:mm:ss")+"</td>";
		if(item.comment==null || $.trim(item.comment)==""){
			_html +="<td>--未填--</td>";
		}else{
			_html +="<td>"+item.comment+"</td>";
		}
		_html += "</tr>";
	});
	$("#publish-record .publishRecord").html(_html);
};




/**
 * 进入节管理
 * @param courseId
 */
function unitManager(courseId){
	window.location.href = "grammar-new-unit-manager.html?courseId="+courseId;
}

/**
 * 进入本章检测管理
 * @param index
 */
function levelQuestionManager(courseId){
	window.location.href = "grammar-new-level-manager.html?courseId="+courseId;
}

