/**---------------------节管理-------------------------*/
var courseId = Request["courseId"];
var unitId = Request["unitId"];
$(function(){
	loadCourseName();
	getKnowledgeList();
	getQuestionList();
	loadConfigInfo();
});


/**
 * 加载章名
 */
var courseName;
function loadCourseName(){
	$.post("/newGrammarManager/getUnitName",{"unitId": unitId},
			function(data){
			$("#courseName").text(data.versionName+"-"+data.courseName+"-"+data.unitName);
	},"json");
}

/**
 * 获取知识点列表
 */
var knowMap = {};
var knowList;
function getKnowledgeList(){
	$.post("/newGrammarManager/getKnowledgeListWithNum",{"unitId": unitId},
			function(data){
			knowList = data.knowList;
			knowMap = {};
			$(data.knowList).each(function(i,item){
				if(knowMap[item.parentId]==null){
					knowMap[item.parentId] = new Array();
				}
				knowMap[item.parentId].push(item);
			});
		var _html = '';
		_html += '<li class="'+(knowMap[0]==null?"":"has-children")+'">';
		_html += '<div data="allKnow" class="title title0"><label>全部知识点（'+data.allKnowNum+'）</label><i class="show"></i></div>';
		_html += showKnowList(0);
		_html += '</li>';
		_html += '<li><div data="noKnow" class="title title0"><label>未关联（'+data.noKnowNum+'）<label><i class="show"></i></div></li>';
		$("#knowledgeList").html(_html);
		$("#knowledgeList .has-children>.title>i").click(function(e){
			e = e || window.event;
			e.stopPropagation();
			$(this).toggleClass("show");
			$(this).parent().next("ul").slideToggle();
		});
		$("#knowledgeList .title").click(function(){
			if($(this).hasClass("clicked")){
				return;
			}
			$("#knowledgeList .clicked").removeClass("clicked");
			$(this).addClass("clicked");
			var titleIds = $("#knowledgeList .clicked").attr("data");
			curTitleId = titleIds;
			if(titleIds!="allKnow" && titleIds!="noKnow"){
				$("#knowledgeList .clicked").next("ul").find(".title").each(function(i, item){
					titleIds += "," + $(item).attr("data");
				});
			}
			if(curTitleId == titleIds){
				if(curTitleId=="allKnow"){
					curTitleId = 0;
				}else if(curTitleId=="noKnow"){
					curTitleId = null;
				}
			}else{
				curTitleId = null;
			}
			if(curTitleId==null){
				$("#apply-copy-btn").hide();
			}else{
				$("#apply-copy-btn").show();
			}
			questionParam.titleIds = titleIds;
			getQuestionList();
		});
		
		_html = showKnowListDia(0);
		$("#common .knowDia>ul").remove();
		$("#common .knowDia").append(_html);
	},"json");
}

function showKnowList(parentId){
	var _html = '';
	$(knowMap[parentId]).each(function(i, item){
		_html += '<li class="'+(knowMap[item.id]==null?"":"has-children")+'">';
		_html += '<div data="'+item.id+'" class="title title'+item.level+'">';
		_html += '<label>'+item.name+'（'+item.questionNum+'）</label>';
		_html += '<i class="'+(knowMap[item.id]==null?"":"show")+'"></i>';
		_html += '</div>';
		_html += showKnowList(item.id);
		_html += '</li>';
	});
	if(_html!=''){
		_html = '<ul>'+_html+'</ul>';
	}
	return _html;
}

/**
 * 显示添加试题弹窗里面的知识点列表
 * @param parentId
 * @returns {String}
 */
function showKnowListDia(parentId){
	var _html = '';
	$(knowMap[parentId]).each(function(i, item){
		_html += '<li>';
		_html += '<label><input type="checkbox" name="titleId" value="'+item.id+'"/>'+item.name+'</label></div>';
		_html += showKnowListDia(item.id);
		_html += '</li>';
	});
	if(_html!=''){
		_html = '<ul>'+_html+'</ul>';
	}
	return _html;
}


/**
 * 获取节列表
 */
var questionParam = {
		unitId: unitId,
		pageNum: 1,
		pageSize: 10,
		titleIds: "allKnow"
};
var questionNum = 0;
var questionTypeArr = ["","单选题","多选题","填空题", "文本", "选填题", "综合题", "点填题", "点选题"];
var difficultyArr = ["","易","中","难"];
var sourceArr = ["","自编","模拟", "小升初", "初升高", "高考", "应用"];
var questionList;
function getQuestionList(nowPage){
	questionParam.pageNum = nowPage==null?1:nowPage;
	if($("#pageSize").length>0){
		questionParam.pageSize = $("#pageSize").val();
	}
	$.post("/newGrammarManager/getQuestionList",questionParam,function(data){
		questionList = data.questionList;
		if(questionParam.pageNum==1){
			questionNum = data.questionNum;
			$(".totalCount>span").text(questionNum);
		}
		var _html = "";
		if(questionList==null || questionList.length==0){
			_html += '<tr><td colspan="'+$("#dataTable thead th").length+'"class="red">没有添加试题哦！</td></tr>';
		}
		$(questionList).each(function(i,item){
			_html += '<tr>';
			_html += '<td><input type="checkbox" name="questionId" value="'+item.id+'"/></td>';
			_html += '<td>'+((questionParam.pageNum-1)*questionParam.pageSize+i+1)+'</td>';
			_html += '<td>'+questionTypeArr[item.questionType]+'</td>';
			_html += '<td class="text-left'+(item.parse==null?'"':(' parse-tips" title="'+item.parse+'"'))+'>';
			switch(item.questionType){
			case 1: //单选
			case 2: //多选
				_html += item.question + '<br>';
				if(item.optionA!=null&&$.trim(item.optionA)!=''){
					_html += '<span>A.'+item.optionA+'</span>&emsp;';
				}
				if(item.optionB!=null&&$.trim(item.optionB)!=''){
					_html += '<span>B.'+item.optionB+'</span>&emsp;';
				}
				if(item.optionC!=null&&$.trim(item.optionC)!=''){
					_html += '<span>C.'+item.optionC+'</span>&emsp;';
				}
				if(item.optionD!=null&&$.trim(item.optionD)!=''){
					_html += '<span>D.'+item.optionD+'</span>&emsp;';
				}
				if(item.optionE!=null&&$.trim(item.optionE)!=''){
					_html += '<span>E.'+item.optionE+'</span>&emsp;';
				}
				_html+='</td>';
				_html+='<td>'+item.correctOption+'</td>';
				break;
			case 3: //填空
			case 7: //点填题
			case 8: //点选题
				questionArr = $.trim(item.question).split(/_{3,}/g);
				$(questionArr).each(function(j,part){
					_html += part;
					if(j<questionArr.length-1){
						_html += '<u>&nbsp;'+(j+1)+'&nbsp;</u>';
					}
				});
				_html+='</td><td>';
				if(item.optionA!=null&&$.trim(item.optionA)!=''){
					_html+='1.'+item.optionA;
				}
				if(item.optionB!=null&&$.trim(item.optionB)!=''){
					_html+='<br/>2.'+item.optionB;
				}
				if(item.optionC!=null&&$.trim(item.optionC)!=''){
					_html+='<br/>3.'+item.optionC;
				}
				if(item.optionD!=null&&$.trim(item.optionD)!=''){
					_html+='<br/>4.'+item.optionD;
				}
				if(item.optionE!=null&&$.trim(item.optionE)!=''){
					_html+='<br/>5.'+item.optionE;
				}
				_html+='</td>';
				break;
			case 5:
			case 6:
				questionArr = $.trim(item.question).split(/_{3,}/g);
				$(questionArr).each(function(j,part){
					_html += part;
					if(j<questionArr.length-1){
						_html += '<u>&nbsp;'+(j+1)+'&nbsp;</u>';
					}
				});
				_html+='</td><td>';
				var correctIndexArr = item.correctOption.split(",");
				var option='', answer;
				$(correctIndexArr).each(function(j, index){
					switch(j){
					case 0: option = item.optionA; break;
					case 1: option = item.optionB; break;
					case 2: option = item.optionC; break;
					case 3: option = item.optionD; break;
					case 4: option = item.optionE; break;
					}
					if(index<0){
						answer = option;
					}else{
						answer = option.split("|")[index];
					}
					if(j>0){
						_html += '<br/>';
					}
					_html += (j+1)+'.'+answer;
				});
				_html+='</td>';
				break;
			}
			_html+='<td>'+difficultyArr[item.difficulty]+'</td>';
			if(item.source==null){
				item.source = 1;
			}
			_html+='<td>'+sourceArr[item.source]+'</td>';
			_html+='<td><button type="button" class="btn btn-primary btn-xs" onclick="toUpdateQuestion('+i+')">修改</button>&emsp;';
				_html+='<button type="button" class="btn btn-primary btn-xs" onclick="deleteQuestion('+item.id+')">删除</button></td>';
			_html += '</tr>';
		});
		$("#dataTable tbody").html(_html);
		$(".parse-tips").tipso({
			useTitle: true,
			position: 'bottom',
			width: '300px'
		});
		loadPageInfo();
	},"json");
}


/**
 * 加载分页信息
 */
function loadPageInfo(){
	var pageLen = 1;
	if(questionNum!=undefined&&questionNum>1){
		if(questionParam.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(questionNum/questionParam.pageSize);					
		}
	}
	
	$('.pageTest').page({
	      leng: pageLen,//分页总数
	      activeClass: 'activP' , //active 类样式定义
	      page_size:questionParam.pageSize,
	      nowPage:questionParam.pageNum,
	      selectPage:getQuestionList
	    });
	
	$(".selectAll").prop("checked",false);	
}

/**
 * 全选按钮点击事件
 * @param obj
 */
function selectAll(obj){
	if($(obj).prop("checked")){
		$("input[name='questionId']").prop("checked",true);
	}else{
		$("input[name='questionId']").prop("checked",false);		
	}
}

/**
 * 加配置信息
 */
function loadConfigInfo(){
	//加载年份
	$.post("/questionConfig/getSystemTime", null, function(data){
		var year = new Date(data).getFullYear();
		var _html = '';
		while(year>=2015){
			_html += '<option value="'+year+'">'+year+'</option>';
			year--;
		}
		$("#common .year").html(_html);
	}, "json");
	
	//加载地区
	$.post("/questionConfig/getAreaList", {"parentId": 0}, function(data){
		var _html = '';
		$(data).each(function(i, item){
			_html += '<option value="'+item.id+'">'+item.name+'</option>';
		});
		$("#common .area").append(_html);
	}, "json");
}


/**
 * 打开修改试题界面
 */
var questionTypeIdArr = new Array("single-choice","multiple-choice","completion","text", "selection", "synthesis", "click-completion", "click-select");
function toUpdateQuestion(index){
	var item = questionList[index];
	var questionTypeId = questionTypeIdArr[item.questionType-1];
	var _html = "<div id='dialog-div' class='question-dia'>"+$("#"+questionTypeId).html()+$("#common").html()+"</div>";
	var d = dialog({
        title: '修改试题',
        content: _html,
        width: "600px",
        zIndex: 999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	return updateOrAddQuestion(item);
        },
        cancelValue: '取消',
        cancel: function () {}
        });
    d.showModal();
    bindCommonEvent();
    //回显试题
    showOldQuestion(item);
}

/**
 * 回显试题
 * @param question
 * @param contianer
 */
function showOldQuestion(oldQuesiton){
	var contianer = $("#dialog-div");
	contianer.find("textarea[name='question']").val(oldQuesiton.question.replace(/<br>/g,"\r\n"));
    contianer.find(".parse").val(oldQuesiton.parse==null?"":oldQuesiton.parse.replace(/<br>/g,"\r\n"));
    contianer.find(".difficulty").val(oldQuesiton.difficulty);
    if(oldQuesiton.source!=null){
    	contianer.find(".source").val(oldQuesiton.source);
    }
    if(oldQuesiton.year!=null){
    	contianer.find(".year").val(oldQuesiton.year);
    }
    if(oldQuesiton.areaId!=null){
    	contianer.find(".area").val(oldQuesiton.areaId);
    }
    var _html = '';
    $(oldQuesiton.titleIdList).each(function(i, titleId){
    	contianer.find(".knowDia input[value='"+titleId+"']").prop("checked", true);
    	$(knowList).each(function(j, know){
    		if(titleId==know.id){
    			_html += '<span class="knowledge-span" data="'+titleId+'">'+know.name+'</span>';
    		}
    	});
    });
    contianer.find(".knowledge-list").html(_html);
    
    switch(oldQuesiton.questionType){
    case 1:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	contianer.find(".correct-option[value='"+oldQuesiton.correctOption+"']").prop("checked",true);
    	break;
    case 2:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	var optionArr = $.trim(oldQuesiton.correctOption).split(",");
		$(optionArr).each(function(){
			contianer.find(".correct-option[value='"+this+"']").prop("checked",true);
		});
    	break;
    case 3:
    case 7:
    case 8:
    	contianer.find("input[name='optionA']").val(oldQuesiton.optionA==null?"":oldQuesiton.optionA);
    	contianer.find("input[name='optionB']").val(oldQuesiton.optionB==null?"":oldQuesiton.optionB);
    	contianer.find("input[name='optionC']").val(oldQuesiton.optionC==null?"":oldQuesiton.optionC);
    	contianer.find("input[name='optionD']").val(oldQuesiton.optionD==null?"":oldQuesiton.optionD);
    	contianer.find("input[name='optionE']").val(oldQuesiton.optionE==null?"":oldQuesiton.optionE);
    	break;
    case 5:
    case 6:
    	var correctArr = oldQuesiton.correctOption.split(",");
    	var answer = null;
    	$(correctArr).each(function(i, cIndex){
    		cIndex = parseInt(cIndex);
    		switch(i){
    		case 0: answer = oldQuesiton.optionA; break;
    		case 1: answer = oldQuesiton.optionB; break;
    		case 2: answer = oldQuesiton.optionC; break;
    		case 3: answer = oldQuesiton.optionD; break;
    		case 4: answer = oldQuesiton.optionE; break;
    		}
    		if(cIndex<0){
    			contianer.find(".option-ul>li").eq(i).find("input[name='optionA']").val(answer);
    		}else{
    			var answerArr = answer.split("|");
    			$(answerArr).each(function(j, answerItem){
    				contianer.find(".option-ul>li").eq(i).find(":text").eq(j).val(answerItem);
    			});
    			contianer.find(".option-ul>li").eq(i).find(".select-item").val(cIndex);
    		}
    	});
    	break;
    }
}

function getQuestionParam(item){
	//获取并验证内容
	item.question = $("#dialog-div textarea[name='question']").val();
	item.question = $.trim(item.question);
	if(item.question==null || $.trim(item.question)==""){
		alert("请输入题干内容");
		return false;
	}
	item.question = item.question.replace(/(\n)|(\r\n)/g,"<br>");
	item.question = item.question.replace(/(<br>){2,}/g,"<br>");
	switch(item.questionType){
    case 1: //单选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	item.correctOption = $("#dialog-div .correct-option:checked").val();
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		if((item.correctOption=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
				(item.correctOption=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
				(item.correctOption=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
				(item.correctOption=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
				(item.correctOption=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
			alert("没有内容的答案不能为正确答案");
			return false;
		}
    	break;
    case 2: //多选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	item.correctOption = "";
		$("#dialog-div .correct-option:checked").each(function(){
			item.correctOption += $(this).val()+",";
			if(($(this).val()=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
					($(this).val()=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
					($(this).val()=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
					($(this).val()=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
					($(this).val()=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
				alert("没有内容的答案不能为正确答案");
				return false;
			}
		});
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		item.correctOption = item.correctOption.substring(0,item.correctOption.length-1);
    	break;
    case 3: //填空
    case 7: //点填
    case 8: //点选
    	item.optionA = $("#dialog-div input[name='optionA']").val();
    	item.optionB = $("#dialog-div input[name='optionB']").val();
    	item.optionC = $("#dialog-div input[name='optionC']").val();
    	item.optionD = $("#dialog-div input[name='optionD']").val();
    	item.optionE = $("#dialog-div input[name='optionE']").val();
    	if((item.optionA==null || $.trim(item.optionA)=="")&&
    			(item.optionB==null || $.trim(item.optionB)=="")&&
    			(item.optionC==null || $.trim(item.optionC)=="")&&
    			(item.optionD==null || $.trim(item.optionD)=="")&&
    			(item.optionE==null || $.trim(item.optionE)=="")){
    		alert("请至少输入一个选项或答案");
    		return false;
    	}
    	break;
    case 5: //选填
    case 6: //综合
    	var questionArr = item.question.split(/_{3,}/);
		if(questionArr.length<=1){
			alert("请给该题设置题空！");
			return false;
		}
		var emptyIsAnswer = false;
		var optionUl = $("#dialog-div .option-ul");
		var curOption, answer, optionNum;
		var optionArr = new Array();
		var correctArr = new Array();
		for(var i=0; i<questionArr.length; i++){
			if(i>=questionArr.length-1){
				break;
			}
			if(i>=5){
				continue;
			}
			emptyIsAnswer = false;
			curOption = $(optionUl).children("li:eq("+i+")");
			answer = parseInt($(curOption).find(".select-item").val());
			optionArr.length = 0;
			optionNum = 0;
			$(curOption).find(".input-item").each(function(j, item){
				var option = $.trim($(item).val());
				if(option != null && option!=""){
					optionNum++;
				}else if(answer==j){
					emptyIsAnswer = true;
					return false;
				}
				optionArr.push(option);
			});
			if(optionNum<1){
				alert("在第"+(i+1)+"空中，请输入至少一个备选项！");
				return false;
			}
			if(optionNum>1 && emptyIsAnswer){
				alert("在第"+(i+1)+"空中，空选项不能为正确答案！");
				return false;
			}
			var optionStr = '';
			if(optionNum>1){//选填
				optionStr = optionArr.join("|");
				correctArr.push(answer);
			}else{//填空
				if(item.questionType==5){
					alert("选填题至少要有两个备选项！");
					return false;
				}
				optionStr = optionArr[0];
				correctArr.push("-1");
			}
			switch(i){
			case 0: item.optionA = optionStr; break;
			case 1: item.optionB = optionStr; break;
			case 2: item.optionC = optionStr; break;
			case 3: item.optionD = optionStr; break;
			case 4: item.optionE = optionStr; break;
			}
		}
		item.correctOption = correctArr.join(",");
    	break;
    }
	item.parse = $("#dialog-div .parse").val();
	item.parse = $.trim(item.parse);
	item.parse = item.parse.replace(/(\n)|(\r\n)/g,"<br>");
	item.parse = item.parse.replace(/(<br>){2,}/g,"<br>");
	item.difficulty = $("#dialog-div .difficulty").val();
	item.source = $("#dialog-div .source").val();
	item.year = $("#dialog-div .year").val();
	item.areaId = $("#dialog-div .area").val();
	if($("#dialog-div .knowledge-list>.knowledge-span").length>0){
		item.titleIdList = new Array();
		$("#dialog-div .knowledge-list>.knowledge-span").each(function(i, span){
			item.titleIdList.push($(span).attr("data"));
		});
	}
	return true;
}


/**
 * 修改试题
 */
function updateOrAddQuestion(item){
	var type = 1;
	if(item==null){
		item = new Object();
		item.unitId=unitId;
		item.questionType = parseInt($("#questionType").val());
		type = 1;
	}else if(item.id==null){
		type = 3;
	}else{
		type = 2;
	}
	//获取试题参数
	if(!getQuestionParam(item)){
		return false;
	}
	
	//发送请求
	var flag = false;
	var url="", tips="";
	switch(type){
	case 1:
		url = "/newGrammarManager/addQuestionList";
		tips = "添加试题";
		break;
	case 2:
		url = "/newGrammarManager/updateQuestion";
		tips = "修改试题";
		break;
	case 3:
		url = "/newGrammarManager/addQuestionList";
		tips = "复制试题";
		break;
	}
	$.ajax({
		url: url,
		type: "post",
		data: JSON.stringify(item),
        contentType:"application/json",
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			flag = data;
			if(data){
				alert(tips+"成功！");
				getKnowledgeList();
				getQuestionList();
			}else{
				alert(tips+"失败！");
			}
		}
	});
	return flag;
}


/**
 * 删除试题
 * @param questionId
 */
function deleteQuestion(questionId){
	var questionIdArr = new Array();
	if(questionId){
		questionIdArr.push(questionId);
	}else{
		$("input[name='questionId']:checked").each(function(){
			questionIdArr.push($(this).val());
		});
		if(questionIdArr.length==0){
			alert("请选中要删除的试题！");
			return;
		}
	}
	var d = dialog({
        title: '删除试题',
        content: "您确定要删除选中的试题吗？",
        okValue: '确 定',
        ok: function () {
            this.title('删除中..');
            $.post("/newGrammarManager/deleteQuestion",{"courseId": courseId, "unitId": questionParam.unitId, "questionIdArr":questionIdArr},function(data){
            	if(data){
            		getKnowledgeList();
    				getQuestionList();
            	}else{
            		alert("删除失败！");
            	}
            },"json");
            return;
        },
        cancelValue: '取消',
        cancel: function () {}
        });
    d.showModal();
}

/**
 * 单题添加
 */
function addQuestion(){
	var _html = '<div id="dialog-div">'+
				'<div>'+
				'<label>题型：</label>'+
			   	'<select id="questionType" onchange="selectQuestionType();">'+
					'<option value="1">单选题</option>'+
					'<option value="2">多选题</option>'+
					'<option value="3">填空题</option>'+
					'<option value="5">选填题</option>'+
					'<option value="6">综合题</option>'+
					'<option value="7">点填题</option>'+
					'<option value="8">点选题</option>'+
				'</select>'+
				'</div>'+
				'<div id="addQuestionDiv" class="question-dia"></div>'+
				'</div>';
	var d = dialog({
        title: '单题添加',
        content: _html,
        width: "600px",
        zIndex: 999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	return updateOrAddQuestion();
        },
        cancelValue: '取消',
        cancel: function () {}
        });
	selectQuestionType();
    d.showModal();
}

function selectQuestionType(){
	var questionType = parseInt($("#questionType").val());
	var _html = $("#"+questionTypeIdArr[questionType-1]).html()+$("#common").html();
	$("#addQuestionDiv").html(_html);
	bindCommonEvent();
}

function bindCommonEvent(){
	var obj = $("#dialog-div");
	$(obj).find(".know-btn").click(function(){
		if($(this).next(".knowDia").is(":visible")){
			$(this).next(".knowDia").hide();
		}else{
			$(this).next(".knowDia").show();
		}
	});
	$(obj).find(".close").click(function(){
		$(this).parent(".knowDia").hide();
	});
	$(obj).find("input[name='titleId']").change(function(){
		if($(this).prop("checked")){
			$(this).parent().parent().parents("li").children("label").children("input").prop("checked", false);
			$(this).parent().siblings("ul").find("input").prop("checked", false);
		}
		var _html = '';
		$(obj).find(".knowDia input:checked").each(function(i, item){
			_html += '<span class="knowledge-span" data="'+$(item).val()+'">'+$(item).parent().text()+'</span>';
		});
		$(obj).find(".knowledge-list").html(_html);
	});
}

/**---------------------------导入试题---------------------------*/
var uploadDia;
function toAddQuestions(){
	var _html = "<div id='upload-dialog'>"+$("#uploadQuestionsDiv").html()+"</div>";
	uploadDia = dialog({
	        title: '批量添加试题',
	        content: _html,
	        width: "445px",
	        zIndex: 999,
	        padding: "10px 20px 20px",
	        ok: false
        });
	$("#upload-dialog input[name='unitId']").val(unitId);
	uploadDia.showModal();
}

/**
 * 下载excel模板
 */
function downloadExcelTemplate(){
	window.location.href = "/newGrammarManager/downloadQuestionTemplate?unitId="+unitId;
}

/**
 * 上传excel文件
 */
var uploading = false;
function uploadExcel(){
	if(uploading){
		return;
	}
	if($("#upload-dialog input[name='unitId']").val()==0){
		alert("请先选定节！");
		return;
	}
	if($("#upload-dialog input[name='file']").val()==""){
		alert("请先选择需要上传的文件！");
		return;
	}
	uploading = true;
	$("#upload-dialog .form").ajaxSubmit({
		type:"POST",
		url:"/newGrammarManager/uploadExcel",
		cache:false,
		dataType:"html",
		success:function(data){
			if(data=="error"){
				alert("上传出错，请确定上传的内容格式是否正确！");
			}else if(data=="size is too big"){
				alert("上传的文件过大！");
			}else if(data=="format is wrong"){
				alert("文件格式有误，正确的文件格式为“.xlsx、.xls”!");
			}else if(data=="failed"){
				alert("添加试题失败！");
			}else if("success"){
				alert("已成功添加试题！");
				uploadDia.remove();
				$("#upload-dialog input[name='file']").val("");
				getKnowledgeList();
				getQuestionList();
			}
			uploading = false;
		}
	});
}

/**
 * 返回
 */
function goBack(){
	window.top.close();
}

/***************从应用中复制试题*****************/
var applyQuestionList = null;
var curTitleId = null;
function getApplyQuestionList(){
	$.post("/newGrammarManager/getApplyQuestionList", {"titleId": curTitleId, "unitId": unitId}, function(data){
		applyQuestionList = data;
		var _html = '';
		if(data==null || data.length==0){
			_html += '<tr><td colspan="'+$("#apply-question-table thead th").length+'" style="color: red;">* 没有数据 *</td></tr>';
		}else{
			$(data).each(function(i, item){
				_html += '<tr id="content_'+item.id+'">';
				_html += '<td>'+(i+1)+'</td>';
				_html += '<td>'+questionTypeArr[item.questionType]+'</td>';
				_html += '<td class="text-left'+(item.parse==null?'"':(' parse-tips" title="'+item.parse+'"'))+'>';
				switch(item.questionType){
				case 1: //单选
				case 2: //多选
					_html += item.question + '<br>';
					if(item.optionA!=null&&$.trim(item.optionA)!=''){
						_html += '<span>A.'+item.optionA+'</span>&emsp;';
					}
					if(item.optionB!=null&&$.trim(item.optionB)!=''){
						_html += '<span>B.'+item.optionB+'</span>&emsp;';
					}
					if(item.optionC!=null&&$.trim(item.optionC)!=''){
						_html += '<span>C.'+item.optionC+'</span>&emsp;';
					}
					if(item.optionD!=null&&$.trim(item.optionD)!=''){
						_html += '<span>D.'+item.optionD+'</span>&emsp;';
					}
					if(item.optionE!=null&&$.trim(item.optionE)!=''){
						_html += '<span>E.'+item.optionE+'</span>&emsp;';
					}
					_html+='</td>';
					_html+='<td>'+item.correctOption+'</td>';
					break;
				case 3: //填空
				case 7: //点填题
				case 8: //点选题
					questionArr = $.trim(item.question).split(/_{3,}/g);
					$(questionArr).each(function(j,part){
						_html += part;
						if(j<questionArr.length-1){
							_html += '<u>&nbsp;'+(j+1)+'&nbsp;</u>';
						}
					});
					_html+='</td><td>';
					if(item.optionA!=null&&$.trim(item.optionA)!=''){
						_html+='1.'+item.optionA;
					}
					if(item.optionB!=null&&$.trim(item.optionB)!=''){
						_html+='<br/>2.'+item.optionB;
					}
					if(item.optionC!=null&&$.trim(item.optionC)!=''){
						_html+='<br/>3.'+item.optionC;
					}
					if(item.optionD!=null&&$.trim(item.optionD)!=''){
						_html+='<br/>4.'+item.optionD;
					}
					if(item.optionE!=null&&$.trim(item.optionE)!=''){
						_html+='<br/>5.'+item.optionE;
					}
					_html+='</td>';
					break;
				case 5:
				case 6:
					questionArr = $.trim(item.question).split(/_{3,}/g);
					$(questionArr).each(function(j,part){
						_html += part;
						if(j<questionArr.length-1){
							_html += '<u>&nbsp;'+(j+1)+'&nbsp;</u>';
						}
					});
					_html+='</td><td>';
					var correctIndexArr = item.correctOption.split(",");
					var option='', answer;
					$(correctIndexArr).each(function(j, index){
						switch(j){
						case 0: option = item.optionA; break;
						case 1: option = item.optionB; break;
						case 2: option = item.optionC; break;
						case 3: option = item.optionD; break;
						case 4: option = item.optionE; break;
						}
						if(index<0){
							answer = option;
						}else{
							answer = option.split("|")[index];
						}
						if(j>0){
							_html += '<br/>';
						}
						_html += (j+1)+'.'+answer;
					});
					_html+='</td>';
					break;
				}
				if(item.copied==1){
					_html += '<td class="green">已复制</td>';
					_html += '<td>--</td>';
				}else{
					_html += '<td class="red">未复制</td>';
					_html+='<td><button type="button" class="btn btn-primary btn-xs" onclick="toCopyApplyQuestion('+i+');">复制</button></td>';
				}
				_html += '</tr>';
			});
		}
		$("#apply-question-table tbody").html(_html);
		var knowName = "";
		if(curTitleId==0){
			knowName = " - 全部知识点";
		}else{
			$(knowList).each(function(i, item){
				if(item.id == curTitleId){
					knowName = " - "+item.name;
					return false;
				}
			});
		}
		var d = dialog({
			title: "复制试题"+knowName,
			content: $("#apply-question-div"),
			ok: false,
			zIndex: 999,
			padding: "10px",
			width: "1000px"
		});
		d.showModal();
	}, "json");
}

/**
 * 复制应用试题
 * @param index
 */
function toCopyApplyQuestion(index){
	var item = applyQuestionList[index];
	var questionTypeId = questionTypeIdArr[item.questionType-1];
	var _html = "<div id='dialog-div' class='question-dia'>"+$("#"+questionTypeId).html()+$("#common").html()+"</div>";
	var d = dialog({
        title: '复制试题',
        content: _html,
        width: "600px",
        zIndex: 99999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	var newQuestion = {};
        	newQuestion.contentId = item.id;
        	newQuestion.unitId=item.unitId;
        	newQuestion.questionType=item.questionType;
        	var flag = updateOrAddQuestion(newQuestion);
        	if(flag){
        		$("#content_"+item.id).children().eq(4).removeClass("red").addClass("green").text("已复制");
        		$("#content_"+item.id).children().eq(5).html("--");
        	}
        	return flag;
        },
        cancelValue: '取消',
        cancel: function () {
        	return true;
        }
    });
    d.showModal();
    bindCommonEvent();
    
    //回显试题
    item.question = transferHtmlToText(item.question);
    item.source = 4;
    item.difficulty = 1;
    item.titleIdList = [item.titleId];
    showOldQuestion(item);
}


/**
 * html转str
 * @param _html
 */
function transferHtmlToText(_html){
	var text = '';
	_html = '<div>'+_html+'</div>';
	if($(_html).find("br").length>0){
		_html = _html.replace(/<br\/*>/g, "\r\n");
		text = $(_html).text();
	}else{
		$(_html).children().each(function(i, item){
			if(i>0){
				text += '\r\n';
			}
			text += $(item).text();
		});
	}
	return text;
}

/**
 * 跳转到添加试题页面
 */
function toCopyUnitQuestion(){
	window.location.href="grammar-new-unit-add.html?courseId="+courseId+"&unitId="+unitId;
}