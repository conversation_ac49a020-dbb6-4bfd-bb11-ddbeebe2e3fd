var learnProgress=-1;//-1：未学；0：学完；>0：正在学的进度
var chinese =  /[\u4E00-\u9FA5，；。“”？！：、（）]/g;
var unitId = Request["unitId"];
var grammarCard;
//var fullScreen = false;
$(function(){
	$(".time-show").text(new Date().Format("hh:mm"));
	setInterval(function(){
		$(".time-show").text(new Date().Format("hh:mm"));
	}, 60000);
	
	//全屏
	$("#fullScreen").click(function(evt){
		//evt.preventDefault();
		var element = document.documentElement || document.body;
		//某个元素有请求    
		var requestMethod =element.requestFullScreen
		||element.webkitRequestFullScreen //谷歌
		||element.mozRequestFullScreen  //火狐
		||element.msRequestFullscreen; //IE11
		if (requestMethod && requestMethod != "undefined") {  
			requestMethod.call(element);   //执行这个请求的方法
		}else if (typeof window.ActiveXObject !== "undefined") {  //window.ActiveXObject判断是否支持ActiveX控件
			var wscript = new ActiveXObject("WScript.Shell"); //创建ActiveX  
			if (wscript !== null) {    //创建成功
				wscript.SendKeys("{F11}");//触发f11   
			}  
		}

		$("#fullScreen").hide();
		$("#exitScreen").show();
		$(".top-box-fixed").hide();
		$(".card-box").css({
    		height: '8.8rem',
			marginTop: '0.5rem'
    	});
		
		cardRemWidth.cardBoxWidth = 14.5;
		cardRemWidth.ParseMaxWidth = 11;
		$(".card-box").css({
			width: cardRemWidth.cardBoxWidth+'rem'
		});
		$(".tips-parse-text").css({
			maxWidth: cardRemWidth.ParseMaxWidth+'rem'
		});
	});
	
	//退出全屏
	$("#exitScreen").click(function(ev){
		var el = document;
		 
        var cfs = el.exitFullscreen || el.webkitExitFullscreen ||
            el.mozCancelFullScreen || el.msExitFullscreen;
        
        if (cfs && cfs!='undefined') { //typeof cfs != "undefined" && cfs
            cfs.call(el);
        } else if (typeof window.ActiveXObject != "undefined") {
            //for IE，这里和fullScreen相同，模拟按下F11键退出全屏
        	
            var wscript = new ActiveXObject("WScript.Shell");
            if (wscript != null) {
                wscript.SendKeys("{F11}");
            }
        }
        
        $("#fullScreen").show();
        $("#exitScreen").hide();
        $(".top-box-fixed").show();
        $(".card-box").css({
    		height: '7.28rem',
			marginTop: '0.9rem'
    	});
        cardRemWidth.cardBoxWidth = 11;
		cardRemWidth.ParseMaxWidth = 8.19;
		$(".card-box").css({
			width: cardRemWidth.cardBoxWidth+'rem'
		});
		$(".tips-parse-text").css({
			maxWidth: cardRemWidth.ParseMaxWidth+'rem'
		});
	});
	
	$(window).keydown(function(e){
		e = e || window.event;
		switch(e.keyCode){
		case 122://F11
			/*e.preventDefault();
			if($("#exitScreen").css("display") === 'block'){
				$("#exitScreen").click();
			}else{
				$("#fullScreen").click();
			}*/
			
			if(isFullscreenForNoScroll()){
				$("#fullScreen").show();
				$("#exitScreen").hide();
			}else{
				$("#fullScreen").hide();
				$("#exitScreen").show();
			}
			break;
		case 33://Page Up
		case 37://Left
		case 38://Up
			e.preventDefault();
			prevStep();
			break;
		case 34://Page Down
		case 39://Right
		case 40://Down
			e.preventDefault();
			nextStep();
			break;
		case 36://Home
			e.preventDefault();
			prevAllStep();
			break;
		case 35://End
			e.preventDefault();
			nextAllStep();
			break;
		}
	});
	
	if(unitId!=null){
		$.post("/newGrammarManager/getGrammarCardDetail",
				{"unitId":unitId},
				function(data){
					grammarCard = data;
					initGrammarContent();
					showGrammarCard();
				},"json");
	}
	
	//视觉窗口大小调整
	$(".smdt-selectSize button").click(function(){
		//$(this).addClass("active").siblings().removeClass("active");
		selectViewSize($(this).attr("class"),cardRemWidth);
	});
	
});

/**
 * 初始化出来语法内容
 */
var grammarContentArr = [];
function initGrammarContent(){
	if(grammarCard.targetContentList!=null && grammarCard.targetContentList.length>0){
		grammarContentArr.push({"id": 0, "question": "学习目标", "questionType": 101});
		grammarContentArr = grammarContentArr.concat(grammarCard.targetContentList);
	}
	
	$(grammarCard.titleList).each(function(i, title1){
		grammarContentArr.push({"id": title1.id, "question": title1.name, "questionType": 101});
		if(title1.subtitleList){
			addSubtitleList(title1.subtitleList);
		}
		$(title1.titleList).each(function(j, title2){
			grammarContentArr.push({"id": title2.id, "question": title2.name, "questionType": 102});
			if(title2.subtitleList){
				addSubtitleList(title2.subtitleList);
			}
			$(title2.titleList).each(function(m, title3){
				grammarContentArr.push({"id": title3.id, "question": title3.name, "questionType": 103});
				if(title3.subtitleList){
					addSubtitleList(title3.subtitleList);
				}
			});
		});
	});
	
	if(grammarCard.summaryContentList!=null && grammarCard.summaryContentList.length>0){
		grammarContentArr.push({"id": 0, "question": "知识点小结", "questionType": 101});
		grammarContentArr = grammarContentArr.concat(grammarCard.summaryContentList);
	}
}

/**
 * 添加子标题内容到grammarContentArr中
 */
function addSubtitleList(_subtitleList){
	if(_subtitleList==null){
		return;
	}
	$(_subtitleList).each(function(i, item){
		grammarContentArr.push({"id": item.id, "question": item.name, "questionType": 104});
		if(item.contentList!=null){
			grammarContentArr = grammarContentArr.concat(item.contentList);
		}
	});
}


var cardRemWidth = {
	cardBoxWidth: 11, //卡片外容器默认宽度
	ParseMaxWidth: 8.19 //解析默认最大宽度
};

//视觉窗口大小调整
function selectViewSize(className,cardRemWidth){
	switch(className){
		case 'reduce':
			//减小
			cardRemWidth.cardBoxWidth -= 0.2;
			cardRemWidth.ParseMaxWidth -=0.2;
			
			$(".card-box").css({
				width: cardRemWidth.cardBoxWidth+'rem'
			});
			$(".tips-parse-text").css({
				maxWidth: cardRemWidth.ParseMaxWidth+'rem'
			});
			
			break;
		case 'zoomIn':
			//放大
			cardRemWidth.cardBoxWidth += 0.2;
			cardRemWidth.ParseMaxWidth +=0.2;
			
			if(cardRemWidth.cardBoxWidth > 14.5) {
				cardRemWidth.cardBoxWidth = 14.5;
				alert('老师，视觉窗口已经最大了哦~');
				cardRemWidth.ParseMaxWidth = 11;
			}
			
			$(".card-box").css({
				width: cardRemWidth.cardBoxWidth+'rem'
			});
			$(".tips-parse-text").css({
				maxWidth: cardRemWidth.ParseMaxWidth+'rem'
			});
			break;
	}
}

function isFullscreenForNoScroll(){
    var explorer = window.navigator.userAgent.toLowerCase();
    if(explorer.indexOf('chrome')>0){//webkit
        if (document.body.scrollHeight === window.screen.height && document.body.scrollWidth === window.screen.width) {
        	$("#fullScreen").hide().next().show();
        	//$("#exitScreen").hide().prev().show();
        } else {
        	$("#fullScreen").hide().next().show();
            //alert("不全屏");
        }
    }else{//IE 9+  fireFox
        if (window.outerHeight === window.screen.height && window.outerWidth === window.screen.width) {
        	return true;
        } else {
        	return false;
        }
    }
}

$(function(){
	var operationTop = ($(window).height()-$(".progress-operation").height())/2;
	$(".progress-operation").css("top", operationTop);
	var knowlegeTop = (window.innerHeight-$(".knowlege-system-box").height())/2;
	$(".knowlege-system-box").css("top", knowlegeTop);
	$(".knowlege-system").mouseenter(function(){
		if(!$(this).hasClass("k-active")){
			$(this).addClass("k-active");
			$(".title-list-div").show();
		}
	});
	$("#knowlege").mouseleave(function(){
		$(".knowlege-system").removeClass("k-active");
		$(".title-list-div").hide();
	});
	
	$(document).click(function() {
		$(".s-active").children("ul").hide();
	});
	//$("#next-bar .rect").click(showNext);
	$(document).keyup(function(event) {
		event = event || window.event;
		switch(event.keyCode){
		case 13:
			if($(".inte-learn-submit:visible").length>0){
				$(".inte-learn-submit:visible").click();
			}else if($("#next-bar:visible").length>0){
				showNext();
			}
			break;
		}
	});
	
});

/**
 * 展示语法卡片
 */
var inteTotalNum = 0;//总互动次数
function showGrammarCard(){
	var titleArr = new Array();
	var title, childTitle2, childTitle3;
	$("#name").text(grammarCard.name);
	$(grammarContentArr).each(function(i, item){
		switch(item.questionType){
		case 101: //一级标题
			title = new Object();
			title.num = inteTotalNum;
			title.index = grammarContentArr.length-1;
			title.text = item.question;
			titleArr.push(title);
			break;
		case 102: //二级标题
			if(titleArr[titleArr.length-1].childTitle2==null){
				titleArr[titleArr.length-1].childTitle2 = new Array();
			}
			childTitle2 = new Object();
			childTitle2.num = inteTotalNum;
			childTitle2.index = grammarContentArr.length-1;
			childTitle2.text = item.question;
			titleArr[titleArr.length-1].childTitle2.push(childTitle2);
			break;
		case 103: //三级标题
			childTitle3 = new Object();
			childTitle3.num = inteTotalNum;
			childTitle3.index = grammarContentArr.length-1;
			childTitle3.text = item.question;
			if(titleArr[titleArr.length-1].childTitle2==null){//一级标题和三级标题之间没有二级标题的情况
				if(titleArr[titleArr.length-1].childTitle3==null){
					titleArr[titleArr.length-1].childTitle3 = new Array();
				}
				titleArr[titleArr.length-1].childTitle3.push(childTitle3);
			}else{
				var title2 = titleArr[titleArr.length-1].childTitle2;
				if(title2[title2.length-1].childTitle3==null){
					title2[title2.length-1].childTitle3 = new Array();
				}
				title2[title2.length-1].childTitle3.push(childTitle3);
			}
			break;
		case 104: //子标题
			break;
		default: //非标题内容
			inteTotalNum++;
			break;
		}
	});
	//显示页面信息
	$("#inteTotalNum").text(inteTotalNum);
	var _html = '';
	for(var i=0; i<titleArr.length; i++){
		title = titleArr[i];
		_html += '<li class="progress_'+title.num+'" data='+title.index+' data_progress='+title.num+'><span></span>';
		_html += '<label title="'+title.text+'">'+title.text+'</label>';
		if(title.childTitle2){
			_html += '<ul class="title2-list">';
			for(var j=0; j<title.childTitle2.length; j++){
				childTitle2 = title.childTitle2[j];
				_html += '<li class="progress_'+childTitle2.num+'" data='+childTitle2.index+' data_progress='+childTitle2.num+'>';
				_html += '<label title="'+childTitle2.text+'">'+childTitle2.text+'</label>';
				if(childTitle2.childTitle3){
					_html += '<ul class="title3-list">';
					for(var k=0; k<childTitle2.childTitle3.length; k++){
						childTitle3 = childTitle2.childTitle3[k];
						_html += '<li class="progress_'+childTitle3.num+'" data='+childTitle3.index+' data_progress='+childTitle3.num+'>';
						_html += '<label title="'+childTitle3.text+'">'+childTitle3.text+'</label>';
						_html += '</li>';
					}
					_html += '</ul>';
				}
				_html += '</li>';
			}
			_html += '</ul>';
		}else if(title.childTitle3){
			_html += '<ul class="title3-list">';
			for(var j=0; j<title.childTitle3.length; j++){
				childTitle3 = title.childTitle3[j];
				_html += '<li class="progress_'+childTitle3.num+'" data='+childTitle3.index+' data_progress='+childTitle3.num+'>';
				_html += '<label title="'+childTitle3.text+'">'+childTitle3.text+'</label>';
				_html += '</li>';
			}
			_html += '</ul>';
		}
		_html += '</li>';
	}
	$("#titleList").html(_html);
	var titleTop = (window.innerHeight-$(".title-list-div").outerHeight())/2;
	$(".title-list-div").css("top", titleTop);
	$("#titleList li>label").click(function(){
		/*if(!$(this).parent().hasClass("learned-title") || $(this).hasClass("l-clicked")){
			return;
		}*/
		$(".l-clicked").removeClass("l-clicked");
		$(this).addClass("l-clicked");
		var _progress = parseInt($(this).parent().attr("data_progress"));
		var _index = parseInt($(this).parent().attr("data"));
		skipStep(_index, _progress);
	});
	showGrammarContent();
}

/**
 * 展示语法卡片内容
 */
var index = 0;
var progress = 0;//记录学习进度
var isLearned = true;//表示当前内容是否已经学过
var haveQuestion = false;//当前内容是否包含需要答题的部分
var nextStatus = 0;//下一个内容的状态，0：正常内容、1：分割线、2：没有内容了
var prevType = 0;//上一个内容的类型
var firstLoad = true;
function showGrammarContent(){
	haveQuestion = false;
	var prev = $("#next-bar").prev();
	var content = grammarContentArr[index++];
	var _html = "";
	while(content!=null){
		if(content.questionType>=1 && content.questionType<=5 || content.questionType==9 || 
				content.questionType==10 || content.questionType==11){
			$(".progress_"+progress).addClass("learned-title");
			isLearned=(learnProgress==0 || progress<learnProgress);
		}
		if(index>=grammarContentArr.length){
			nextStatus = 2;
		}else if(grammarContentArr[index].questionType==0){
			nextStatus = 1;
		}else{
			nextStatus = 0;
		}
		if(index>1){
			prevType = grammarContentArr[index-2].questionType;
		}
		switch(content.questionType){
		case 0: //分割线
			_html += "<div data_progress='"+(progress+1)+"'></div>";
			break;
		case 1: //单选题
			haveQuestion = true;
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+content.question+'</div>';
			}else{
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+content.question;
				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<ul class="option inte-learn-radio">';
			if(isLearned){
				if(content.optionA!=null && content.optionA!=""){
					_html += '<li><label><input name="input_'+index+'" type="radio" value="A" '; 
					_html += content.correctOption=="A"?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>A</span>. '+content.optionA+'</label></li>';
				}
				if(content.optionB!=null && content.optionB!=""){
					_html += '<li><label><input name="input_'+index+'" type="radio" value="B" ';
					_html += content.correctOption=="B"?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>B</span>. '+content.optionB+'</label></li>';
				}
				if(content.optionC!=null && content.optionC!=""){
					_html += '<li><label><input name="input_'+index+'" type="radio" value="C" ';
					_html += content.correctOption=="C"?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>C</span>. '+content.optionC+'</label></li>';
				}
				if(content.optionD!=null && content.optionD!=""){
					_html += '<li><label><input name="input_'+index+'" type="radio" value="D" ';
					_html += content.correctOption=="D"?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>D</span>. '+content.optionD+'</label></li>';
				}
			}else{
				if(content.optionA!=null && content.optionA!=""){
					_html += '<li><label><input name="input_'+index+'" type="radio" value="A"/><span>A</span>. '+content.optionA+'</label>';
				}
				if(content.optionB!=null && content.optionB!=""){
					_html += '</li><li><label><input name="input_'+index+'" type="radio" value="B"/><span>B</span>. '+content.optionB+'</label>';
				}
				if(content.optionC!=null && content.optionC!=""){
					_html += '</li><li><label><input name="input_'+index+'" type="radio" value="C"/><span>C</span>. '+content.optionC+'</label>';
				}
				if(content.optionD!=null && content.optionD!=""){
					_html += '</li><li><label><input name="input_'+index+'" type="radio" value="D"/><span>D</span>. '+content.optionD+'</label>';
				}
			}
			_html += '</ul>';
			if(!isLearned){
				_html += '<button style="margin-left: 0.8rem;" class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button></li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 2: //多选题
			haveQuestion = true;
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			var questionHtml = content.question.replace(/<\/p>(\s)*$/,"<span style='color: #999;'>（多选）</span></p> ");
			if(isLearned){
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '</div>';
			}
			_html += '<ul class="option inte-learn-radio">';
			if(isLearned){
				if(content.optionA!=null && content.optionA!=""){
					_html += '<li><label><input type="checkbox" value="A" ';
					_html += content.correctOption.indexOf("A")>=0?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>A</span>. '+content.optionA+'</label></li>';
				}
				if(content.optionB!=null && content.optionB!=""){
					_html += '<li><label><input type="checkbox" value="B" ';
					_html += content.correctOption.indexOf("B")>=0?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>B</span>. '+content.optionB+'</label></li>';
				}
				if(content.optionC!=null && content.optionC!=""){
					_html += '<li><label><input type="checkbox" value="C" ';
					_html += content.correctOption.indexOf("C")>=0?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>C</span>. '+content.optionC+'</label></li>';
				}
				if(content.optionD!=null && content.optionD!=""){
					_html += '<li><label><input type="checkbox" value="D" ';
					_html += content.correctOption.indexOf("D")>=0?'checked="checked" ':'';
					_html += 'disabled="disabled"/><span>D</span>. '+content.optionD+'</label></li>';
				}
			}else{
				if(content.optionA!=null && content.optionA!=""){
					_html += '<li><label><input type="checkbox" value="A"/><span>A</span>. '+content.optionA+'</label>';
				}
				if(content.optionB!=null && content.optionB!=""){
					_html += '</li><li><label><input type="checkbox" value="B"/><span>B</span>. '+content.optionB+'</label>';
				}
				if(content.optionC!=null && content.optionC!=""){
					_html += '</li><li><label><input type="checkbox" value="C"/><span>C</span>. '+content.optionC+'</label>';
				}
				if(content.optionD!=null && content.optionD!=""){
					_html += '</li><li><label><input type="checkbox" value="D"/><span>D</span>. '+content.optionD+'</label>';
				}
			}
			_html += '</ul>';
			if(!isLearned){
				_html += '<button style="margin-left: 0.8rem;" class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button></li>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 3: //填空题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
			var width = 0, len = 0;
			var correctAnswer = "";
			for(i=0; i<questionArr.length; i++){
				questionHtml += questionArr[i];
				if(i<questionArr.length-1){
					switch (i){
					case 0: 
						correctAnswer = content.optionA; 
						break;
					case 1: 
						correctAnswer = content.optionB; 
						break;
					case 2: 
						correctAnswer = content.optionC; 
						break;
					case 3: 
						correctAnswer = content.optionD; 
						break;
					case 4: 
						correctAnswer = content.optionE; 
						break;
					}
					len = correctAnswer.length;
					if(chinese.test(correctAnswer)){
						len += correctAnswer.match(chinese).length;
					}
					width = getContentWidth(len);
					if(isLearned){
						questionHtml += '<input class="'+(content.questionType==3?'border':'input-line')+'" style="width:'+width/100+'rem;" value="'+correctAnswer+'" disabled="disabled"/>';
					}else{
						questionHtml += '<input class="'+(content.questionType==3?'border':'input-line')+'" style="width:'+width/100+'rem;"/>';
					}
				}
			}
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 4: //文本
			haveQuestion = true;
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box" '+(prevType==6?'style="padding-top: 0.14rem;"':'')+'>';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			_html += '<div class="question inte-learn-sm-text inte-learn-mid-text">'+content.question;
			_html += '</div>';
			_html += '</div></div>';
			break;
		case 5: //下拉选择题
			haveQuestion = true;
			var _select = "";
			var width = 0, length = 0, len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			var questionHtml = content.question.replace(/<p[^>]*>/g,"");
			questionHtml = questionHtml.replace(/<\/p>/g,"<br>");
			questionHtml = questionHtml.replace(/<br>+\s*$/, "");
			var questionArr = questionHtml.split(/_{3,}/);
			for(var i=0; i<questionArr.length-1; i++){
				if(i==0){
					optionStr = content.optionA;
				}else if(i==1){
					optionStr = content.optionB;
				}else if(i==2){
					optionStr = content.optionC;
				}else if(i==3){
					optionStr = content.optionD;
				}else if(i==4){
					optionStr = content.optionE;
				}else{
					break;
				}
				optionArr = optionStr.split("|");
				answer = optionArr[parseInt(answerIndexArr[i])];
				if(isLearned){
					if(chinese.test(answer)){
						width = getContentWidth(answer.length+answer.match(chinese).length);
					}else{
						width = getContentWidth(answer.length);
					}
					_select = "<div class='question-select s-disable'>";
					_select += "<h3 data='' style='width:"+width/100+"rem; background-position-x: "+(width)/100+"rem;'>"+answer+"</h3>";
					_select += "</div>";
				}else{
					len = 0;
					for(var j=0; j<optionArr.length; j++){
						length = optionArr[j].length;
						if(chinese.test(optionArr[j])){
							length += optionArr[j].match(chinese).length;
						}
						if(len<length){
							len = length;
						}
					}
					width = getContentWidth(len);
					_select = "<div class='s-active question-select'>";
					_select += "<h3 data='' style='width:"+width/100+"rem; background-position-x: "+((width)/100+0.12)+"rem;'>请选择</h3>";
					_select += "<ul>";
					$(optionArr).each(function(j, option){
						if(option!=null && option!=""){
							_select += "<li data='"+j+"' style='width:"+(width/100+0.2)+"rem'>"+option+"</li>";
						}
					});
					_select += "</ul></div>";
				}
				questionHtml = questionHtml.replace(/_{3,}/,_select); 
			}
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 6: //综合题
			haveQuestion = true;
			var _select = "";
			var questionHtml = "";
			var width = 0, length = 0, len = 0;
			var optionStr, optionArr, answer;
			var answerIndexArr = content.correctOption.split(",");
			var questionTemp = content.question.replace(/<p[^>]*>/g,"");
			questionTemp = questionTemp.replace(/<\/p>/g,"<br>");
			questionTemp = questionTemp.replace(/<br>+\s*$/, "");
			var questionArr = questionTemp.split(/_{3,}/);
			for(var i=0; i<questionArr.length; i++){
				questionHtml += questionArr[i];
				if(i>=questionArr.length-1){
					break;
				}
				if(i==0){
					optionStr = content.optionA;
				}else if(i==1){
					optionStr = content.optionB;
				}else if(i==2){
					optionStr = content.optionC;
				}else if(i==3){
					optionStr = content.optionD;
				}else if(i==4){
					optionStr = content.optionE;
				}else{
					break;
				}
				optionArr = optionStr.split("|");
				if(isLearned){
					if(optionArr.length==1){
						answer = optionArr[0];
					}else{
						answer = optionArr[parseInt(answerIndexArr[i])];
					}
					if(chinese.test(answer)){
						width = getContentWidth(answer.length+answer.match(chinese).length);
					}else{
						width = getContentWidth(answer.length);
					}
					if(optionArr.length==1){
						_select = '<input class="border" style="width:'+width/100+'rem;" value="'+answer+'" disabled="disabled"/>';
					}else{
						_select = "<div class='question-select s-disable'>";
						_select += "<h3 data='' style='width:"+width/100+"rem; background-position-x: "+(width-10)/100+"rem;'>"+answer+"</h3>";
						_select += "</div>";
					}
				}else{
					len = 0;
					for(var j=0; j<optionArr.length; j++){
						length = optionArr[j].length;
						if(chinese.test(optionArr[j])){
							length += optionArr[j].match(chinese).length;
						}
						if(len<length){
							len = length;
						}
					}
					width = getContentWidth(len);
					if(optionArr.length==1){
						_select = "<input class='border' style='width:"+width/100+"rem;'/>";
					}else{
						_select = "<div class='s-active question-select'>";
						_select += "<h3 data='' style='width:"+width/100+"rem; background-position-x: "+(width-10)/100+"rem;'>请选择</h3>";
						_select += "<ul>";
						$(optionArr).each(function(j, option){
							if(option!=null && option!=""){
								_select += "<li data='"+j+"' style='width:"+width/100+"rem'>"+option+"</li>";
							}
						});
						_select += "</ul></div>";
					}
				}
				questionHtml += _select;
			}
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 7: //点填题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
            var width = 0, len = 0;
            var correctAnswer = "";
			for(i=0; i<questionArr.length; i++){
				questionHtml += questionArr[i];
				if(i<questionArr.length-1){
					switch (i){
					case 0: 
						correctAnswer = content.optionA; 
						break;
					case 1: 
						correctAnswer = content.optionB; 
						break;
					case 2: 
						correctAnswer = content.optionC; 
						break;
					case 3: 
						correctAnswer = content.optionD; 
						break;
					case 4: 
						correctAnswer = content.optionE; 
						break;
					}
					len = correctAnswer.length;
					if(chinese.test(correctAnswer)){
						len += correctAnswer.match(chinese).length;
					}
					width = getContentWidth(len);
					if(isLearned){
						questionHtml += '<input class="input-line" style="width:'+width/100+'rem;" value="'+correctAnswer+'" disabled="disabled"/>';
					}else{
						questionHtml += "<input class='input-line' style='width:"+width/100+"rem;'/>";
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/,"<span style='color: #999;'>（选词填空1）</span></p>");
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box pace-last-box dian-tian-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>';
				_html += '</div>';
			}
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 8: //点选题
			haveQuestion = true;
			var questionArr = content.question.split(/_{3,}/);
			var questionHtml = "";
            var width = 0, len = 0;
            var correctAnswer = "";
			var answerArr = [];
			for(i=0; i<questionArr.length; i++){
				questionHtml += questionArr[i];
				if(i<questionArr.length-1){
					switch (i){
					case 0: 
						correctAnswer = content.optionA; 
						break;
					case 1: 
						correctAnswer = content.optionB; 
						break;
					case 2: 
						correctAnswer = content.optionC; 
						break;
					case 3: 
						correctAnswer = content.optionD; 
						break;
					}
					answerArr.push(correctAnswer);
					len = correctAnswer.length;
					if(chinese.test(correctAnswer)){
						len += correctAnswer.match(chinese).length;
					}
					width = getContentWidth(len);
					if(isLearned){
						//questionHtml += '<input class="input-line" style="width:'+width+'px;" value="'+correctAnswer+'" disabled="disabled"/>';
						questionHtml += '<span class="span-line">'+correctAnswer+'</span>';
					}else{
						//questionHtml += "<input class='input-line' style='width:"+width+"px;'/>";
						questionHtml += '<span class="span-line"></span>';
					}
				}
			}
			questionHtml = questionHtml.replace(/<\/p>(\s)*$/,"<span style='color: #999;'>（选词填空2）</span></p>");
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box pace-last-box dian-xuan-box">';
			_html += '<div class="content inte-learn-mid-text-wrap" data="'+index+'">';
			if(isLearned){
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml+'</div>';
			}else{
				_html += '<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc">'+questionHtml;
				_html += '<div class="answer-result"></div>';
				_html += '<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>';
				_html += '</div>';
			}
			answerArr.sort(function(){return Math.random() > 0.5 ? -1 : 1;});//随机排序
			_html += '<div class="answer-item-div">备选项：</div>';
			_html += '<ul class="answer-ul">';
			for(var i=0; i<answerArr.length; i++){
				_html += '<li><span>'+answerArr[i]+'</span></li>';
			}
			_html += '</ul>';
			_html += '<div class="tips none inte-learn-sm-nalysis">';
			_html += 	'<div class="tips-parse">提示：</div>';
			_html += 	'<div class="tips-parse-text"></div>';
			_html += 	'<br style="clear: both;"/>';
			_html += '</div>';
			_html += '<div class="parse none inte-learn-sm-nalysis">';
			if(content.parse!=null && content.parse!=""){
				_html += 	'<div class="tips-parse">解析：</div>';
				_html += 	'<div class="tips-parse-text">'+content.parse+'</div>';
				_html += 	'<br style="clear: both;"/>';
			}   
			_html += '</div>';
			_html += "</div></div>";
			break;
		case 101://一级标题
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box" id="title_'+(index-1)+'">';
			_html += '<div class="inte-learn-big-title">';
			_html +=	'<div class="inte-learn-big-title-inner">';
			_html +=		'<h2>'+content.question+'</h2>';
			_html +=	'</div>';
			_html +='</div></div>';
			break;
		case 102://二级标题
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box" id="title_'+(index-1)+'">';
			_html += '<div class="inte-learn-mid-title">'+content.question+'</div>';
			_html += '</div>';
			break;
		case 103://三级标题
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box" id="title_'+(index-1)+'">';
			_html += '<div class="inte-learn-third-title">'+content.question+'</div>';
			_html += '</div>';
			break;
		case 104://子标题
			_html += '<div data_progress="'+(progress+1)+'" class="inte-learn-box">';
			_html += '<div class="inte-learn-sm-title">【'+content.question+'】</div>';
			_html += '</div>';
			break;
		}
		if(content.questionType<100){
			$(".progress_"+progress).addClass("learned-title");
			progress++;
		}
		if(!isLearned && (haveQuestion)){
			break;//跳出while循环
		}else{
			content = grammarContentArr[index++];
		}
	}
	$("#next-bar").prevAll().hide();
	$("#next-bar").before(_html);
	$("#next-bar").prevAll().hide();
	$("#card>div[data_progress='"+progress+"']").show();
	/*if(learnProgress==0){
	}*/
	
	$("#card .answer-result").prev("p,div").css({"display":"inline-block"});
	$("#card>.inte-learn-box").removeClass("last-box");
	$("#card>.inte-learn-box:last").addClass("last-box");
	/*$("#card>.cut-line-div").prev().css("padding-bottom","40px");*/
	//显示进度信息
	$("#progress").text(progress);
	var width = Math.round(progress*100/inteTotalNum)+"%";
	$(".progress-inner").css("width",width).children("div").text(width);
	
	
	/*if(nextStatus==1){
		$("#card>.inte-learn-box:last").addClass("module-last-box");
	}*/
	if(isLearned && nextStatus==2){
		//$(".bottom-btn-div").show();
	}else{
		if($(".inte-learn-submit:visible").length==0){
			$("#next-bar").show();
		}else{
			$("#next-bar").hide();
		}
	}
	
	$(".s-active").bind("click", function(e) {
		e.stopPropagation();
		$(this).toggleClass("select-clicked").children("ul").toggle();
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	});

	$(".s-active li").click(function() {
		$(this).parent().siblings("h3").text($(this).text()).attr("data", $(this).attr("data"));
	});
	
	$(".last-box .option label").click(function(){
		if($(this).find('input').prop("disabled")){
			return;
		}
		if($(this).find('input:radio').length>0 && $(this).children(".checked").length>0){
			return;
		}
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	});
	$(".last-box .border").focus(function(){
		$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	});
	
	if($(".last-box").hasClass("dian-tian-box")){
		$(".last-box .question").mouseup(function(){
			 var txt = window.getSelection?window.getSelection().toString():document.selection.createRange().text;
			 if($(this).find(".select-input").length>0){
				 var value = $(this).find(".select-input").val();
				 $(this).find(".select-input").val(value+txt);
			 }
		});
		$(".last-box .question").find(".input-line,.inte-learn-submit").mouseup(function(e){
			e = e || window.event;
			e.stopPropagation();
		});
		$(".last-box .input-line").click(function(){
			if($(this).hasClass("select-input")){
				return;
			}
			$(".last-box .input-line").removeClass("select-input");
			$(this).addClass("select-input");
		});
		$(".last-box .input-line").eq(0).click(); //默认第一个空为选中状态
	}else if($(".last-box").hasClass("dian-xuan-box")){
		$(".last-box .answer-ul>li").click(function(){
			if($(this).parent().hasClass("disable")){
				return;
			}
			if(!$(this).children("span").hasClass("v-none")){
				$(this).children("span").addClass("v-none");
				var text = $(this).children("span").text();
				$(".last-box .span-line").each(function(i, item){
					if($(item).text()==""){
						$(item).text(text);
						return false;
					}
				});
			}
		});
		$(".last-box .span-line").click(function(){
			if($(this).hasClass("disable")){
				return;
			}
			var text = $(this).text();
			if(!text==""){
				$(".last-box .answer-ul>li").each(function(i, item){
					if($(item).children("span").text()==text){
						$(item).children("span").removeClass("v-none");
						return false;
					}
				});
				$(this).text("");
			}
		});
	}
	
	if(firstLoad){
		firstLoad = false;
		$('.inte-learn-box input').iCheck({
			checkboxClass: 'icheckbox_minimal-green',
			radioClass: 'iradio_minimal-grey',
			increaseArea: '20%' // optional
		});
	}else{
		$('.inte-learn-box label>input').iCheck({
			checkboxClass: 'icheckbox_minimal-green',
			radioClass: 'iradio_minimal-grey',
			increaseArea: '20%' // optional
		});
	}
	$(".inte-learn-box:last .option>li>label:last").css("margin-bottom","0");
	//scrollBotom();
}

function getContentWidth(len){
	var width = (len+2)*20;
	width = width<120?120:width;
	return width;
}

function scrollBotom(){
	var heigt = $("#card").height();
    $(".card-box").animate({'scrollTop': heigt+"px"},1000);
}

/**
 * 上一步
 */
function prevStep(){
	if(progress<=1){
		return;
	}
	if(index>grammarContentArr.length){
		index = grammarContentArr.length;
		/*$(".bottom-btn-div").hide();
		$("#next-bar").show();
		learnProgress = -1;
		return;*/
	}
	var content = grammarContentArr[index-1];
	var curObj = $("#card>div").eq(index-1);
	if(content.questionType<100){ //试题
		if(content.questionType!=4 && $(curObj).find(".inte-learn-submit").length==0){//已答
			$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
			$(curObj).find(":text").prop("disabled", false).css("color", "").val("");
			$(curObj).find(":radio").prop("disabled", false).css("color", "").prop("checked", false).parent().removeClass("checked");
			$(curObj).find(":checkbox").prop("disabled", false).css("color", "").prop("checked", false).parent().removeClass("checked");
			$(curObj).find(".question-select").removeClass("s-disable").addClass("s-active").bind("click", function(e) {
				e.stopPropagation();
				$(this).toggleClass("select-clicked").children("ul").toggle();
				$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
			});
			$(curObj).find(".question-select>h3").text("请选择").removeAttr("data").css("color", "");
			$(curObj).find(".span-line").removeClass("disable").css("color","");
			$(curObj).find(".answer-ul").removeClass("disable");
			$(curObj).find(".parse").hide();
			$(curObj).find(".question").append('<button class="inte-learn-submit my-btn btn-green rounded" onclick="parseContent(this);">提交</button>');
			$("#next-bar").hide();
		}else{//未答
			learnProgress = -1;
			$("#card>.last-box").prev().addClass("last-box").next().remove();
			$("#next-bar").show();
			progress--;
			$(".progress_"+progress).removeClass("learned-title");
			//进度
			$("#progress").text(progress);
			var width = Math.round(progress*100/inteTotalNum)+"%";
			$(".progress-inner").css("width",width).children("div").text(width);
			index--;
			var prevContent = grammarContentArr[index-1];
			while(prevContent.questionType>=100){//去除标题和分割线
				$("#card>.last-box").prev().addClass("last-box").next().remove();
				index--;
				prevContent = grammarContentArr[index-1];
			}
			nextStatus = 0;
			$("#card").children("div[data_progress='"+progress+"']").show();
		}
	}
}

/**
 * 回到第一步
 */
function prevAllStep(){
	$("#titleList .learned-title").removeClass("learned-title");
	$("#next-bar").prevAll().remove();
	//$(".bottom-btn-div").hide();
	index = 0;
	progress = 0;
	learnProgress = -1;
	showGrammarContent();
}

/**
 * 下一步
 */
function nextStep(){
	if(nextStatus==2 && $("#next-bar").is(":visible")){
		return;
	}
	$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	if($("#next-bar").is(":visible")){
		showNext();
		$(".tips-parse-text").css({
			maxWidth: cardRemWidth.ParseMaxWidth + 'rem'
		});
	}else if($(".inte-learn-submit:visible").length>0){
		parseContent();
	}
	//scrollBotom();
}

/**
 * 跳到最后一步
 */
function nextAllStep(){
	if(nextStatus==2 && $("#next-bar").is(":visible")){
		return;
	}
	learnProgress = 0;
	$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	if($("#next-bar").is(":visible")){
		showNext();
		$(".tips-parse-text").css({
			maxWidth: cardRemWidth.ParseMaxWidth + 'rem'
		});
	}else if($(".inte-learn-submit:visible").length>0){
		parseContent();
		showGrammarContent();
	}
	//scrollBotom();
}

/**
 * 跳步
 */
function skipStep(_index, _progress){
	learnProgress = _progress==0?-1:_progress;
	if($("#card>div[data_progress='"+(_progress+1)+"']").length>0){
		$("#card>div[data_progress='"+(_progress+1)+"']").show();
		$("#card>div[data_progress='"+(_progress+1)+"']").last().nextUntil("#next-bar").remove();
		index = _index+1;
		progress = _progress;
		$("#progress").text(_progress+1);
		var width = Math.round((_progress+1)*100/inteTotalNum)+"%";
		$(".progress-inner").css("width",width).children("div").text(width);
		$("#next-bar").show();
		$("#titleList .learned-title").each(function(){
			if(parseInt($(this).attr("data_progress"))>_progress){
				$(this).removeClass("learned-title");
			}
		});
		return;
	}
	$(".answer-result:last").removeClass("inte-learn-pass").removeClass("inte-learn-error");
	if($("#next-bar").is(":visible")){
		showNext();
		$(".tips-parse-text").css({
			maxWidth: cardRemWidth.ParseMaxWidth + 'rem'
		});
	}else if($(".inte-learn-submit:visible").length>0){
		parseContent();
		showGrammarContent();
	}
}


/**
 * 显示接下来的内容
 */
function showNext(btn){
	$("#next-bar").hide();
	if(nextStatus==2){
//		index++;
//		$(".bottom-btn-div").show();
	}else{
		showGrammarContent();
	}
}


/**
 * 解析语法卡片内容
 */
function parseContent(btn){
	var isWrong = false;
	var nextStep = false; //通过下一步按钮触发的
	if(btn==null){
		nextStep = true;
		btn = $(".inte-learn-submit:visible").last();
	}else{
		btn = $(btn);
	}
	var curQuestion = btn.parents(".content");
	var i = parseInt($(curQuestion).attr("data"))-1;
	var content = grammarContentArr[i];
	var wrongTips = "";
	var tipsArr = [];
	if(content.wrongTips1!=null && content.wrongTips1!=""){
		tipsArr.push(content.wrongTips1);
	}
	if(content.wrongTips2!=null && content.wrongTips2!=""){
		tipsArr.push(content.wrongTips2);
	}
	if(content.wrongTips3!=null && content.wrongTips3!=""){
		tipsArr.push(content.wrongTips3);
	}
	if(tipsArr.length>0){
		var tipsIndex = $(curQuestion).attr("data_ti");
		if(tipsIndex==null || tipsIndex==""){
			tipsIndex = 0;
		}else if(tipsIndex >= tipsArr.length){
			tipsIndex = tipsArr.length-1;
		}
		wrongTips = tipsArr[tipsIndex];
		$(curQuestion).attr("data_ti",++tipsIndex);
	}
	switch(content.questionType){
	case 0: //文本
		break;
	case 1: //单选
		if(nextStep){
			$(curQuestion).find("input[value='"+content.correctOption+"']").parent().click();
		}
		var answer = $(curQuestion).find("input:checked").val();
		answer = $.trim(answer);
		if(answer==null || answer!=content.correctOption){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
			$(curQuestion).find(".option input").prop("disabled",true);
		}
		break;
	case 2: //多选
		if(nextStep){
			$(curQuestion).find("input").prop("checked", false).parent().removeClass("checked");
			$(content.correctOption.split(",")).each(function(i, item){
				$(curQuestion).find("input[value='"+item+"']").parent().click();
			});
		}
		var answer = "";
		$(curQuestion).find("input:checked").each(function(i,item){
			answer += $(item).val()+",";
		});
		answer = answer.substring(0,answer.length-1);
		if(answer!=content.correctOption){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
			$(curQuestion).find(".option input").prop("disabled",true);
		}
		break;
	case 3: //填空
	case 7: //选词1
		if(nextStep){
			$(curQuestion).find("input").each(function(i, item){
				switch(i){
				case 0:
					$(item).val(content.optionA.split("|")[0]); break;
				case 1:
					$(item).val(content.optionB.split("|")[0]); break;
				case 2:
					$(item).val(content.optionC.split("|")[0]); break;
				case 3:
					$(item).val(content.optionD.split("|")[0]); break;
				case 4:
					$(item).val(content.optionE.split("|")[0]); break;
				}
			});
		}
		var completionWrong = false;
		$(curQuestion).find("input").each(function(i,item){
			if(i==0 && !checkCompletionAnswer($(item).val(), content.optionA) ||
				i==1 && !checkCompletionAnswer($(item).val(), content.optionB) ||
				i==2 && !checkCompletionAnswer($(item).val(), content.optionC) ||
				i==3 && !checkCompletionAnswer($(item).val(), content.optionD) ||
				i==4 && !checkCompletionAnswer($(item).val(), content.optionE)){
				completionWrong = true;
				return true;
			}else{
				$(item).prop("disabled", true).css("color","#00b48b").val(tempRightAnswer);
			}
		});
		if(completionWrong){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
			if(content.questionType==10){
				$(curQuestion).find(".question").unbind("mouseup");
				$(curQuestion).find(".input-line").removeClass("select-input").unbind("click");
			}
			$("body,html").focus();
		}
		break;
	case 5: //选填题
		if(nextStep){
			$(content.correctOption.split(",")).each(function(i, item){
				$(curQuestion).find(".question-select").eq(i).find("li[data='"+item+"']").click();
			});
		}
		var selectionWrong = false;
		var answerArr = content.correctOption.split(",");
		var result;
		$(curQuestion).find(".question-select").each(function(i, item){
			result = $(item).children("h3").attr("data");
			if(result==answerArr[i]){
				$(item).unbind("click").removeClass("s-active").addClass("s-disable");
				$(item).children("h3").css("color","#00b48b");
				$(item).children("ul").hide();
			}else{
				selectionWrong = true;
			}
		});
		if(selectionWrong){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
		}
		break;
	case 6: //综合题
		if(nextStep){
			var answerArr = content.correctOption.split(",");
			var answer;
			$(curQuestion).find(".question-select,:text").each(function(i, item){
				if($(item).is(":text")){//填空
					switch(i){
					case 0: answer = content.optionA; break;
					case 1: answer = content.optionB; break;
					case 2: answer = content.optionC; break;
					case 3: answer = content.optionD; break;
					case 4: answer = content.optionE; break;
					}
					$(item).val(answer);
				}else{//选填
					answer = answerArr[i];
					$(item).find("li[data='"+answer+"']").click();
				}
			});
		}
		var selectionWrong = false;
		var answerArr = content.correctOption.split(",");
		var result, answer;
		$(curQuestion).find(".question-select,:text").each(function(i, item){
			if($(item).is(":text")){//填空
				switch(i){
				case 0: answer = content.optionA; break;
				case 1: answer = content.optionB; break;
				case 2: answer = content.optionC; break;
				case 3: answer = content.optionD; break;
				case 4: answer = content.optionE; break;
				}
				if(answer==$.trim($(item).val())){
					$(item).prop("disabled", true).css("color","#00b48b").val(answer);
				}else{
					selectionWrong = true;
				}
			}else{//选填
				result = $(item).children("h3").attr("data");
				if(result==answerArr[i]){
					$(item).unbind("click").removeClass("s-active").addClass("s-disable");
					$(item).children("h3").css("color","#00b48b");
					$(item).children("ul").hide();
				}else{
					selectionWrong = true;
				}
			}
		});
		if(selectionWrong){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
			$(curQuestion).find(".select-input").removeClass("select-input");
		}
		break;
	case 8: 
		if(nextStep){
			if(nextStep){
				$(curQuestion).find(".span-line").each(function(i, item){
					switch(i){
					case 0:
						$(item).text(content.optionA.split("|")[0]); break;
					case 1:
						$(item).text(content.optionB.split("|")[0]); break;
					case 2:
						$(item).text(content.optionC.split("|")[0]); break;
					case 3:
						$(item).text(content.optionD.split("|")[0]); break;
					case 4:
						$(item).text(content.optionE.split("|")[0]); break;
					}
				});
			}
		}
		var completionWrong = false;
		$(curQuestion).find(".span-line").each(function(i,item){
			if(i==0 && !checkCompletionAnswer($(item).text(), content.optionA) ||
				i==1 && !checkCompletionAnswer($(item).text(), content.optionB) ||
				i==2 && !checkCompletionAnswer($(item).text(), content.optionC) ||
				i==3 && !checkCompletionAnswer($(item).text(), content.optionD) ||
				i==4 && !checkCompletionAnswer($(item).text(), content.optionE)){
				completionWrong = true;
				return false;
			}
		});
		if(completionWrong){
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-error");
			if(wrongTips!=null && wrongTips!=""){
				$(curQuestion).find(".tips").show().find(".tips-parse-text").text(wrongTips);
			}
			$(curQuestion).find(".parse").hide();
			$(curQuestion).find(".span-line").click();//归位
			isWrong = true;
		}else{
			$(curQuestion).find(".answer-result").removeClass("inte-learn-pass").removeClass("inte-learn-error").addClass("inte-learn-pass");
			$(curQuestion).find(".parse").show();
			$(curQuestion).find(".tips").hide();
			//解除绑定事件
			$(curQuestion).find(".span-line").addClass("disable").css("color","#00b48b");
			$(curQuestion).find(".answer-ul>li>span").removeClass("v-none");
			$(curQuestion).find(".answer-ul").addClass("disable");
			$("body,html").focus();
		}
		break;
	}
	if(!isWrong){//答对
		$(btn).remove();
		$("#next-bar").show();
		//updateUserUnitLearnProgress(progress);
	}
	scrollBotom();
}

/**
 * 检查填空题答案
 * @param item
 * @param answer
 * @returns {Boolean}
 */
var tempRightAnswer = "";
function checkCompletionAnswer(item, answer){
	var reg1 = /\s+/g;
	var reg2 = /\s+(?=[!\?,\.'";:\+\-\*\/！？，。’“；：、])/g;
	item = $.trim(item);
	item = item.replace(reg1, " ");
	item = item.replace(reg2, "");
	var answerArr = answer.split("|");
	var i, answerItem;
	for(i in answerArr){
		answerItem = answerArr[i];
		answerItem = $.trim(answerItem);
		answerItem = answerItem.replace(reg1, " ");
		answerItem = answerItem.replace(reg2, "");
		if(item==answerItem){
			tempRightAnswer = answerArr[i];
			return true;
		}
	}
	return false;
}
/**
 * 更新学生学习进度
 * @param _progress
 */
function updateUserUnitLearnProgress(_progress){
	if(nextStatus==2){//学完
		_progress = 0;
	}
	$.post("/student/grammar/updateUserUnitLearnProgress",{"unitId": unitId, "progress": _progress},
			function(data){
	},"json");
}


function initContent(btn){
	$(".active input").each(function(){
		if($(this).prop("disabled")){
			return true;
		}
		if($(this).attr("type")=="checkbox" || $(this).attr("type")=="radio"){
			$(this).prop("checked",false);
		}else{
			$(this).val("");
		}
	});
	$(".active .select-q").each(function(){
		if(!$(this).prop("disabled")){//答错了
			$(this).find("option[value='']").prop("selected",true);
		}
	});
	$(".active").find(".tips").remove();
	$(btn).toggle().siblings().toggle();
}

/**
 * 返回退出
 */
function exit(){
	window.top.close()
}

function goTop(){
	$("html,body").animate({'scrollTop': 0},1000);
}