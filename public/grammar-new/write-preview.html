<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>写作方法</title>
		<!-- <link rel="stylesheet" type="text/css" href="css/global/3.0public.css" /> -->
		<!-- <link rel="stylesheet" type="text/css" href="css/global/base.css" /> -->
		<link rel="stylesheet" type="text/css" href="/css/common.css" />
		<link rel="stylesheet" type="text/css" href="/css/global/ui-dialog.css" />
		<link href="/common/plugin/iCheck/css/custom.css" rel="stylesheet">
		<link href="/common/plugin/iCheck/skins/all.css" rel="stylesheet">
		<link rel="stylesheet" type="text/css" href="/css/grammar-main.css" />
		<link rel="stylesheet" type="text/css" href="/grammar-new/css/grammar-new-card-preview.css" />
	</head>

	<body style="position: relative;">
		<div class="top-box-fixed page-head-wrap ">
			<div class="container">
				<div class="sm-header-title">
					<span id="name"></span>
					<div class="fl page-logo">写作方法&gt;<strong id="unitName"></strong></div>
				</div>
				<div class="fr">
					<div class="fl sm-header-title-goback"><a href="javascript:goBack();">退出</a></div>
				</div>
			</div>
			<div class="container top-progress">
				<div id="card-top" class="main-top">
					<div class="left">
						<p>知识点学习进度：</p>
						<ul id="progress-list">
						</ul>
						<p id="progress-num" class="number">(0/0)</p>
					</div>
				</div>
				<!-- <div id="screen-top" class="inte-learn-progress-box inte-learn-top-box" style="display: none;">
					<div class="progress-box" style="padding-left: 30px;">
						<span>学习进度</span>
						<div class="progress-outer">
							<div class="progress-inner">
							</div>
						</div>
						<span class="progress" id="progress">1</span>/<span class="inte-total-num" id="inteTotalNum">1</span>
					</div>
					<img style="margin-top: -28px;" src="images/default/interactive-learning-progress-box1.png" />
				</div> -->
				<!-- <div class="show-type">
					<span class="fl">展示形式：</span>
					<div id="select_showType" class="fl card">
						<i class="circle"></i>
						<i class="card-icon" title="卡片式"></i>
						<i class="screen-icon" title="全屏式"></i>
					</div>
				</div> -->
			</div>
		</div>

		<!-- 卡片形式  开始 -->
		<div id="card-box">
			<div id="card_mode" class="container">
				<div class="main-content">
					<div class="top clear">
						<h2 class="fl cur-title-name"></h2>
						<!-- 
						@ did: 已经做过
						@ active: 当前位置
					 -->
						<ul class="fr subtitleList">
							<li class="did">
								<div class="main-content-icon-box">
									<div class="main-content-icon-inner">
										<i class="main-content-icon"></i>
									</div>
								</div>
								<span class="main-content-text">观察</span>
							</li>
							<li>
								<div class="main-content-icon-box">
									<div class="main-content-icon-inner">
										<i class="main-content-icon"></i>
									</div>
								</div>
								<span class="main-content-text">提炼</span>
							</li>
							<li>
								<div class="main-content-icon-box">
									<div class="main-content-icon-inner">
										<i class="main-content-icon"></i>
									</div>
								</div>
								<span class="main-content-text">强化</span>
							</li>
							<li>
								<div class="main-content-icon-box">
									<div class="main-content-icon-inner">
										<i class="main-content-icon"></i>
									</div>
								</div>
								<span class="main-content-text">应用</span>
							</li>
						</ul>
					</div>

					<!-- 
					@ pass: 答对 
					@ fail: 答错 
				 -->

					<!-- 观察  开始 -->
					<div class="tab s-container1" style="display: block;">
						<div class="tab-title tab-title-guancha clear">
							<img src="images/default/il-guanchadian-deep.png">
							<span>观察</span>
						</div>
						<ul class="c-index-list">
						</ul>
						<div class="tab-content">
							<div class="card-item"></div>
							<div class="btn-box">
								<button class="btn submit-btn" type="button" style="display: none;" onclick="submitAnswer1(this);">提交</button>
								<button class="btn next-btn" type="button" style="display: none;" onclick="showNext();">下一步</button>
								<button class="btn help-btn" type="button" style="display: none;" onclick="seekHelp(this);">求助</button>
							</div>
						</div>
					</div>
					<!-- 观察  结束 -->

					<!-- 提炼  开始 -->
					<div class="tab s-container2">
						<div class="tab-title tab-title-zhishi clear">
							<img src="images/default/il-zhishi-pointer-deep.png">
							<span>提炼</span>
						</div>
						<ul class="c-index-list"></ul>
						<div class="tab-content">
							<div class="card-item"></div>
							<div class="btn-box">
								<button class="btn submit-btn" type="button" style="display: none;" onclick="submitAnswer1(this);">提交</button>
								<button class="btn next-btn" type="button" style="display: none;" onclick="showNext();">下一步</button>
								<button class="btn help-btn" type="button" style="display: none;" onclick="seekHelp(this);">求助</button>
							</div>
						</div>
					</div>
					<!-- 提炼  结束 -->

					<!-- 强化  开始 -->
					<div class="tab s-container3">
						<div class="tab-title tab-title-gonggu clear">
							<img src="images/default/il-gonggu-deep.png">
							<span>强化</span>
						</div>
						<ul class="c-index-list"></ul>
						<div class="tab-content">
							<div class="card-item"></div>
							<div class="btn-box">
								<button class="btn submit-btn" type="button" style="display: none;" onclick="submitAnswer1(this);">提交</button>
								<button class="btn next-btn" type="button" style="display: none;" onclick="showNext();">下一步</button>
								<button class="btn help-btn" type="button" style="display: none;" onclick="seekHelp(this);">求助</button>
							</div>
						</div>
					</div>
					<!-- 强化  结束 -->

					<!-- 应用  开始 -->
					<div class="tab s-container4">
						<div class="tab-title tab-title-lianxi clear">
							<img src="images/default/il-lianxi-deep.png">
							<span>应用</span>
						</div>
						<ul class="c-index-list"></ul>
						<div class="tab-content">
							<div class="card-item"></div>
							<div class="btn-box">
								<button class="btn submit-btn" type="button" style="display: none;" onclick="submitAnswer1(this);">提交</button>
								<button class="btn next-btn" type="button" style="display: none;" onclick="showNext();">下一步</button>
								<button class="btn help-btn" type="button" style="display: none;" onclick="seekHelp(this);">求助</button>
							</div>
						</div>
					</div>
					<!-- 应用  结束 -->
				</div>
			</div>
			<!-- 卡片形式  结束 -->
		</div>
		<!-- 卡片形式  结束 -->

		<!-- 全屏形式  开始 -->
		<div id="fullScreen_mode" class="container" style="display: none;">
			<div class="container inte-learn-container" style="margin-bottom: 100px; background-color: #fff">
				<!-- <div class="order-div">
				</div>
				<div class="report-div">
				</div> -->
				<div id="contentList" style="padding-bottom: 30px;"></div>
				<div id="next-bar" onclick="showNext();" class="s-more-bar s-more-up" style="bottom: 0px;">
					<div class="rect">
						<div class="animate-arrow" style="top: 8px;"></div>
					</div>
				</div>
				<div class="bottom-btn-div none">
					<div class="cut-line-div"></div>
					<div class="over-tips">恭喜你学完了本互动卡片的所有内容~~！
					</div>
				</div>
			</div>
			<div class="goTop" onclick="goTop();"></div>
		</div>
		<!-- 全屏形式  结束 -->

		<!-- 知识体系 -->
		<div id="knowlege">
			<div class="knowlege-system-box">
				<div class="knowlege-system">
					<div class="knowlege-icon"></div>
					<div class="knowlege-text">知识体系</div>
				</div>

				<div id="red" class="title-list-div">
					<ul id="titleList" class="title1-list knowledge-map">
					</ul>
				</div>
			</div>
		</div>

		<!-- 帮助指引  开始 -->
		<div class="help-page" style="display: none;">
			<div class="container">
				<div class="tip">
					<p>点击这个按钮</p>
					<p>可以切换展示形式哦！</p>
					<button id="close_help" type="button">知道啦</button>
				</div>
				<span class="bg"></span>
			</div>
		</div>
		<!-- 帮助指引  结束 -->

		<!-- 学习目标  开始 -->
		<div id="learnTarget" class="learn-target">
			<div id="learnTargetDia" class="il-popup il-target-popup">
				<div class="inner">
					<i class="bulb"></i>
					<span class="line"></span>
					<ul class="main"></ul>
				</div>
			</div>
		</div>
		<!-- 学习目标  结束 -->

		<!-- 学习小结  开始 -->
		<div id="learnSummary" class="learn-summary">
			<div id="learnSummaryDia" class="il-popup il-sumUp-popup">
				<div class="inner">
					<i class="bulb"></i>
					<span class="line"></span>
					<div id="main_wrap" class="main-wrap">
						<ul class="main"></ul>
					</div>
				</div>
			</div>
		</div>
		<!-- 学习小结  结束 -->
		<script src="/js/global/jquery-1.12.4.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="/common/plugin/iCheck/icheck.js"></script>
		<script src="/common/plugin/iCheck/js/custom.min.js"></script>
		<script src="/js/global/jquery.nicescroll.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/main.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/global/common.js" type="text/javascript"></script>
		<script src="/js/global/dialog.js" type="text/javascript"></script>
		<script src="/grammar-new/js/write-preview.js" type="text/javascript" charset="utf-8"></script>
	</body>

</html>
