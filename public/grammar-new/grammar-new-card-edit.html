<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>语法卡片制作系统</title>
		<link rel="stylesheet" type="text/css" href="/css/grammar-common.css" />
		<link rel="stylesheet" type="text/css"  href="/common/plugin/iCheck/css/custom.css">
		<link rel="stylesheet" type="text/css"  href="/common/plugin/iCheck/skins/all.css">
		<link rel="stylesheet" type="text/css"  href="/css/global/ui-dialog.css">
   		<link rel="stylesheet" type="text/css"  href="/css/global/management-public.css">
		<link rel="stylesheet" type="text/css"  href="/css/grammar-main.css" />
		<link rel="stylesheet" type="text/css"  href="/css/grammar-interactive-learning.css" />
		<link rel="stylesheet" type="text/css"  href="/css/grammar-card-edit.css">
		<style>
		.ckeditor-html5-audio audio{
				transform: scale(0.5);
		}
		</style>
	</head>

	<body>
		<div class="top-box">
			<div class="container-top inte-learn-progress-box">
				<div class="inte-learn-progress-content">
					<div class="title-div">
						<span style="margin-right: 100px;" id="total-title">语法卡片制作系统</span>
						标题：<span class="card-title" id="name"></span>
						共制作了<span id="inteNum" class="red">0</span>步
						<button class="button right" onclick="exit();">退出</button>
					</div>
					<div class="add-btn">
						<ul>
							<!-- <li><button class="button" onclick="addTemplate(100)">分割线</button></li>
							<li><div class="cut-line-y"></div></li> -->
							<li class="text">标题：</li>
							<li>
								<button class="button" onclick="addTemplate(101)">一级</button>
								<img class="select-title" src="../images/card-edit-title.png">
								<ul data="101" class="example-list">
									<li>学习目标</li>
									<li>新课引入</li>
									<li>学习与互动</li>
									<li>巩固与训练</li>
								</ul>
							</li>
							<li>
								<button class="button" onclick="addTemplate(102)">二级</button>
							</li>
							<li>
								<button class="button" onclick="addTemplate(103)">三级</button>
							</li>
							<li>
								<button class="button" onclick="addTemplate(104)">子标题</button>
								<img class="select-title" src="../images/card-edit-title.png">
								<ul data="104" class="example-list">
									<li>观察</li>
									<li>提炼</li>
									<li>强化</li>
									<li>应用</li>
								</ul>
							</li>
							<li><div class="cut-line-y"></div></li>
							<li><button class="button" onclick="addTemplate(4)">文本/图片</button></li>
							<li><div class="cut-line-y"></div></li>
							<li><button class="button" onclick="addTemplate(1)">单选</button></li>
							<li><button class="button" onclick="addTemplate(2)">多选</button></li>
							<li><div class="cut-line-y"></div></li>
							<li><button class="button" onclick="addTemplate(3)">填空</button></li>
							<li><button class="button" onclick="addTemplate(5)">选填</button></li>
							<li><button class="button" onclick="addTemplate(6)">综合</button></li>
							<li><div class="cut-line-y"></div></li>
							<li><button class="button" onclick="addTemplate(7)">选词1</button></li>
							<li><button class="button" onclick="addTemplate(8)">选词2</button></li>
						</ul>
						<button class="button-white rounded right" style="margin-top: 1px;" id="save-card" onclick="saveCard();">保存卡片</button>
						<!-- <span id="saveTips" class="save-tips right">保存成功</span> -->
					</div>
				</div>
				<img src="../images/interactive-learning-progress-box1.png" />
			</div>
		</div>
		<div id="copy-box" class="copy-box">
			<div>
				<button id="batchCopyBth" class="button btn-big" onclick="toBatchCopy(this);">批量复制</button>
				<button id="batchCopyBth" class="button btn-big" onclick="toBatchCopy2(this);">批量删除</button>
				<div>剪切板已有：<span id="copiedNum" style="color: red">0</span>个</div>
			</div>
			<div class="d-none" id="copy1">
				<button class="button" onclick="copySelectAll();">全选</button>
				<button class="button" onclick="batchCopy();">确认复制</button>
				<button class="button-white" onclick="copyCancel();">取消</button>
				<div>已选择：<span id="selectedNum" style="color: red">0</span>个</div>
			</div>
			<div class="d-none" id="copy2">
				<button class="button" onclick="copySelectAll();">全选</button>
				<button class="button" onclick="batchCopy2();">确认删除</button>
				<button class="button-white" onclick="copyCancel();">取消</button>
				<div>已选择：<span id="selectedNum" style="color: red">0</span>个</div>
			</div>
			<button id="stickBth" class="button" onclick="stick();">粘贴(0)</button>
		</div>
		<div id="card" class="container inte-learn-container">
			<span class="order-div"></span>
			<span class="selected"></span>
		</div>
		<div class="btn-div">
			<ul>
				<li>
					<button id="saveBth" class="button" onclick="save();">保存</button>
					<button id="editBth" class="button" onclick="edit();">编辑</button>
				</li>
				<li>
					<button id="copyBth" class="button" onclick="copy();">复制</button>
<!--					<button id="stickBth1" class="button" onclick="stick();">粘贴(0)</button>-->
				</li>
				<li>
					<button id="moveUpBth" class="button" onclick="moveUp();">上移↑</button>
					<button id="downDownBth" class="button" onclick="moveDown();">下移↓</button>
				</li>
				<li>
					<button id="deleteBth" class="button" onclick="deleteContent();">删除</button>
				</li>
			</ul>
		</div>
		<!-- 内容模板开始 -->
		<div class="d-none">
			<!-- 分割线 -->
			<div id="cutLineTemp">
				<div data="100" class="cut-line selected" onclick="selectThis(this);">
				</div>
			</div>
			<!-- 一级标题 -->
			<div id="firstTitleTemp">
				<div data="101" class="first-title inte-learn-box selected" onclick="selectThis(this);">
					<div class="edit">
						<h1 style="text-align: center;">一级标题：<input name="title"/></h1>
					</div>
					<div class="show inte-learn-big-title d-none">
						<div class="inte-learn-big-title-inner">
							<span></span>
							<h2></h2>
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="101" />选择</label>
					</div>
				</div>
			</div>
			<!-- 二级标题 -->
			<div id="secondTitleTemp">
				<div data="102" class="second-title inte-learn-box selected" onclick="selectThis(this);">
					<div class="edit">
						<h2>二级标题：<input name="title"/></h2>
					</div>
					<div class="show inte-learn-mid-title d-none">
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="102" />选择</label>
					</div>
				</div>
			</div>
			<!-- 三级标题 -->
			<div id="thirdTitleTemp">
				<div data="103" class="third-title inte-learn-box selected" onclick="selectThis(this);">
					<div class="edit">
						<h3>三级标题：<input name="title"/></h3>
					</div>
					<div class="show inte-learn-third-title d-none">
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="103" />选择</label>
					</div>
				</div>
			</div>
			<!-- 四级标题 -->
			<div id="fourthTitleTemp">
				<div data="104" class="fourth-title inte-learn-box selected" onclick="selectThis(this);">
					<div class="edit">
						<h3>子标题：<input name="title"/></h3>
					</div>
					<div class="show inte-learn-sm-title d-none">
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="104" />选择</label>
					</div>
				</div>
			</div>
			<!-- 文本 -->
			<div id="textTemp">
				<div data="4" class="text inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit">
						<textarea class="ckeditor"></textarea>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div class="question inte-learn-sm-text inte-learn-mid-text"></div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="4" />选择</label>
					</div>
				</div>
			</div>
			<!-- 单选题模板 -->
			<div id="singleTemp">
				<div data="1" class="single-choice inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">单选题</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor" tabindex="1"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
			        		<span class="fl option-span">选项：</span>
				        	<div class="fl">
				        		<ul type="none" class="option-ul">
				        			<li><span>A.</span><input name="optionA" class="input-option" type="text" maxlength="100" tabindex="2"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
			        					<label><input type="radio" value="A"/>是否是答案</label></li>
				        			<li><span>B.</span><input name="optionB" class="input-option" type="text" maxlength="100" tabindex="3"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="radio" value="B"/>是否是答案</label></li>
				        			<li><span>C.</span><input name="optionC" class="input-option" type="text" maxlength="100" tabindex="4"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="radio" value="C"/>是否是答案</label></li>
				        			<li><span>D.</span><input name="optionD" class="input-option" type="text" maxlength="100" tabindex="5"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="radio" value="D"/>是否是答案</label></li>
				        		</ul>
				        	</div>
				        	<br style="clear: both;" />
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text" tabindex="6"/><input type="text" tabindex="7"/><input type="text" tabindex="8"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse" tabindex="9"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<ul class="option inte-learn-radio"></ul>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value=""data-questionType="1" />选择</label>
					</div>
				</div>
			</div>
			<!-- 多选题 -->
			<div id="multiTemp">
				<div data="2" class="multi-choice inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">多选题</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor" tabindex="10"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
			        		<span class="fl option-span">选项：</span>
				        	<div class="fl">
				        		<ul type="none" class="option-ul">
				        			<li><span>A.</span><input name="optionA" class="input-option" type="text" maxlength="100" tabindex="11"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
			        					<label><input type="checkbox" value="A"/>是否是答案</label></li>
				        			<li><span>B.</span><input name="optionB" class="input-option" type="text" maxlength="100" tabindex="12"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="checkbox" value="B"/>是否是答案</label></li>
				        			<li><span>C.</span><input name="optionC" class="input-option" type="text" maxlength="100" tabindex="13"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="checkbox" value="C"/>是否是答案</label></li>
				        			<li><span>D.</span><input name="optionD" class="input-option" type="text" maxlength="100" tabindex="14"/>
				        				<button class="button" onclick="uploadImage(this);">添加图片</button>
				        				<label><input type="checkbox" value="D"/>是否是答案</label></li>
				        		</ul>
				        	</div>
				        	<br style="clear: both;" />
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text" tabindex="15"/><input type="text" tabindex="16"/><input type="text" tabindex="17"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse" tabindex="18"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<ul class="option inte-learn-radio"></ul>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="2" />选择</label>
					</div>
				</div>
			</div>
			<!-- 填空题 -->
			<div id="completionTemp">
				<div data="3" class="completion inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">填空题
							<span class="question-tips">注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。</span>
						</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor" tabindex="19"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
			        		<span class="fl option-span">答案：</span>
				        	<div class="fl">
				        		<ul type="none" class="option-ul">
				        			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="100" tabindex="20"/></li>
				        			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="100" tabindex="21"/></li>
				        			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="100" tabindex="22"/></li>
				        			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="100" tabindex="23"/></li>
				        			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="100" tabindex="24"/></li>
				        		</ul>
				        	</div>
				        	<br style="clear: both;" />
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text" tabindex="24"/><input type="text" tabindex="25"/><input type="text"  tabindex="26"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse" tabindex="27"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="3" />选择</label>
					</div>
				</div>
			</div>
			<!-- 选填题 -->
			<div id="selectionTemp">
				<div data="5" class="selection inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">选填题
							<span class="question-tips">注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。</span>
						</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
				        	<div>
				        		<ul type="none" class="option-ul">
				        		</ul>
				        	</div>
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text"/><input type="text"/><input type="text"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div  style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="5" />选择</label>
					</div>
				</div>
			</div>
			<!-- 综合题 -->
			<div id="synthesisTemp">
				<div data="6" class="selection inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">综合题
							<span class="question-tips">注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								提供多个备选项，并指定正确选择时，该空判为选填题；只提供一个备选项时，该空判为填空题，备选项即为正确答案！
							</span>
						</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
				        	<div>
				        		<ul type="none" class="option-ul">
				        		</ul>
				        	</div>
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text"/><input type="text"/><input type="text"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div  style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="6" />选择</label>
					</div>
				</div>
			</div>
			<!-- 点填题 -->
			<div id="clickCompletionTemp">
				<div data="7" class="completion inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">选词1
							<span class="question-tips">注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								该题型类似于填空题，只是答案从题干中点击选出。
							</span>
						</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor" tabindex="19"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
			        		<span class="fl option-span">答案：</span>
				        	<div class="fl">
				        		<ul type="none" class="option-ul">
				        			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="100" tabindex="20"/></li>
				        			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="100" tabindex="21"/></li>
				        			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="100" tabindex="22"/></li>
				        			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="100" tabindex="23"/></li>
				        			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="100" tabindex="24"/></li>
				        		</ul>
				        	</div>
				        	<br style="clear: both;" />
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text" tabindex="24"/><input type="text" tabindex="25"/><input type="text"  tabindex="26"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse" tabindex="27"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="7" />选择</label>
					</div>
				</div>
			</div>
			<!-- 点选题 -->
			<div id="clickSelectTemp">
				<div data="8" class="completion inte-learn-box pace-last-box selected" onclick="selectThis(this);">
					<div class="edit item">
						<div class="question-type">选词2
							<span class="question-tips">注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								该题是将正确答案随机打乱，让学生点击填入。编辑时提供的答案顺序即为正确顺序。
							</span>
						</div>
			        	<div><span class="question-span">题干：</span>
			        		<div class="textarea"><textarea class="ckeditor" tabindex="19"></textarea></div>
		        		</div>
			        	<div style="margin-bottom: 0px;">
			        		<span class="fl option-span">答案：</span>
				        	<div class="fl">
				        		<ul type="none" class="option-ul">
				        			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="100" tabindex="20"/></li>
				        			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="100" tabindex="21"/></li>
				        			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="100" tabindex="22"/></li>
				        			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="100" tabindex="23"/></li>
				        			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="100" tabindex="24"/></li>
				        		</ul>
				        	</div>
				        	<br style="clear: both;" />
			        	</div>
			        	<div class="wrongTips">
			        		错误提示：<input type="text" tabindex="24"/><input type="text" tabindex="25"/><input type="text"  tabindex="26"/>
			        	</div>
			        	<div class="parseDiv">
				       		<span class="parse-span">解析：</span>
				       		<textarea class="input-parse" name="parse" tabindex="27"></textarea>
				       	</div>
					</div>
					<div class="show inte-learn-mid-text-wrap d-none">
						<div style="text-align: left;" class="question inte-learn-sm-text inte-learn-sm-answer-desc"></div>
						<div class="tips inte-learn-sm-nalysis">
							<div class="tips-parse">提示：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
						<div class="parse inte-learn-sm-nalysis">
							<div class="tips-parse">解析：</div>
							<div class="tips-parse-text inte-learn-sm-text"></div>
							<br style="clear: both;">
						</div>
					</div>
					<div class="check-item">
						<label><input type="checkbox" value="" data-questionType="8" />选择</label>
					</div>
				</div>
			</div>
		</div>
		<!-- 内容模板结束 -->
		<!-- 提交审核弹框开始 -->
		<div class="submit-div" style="display: none;">
			<div>错误步骤：<span id="error-position">0</span></div>
			<div>错误描述：</div>
			<div id="error-description" style="text-indent: 2em;"></div>
			<div style="text-align: center; ">
				<button class="save-submit" onclick="saveAndSubmit();">保存并提交审核</button>
			</div>
		</div>
		<div id="copy-tips" class="modal-tips">已复制到剪切板</div>
		<!-- 提交审核弹框结束 -->
		<script src="/js/global/jquery-1.12.4.min.js" type="text/javascript" charset="utf-8"></script>
        <script type="text/javascript" src="/js/global/jquery-form.js"></script>
		<script src="/common/plugin/iCheck/icheck.js"></script>
		<script src="/common/plugin/iCheck/js/custom.min.js"></script>
		<!--[if lt IE 9]>
		<script src="/js/global/PIE.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/global/round.js" type="text/javascript" charset="utf-8"></script>
		<![endif]-->
		<script type="text/javascript" src="/common/plugin/ckeditor/ckeditor.js"></script>
		<script type="text/javascript" src="/js/global/dialog.js"></script>
		<script type="text/javascript" src="/js/global/common.js"></script>
		<script src="/grammar-new/js/grammar-new-card-edit.js" type="text/javascript"></script>
	</body>

</html>
