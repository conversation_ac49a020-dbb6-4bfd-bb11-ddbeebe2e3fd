<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>卡片讲解</title>
		<link rel="stylesheet" type="text/css" href="/css/grammar-common.css" />
		<link rel="stylesheet" type="text/css"  href="/css/global/ui-dialog.css">
		<link href="/common/plugin/iCheck/css/custom.css" rel="stylesheet">
		<link href="/common/plugin/iCheck/skins/all.css" rel="stylesheet">
		<link rel="stylesheet" type="text/css" href="/css/grammar-teach.css" />
	</head>

	<body style="position: relative;">

		<div class="top-box-fixed">
			<div class="sm-header gt-sm-header">
				<div class="sm-header-title container">
					<span id="name" class="fl unitName-title"></span>
					<!-- <div class="sm-header-title-left">卡片讲解</div> -->
					<div class="sm-header-title-right sm-header-title-goback">
						<a href="javascript:exit();">返回</a>
					</div>
					<div class="smdt-selectSize">
						<span>视觉窗口大小调整：</span>
						<button type="button" class="reduce">缩小</button>
						<button type="button" class="zoomIn">加宽</button>
					</div>
					<br style="clear: both;">
				</div>
			</div>
		</div>
		
		<div class="card-box">
			<!-- <div class="cb-fill cb-fill-top"></div> -->
			<div id="card" class="inte-learn-container">
				<div id="next-bar" class="s-more-bar s-more-up" style="bottom: 0px;">
					<div class="rect">
						<div class="animate-arrow" style="top: 0.08rem;"></div> 
					</div>
				</div>
				<div class="bottom-btn-div none">
					<!-- <div class="cut-line-div"></div> -->
					<div class="over-tips">卡片内容讲解完毕！</div>
				</div>
			</div>
		<!-- 	<div class="cb-fill cb-fill-bottom"></div> -->
		
		</div>
		<!-- 知识体系 -->
		<div id="knowlege">
			<div class="knowlege-system-box">
				<div class="knowlege-system">
					<div class="knowlege-icon"></div>
					<div class="knowlege-text">知识体系</div>
				</div>
			</div>
			<div class="title-list-div">
				<ul id="titleList" class="title1-list">
				</ul>
			</div>
		</div>
		<div class="progress-operation">
			<a href="#" id="fullScreen" class="po-btns"></a>
			<a href="#" id="exitScreen" class="po-btns" style="display: none;"></a>
			
			<a href="#" class="po-btns po-prev-all" onclick="prevAllStep();"></a>
			<a href="#" class="po-btns po-prev" onclick="prevStep();"></a>
			<!-- <img alt="" src="images/arrays-up.png" onclick="prevStep();" style="margin-bottom: 20px;"> -->
			<div style="padding-bottom: 0.18rem;">
				<span class="progress" id="progress">1</span>/<span class="inte-total-num" id="inteTotalNum">1</span>
			</div>
			<a href="#" class="po-btns po-next" onclick="nextStep();"></a>
			<a href="#" class="po-btns po-next-all" onclick="nextAllStep();"></a>
			<!-- <img alt="" src="images/arrays-down.png" onclick="nextStep();"> -->
		</div>
		<div class="time-show"></div>
		<!-- <div class="goTop" onclick="goTop();" style="bottom: 60px;"></div> -->
		<script src="/js/global/jquery-1.12.4.min.js"></script>
		<script src="/common/plugin/iCheck/icheck.js"></script>
		<script src="/common/plugin/iCheck/js/custom.min.js"></script>
		<script>
			$(document).ready(function() {
				$('input').iCheck({
					checkboxClass: 'icheckbox_minimal-green',
					radioClass: 'iradio_minimal-grey',
					increaseArea: '20%' // optional
				});
			});
		</script>
		<!--[if lt IE 9]>
		<script src="http://localhost/student/js/global/PIE.js" type="text/javascript" charset="utf-8"></script>
		<script src="http://localhost/student/js/global/round.js" type="text/javascript" charset="utf-8"></script>
		<![endif]-->
		<script type="text/javascript" src="/js/global/dialog.js"></script>
		<script type="text/javascript" src="/js/global/common.js"></script>	
		<script type="text/javascript" src="/js/global/rem.js"></script>	
		<script type="text/javascript" src="/grammar-new/js/grammar-new-teach.js"></script>
	</body>

</html>