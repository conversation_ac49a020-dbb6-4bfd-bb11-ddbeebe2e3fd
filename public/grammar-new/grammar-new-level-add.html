<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>本章检测管理</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/global/style.css" rel="stylesheet">
    <link href="/css/global/ui-dialog.css" rel="stylesheet">
    <link href="/css/global/page.css" rel="stylesheet">
    <link href="/css/global/management-public.css" rel="stylesheet">
    <link href="/css/global/tipso.css" rel="stylesheet" type="text/css"/>
    <link href="/common/plugin/knowledgePointer/css/knowLedge-pointer.css" rel="stylesheet">
    <link href="/css/grammar-level-add.css" rel="stylesheet">
</head>
<body>
	<div class="bigbox-title">
		<font style="font-weight: bold; font-family: '微软雅黑'" id="courseName"></font>--添加本章检测题
		<button type="button" class="btn btn-primary btn-xs fr" style="margin-right: 13px;" onclick="goBack();">返回</button>
	</div>
	<div class="bigbox">
		<div class="select-div">
			<table class="layout-table">
				<tr id="sourceType">
					<td class="filter-name">题库类别：</td>
					<td class="filter-item">
						<label class="all-type"><input type="checkbox" value="" checked="checked"/>全部题库</label>
						<label class="item-type"><input type="checkbox" value="0"/>自建题库</label>
						<label class="item-type"><input type="checkbox" value="1"/>系统题库</label>
						<label class="item-type"><input type="checkbox" value="2"/>共享题库</label>
						<label class="item-type"><input type="checkbox" value="3"/>分校题库</label>
					</td>
				</tr>
				<tr id="gradePhase">
					<td class="filter-name">学段：</td>
					<td class="filter-item">
		    			<label><input type="radio" name="gradePhase" value="30" checked="checked">小升初</label>
		    			<label><input type="radio" name="gradePhase" value="40">初升高</label>
		    			<label><input type="radio" name="gradePhase" value="50">高考</label>
	    			</td>
				</tr>
				<tr id="grade">
					<td class="filter-name">年级：</td>
					<td class="filter-item">
						<label class="all-type"><input type="checkbox" value="" checked="checked"/>全部年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="31">一年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="32">二年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="33">三年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="34">四年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="35">五年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="36">六年级</label>
		    			<label class="item-type grade30"><input type="checkbox" value="30">小升初</label>
		    			<label class="item-type grade40 d-none"><input type="checkbox" value="41">七年级</label>
		    			<label class="item-type grade40 d-none"><input type="checkbox" value="42">八年级</label>
		    			<label class="item-type grade40 d-none"><input type="checkbox" value="43">九年级</label>
		    			<label class="item-type grade40 d-none"><input type="checkbox" value="40">初升高</label>
		    			<label class="item-type grade50 d-none"><input type="checkbox" value="51">高一</label>
		    			<label class="item-type grade50 d-none"><input type="checkbox" value="52">高二</label>
		    			<label class="item-type grade50 d-none"><input type="checkbox" value="53">高三</label>
		    			<label class="item-type grade50 d-none"><input type="checkbox" value="50">高考</label>
	    			</td>
				</tr>
				<tr id="knowledgePoint">
					<td class="filter-name">知识点：</td>
					<td class="filter-item">
						<span id="knowledgePointList"></span>
						<button type="button" class="btn btn-primary btn-xs" onclick="toSelectKnowledgePoint();">选择知识点</button>
	    			</td>
				</tr>
				<tr id="questionType">
					<td class="filter-name">题型：</td>
					<td class="filter-item">
						<label class="all-type"><input type="checkbox" value="" checked="checked"/>全部题型</label>
		    			<label class="item-type"><input type="checkbox" value="1">单选题</label>
		    			<label class="item-type"><input type="checkbox" value="2">多选题</label>
		    		<!-- 	<label class="item-type"><input type="checkbox" value="3">判断题</label> -->
		    			<label class="item-type"><input type="checkbox" value="4">填空题</label>
	    			</td>
    			</tr>
				<tr id="difficulty">
					<td class="filter-name">难度：</td>
					<td class="filter-item">
						<label class="all-type"><input type="checkbox" value="" checked="checked"/>全部难度</label>
		    			<label class="item-type"><input type="checkbox" value="1">易</label>
		    			<label class="item-type"><input type="checkbox" value="2">中</label>
		    			<label class="item-type"><input type="checkbox" value="3">难</label>
	    			</td>  
    			</tr>
			</table>
			<div>
				<button type="button" class="btn btn-primary btn-xs" onclick="getSimulationQuestionList();">筛选</button>
			</div>
		</div>
		<div class="question-div">
			<div class="table-responsive mar-top">
	            <table class="table table-bordered table-hover" style="margin-bottom: 10px;" id="dataTable">
	                <thead>
	                   <tr>
	                      <th style="width: 60px;">选择</th>
	                      <th style="width: 60px;">序号</th>
	                      <th style="min-width: 70px;">语法点</th>
	                      <th style="width: 70px;">题型</th>
	                      <th style="width: 70px;">难度</th>
	                      <th style="min-width: 200px;">题目内容</th>
	                      <th style="min-width: 50px;">答案</th> 
	                      <th style="min-width: 70px;" onclick="orderByQuery('createTime',this)">创建时间<span class="s-ico right" style="display: inline; position: relative;"><span sort="asc" class="shang ui-grid-ico-sort ui-icon-asc ui-sort-ltr glyphicon glyphicon-triangle-top ui-disabled none"></span><span sort="desc" class="xia ui-grid-ico-sort ui-icon-desc ui-sort-ltr glyphicon glyphicon-triangle-bottom"></span></span></th>
	                   </tr>
	                </thead>
	                <tbody id="dataTbody">
	                </tbody>
	            </table>
	       </div>
	       <div class="system-top" id="buttomDiv" style="width:300px;float:left;">
	            <ul style="margin-top:3px;">
	                <li style="margin-left:19px;">
	                    <label><input type="checkbox" class="selectAll" onclick="selectQuestionAll(this);">全选</label> 
	                </li>
	            </ul>
	        </div>
	       <div class="pageTest fr"></div>
	       <div class="totalCount fr">共<span id="haveNum" class="red">0</span>题，已选中<span id="addNum" class="red">0</span>题</div>
           <br style="clear:both;">
		</div>
		<div id="add-question-btn" class="d-none" style="text-align: center;">
            <button type="button" class="btn btn-primary" onclick="addCourseQuestion(true);">复制所有试题</button>
            <button type="button" class="btn btn-primary" onclick="addCourseQuestion(false);">复制选中试题</button>
		</div>
	</div>
</body>
<script type="text/javascript" src="/js/global/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="/js/global/dialog.js"></script>
<script type="text/javascript" src="/js/global/page.js"></script>
<script type="text/javascript" src="/js/global/tipso.js"></script>
<script type="text/javascript" src="/js/global/common.js"></script>
<script type="text/javascript" src="/js/simulation-common.js?v1.0.0"></script>
<script type="text/javascript" src="/common/plugin/knowledgePointer/js/jquery.nicescroll.min.js"></script>
<script type="text/javascript" src="/common/plugin/knowledgePointer/js/knowLedge-pointer.js"></script>
<script type="text/javascript" src="/js/common-knowledge.js"></script>
<script type="text/javascript" src="/grammar-new/js/grammar-new-level-add.js"></script>
</html>