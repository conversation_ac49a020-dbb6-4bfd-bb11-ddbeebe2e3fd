<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>本节检测试题</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/global/style.css" rel="stylesheet">
    <link href="/css/global/ui-dialog.css" rel="stylesheet">
    <link href="/css/global/tipso.css" rel="stylesheet" type="text/css"/>
    <link href="/css/global/page.css" rel="stylesheet" type="text/css"/>
    <link href="/css/global/management-public.css" rel="stylesheet">
    <link href="/css/grammar-unit-manager.css" rel="stylesheet">
    <link href="/grammar-new/css/grammar-new-train-manager.css" rel="stylesheet">
</head>
<body>
	<div class="bigbox-title">
		<font style="font-weight: bold; font-family: '微软雅黑'" id="courseName"></font>
		<button type="button" class="btn btn-primary btn-xs fr" style="margin-right: 13px;" onclick="goBack();">返回</button>
	</div>
	<div class="bigbox">
		<div class="system-top operation">
			<ul>
				<!-- <li style="float: right;">
					<button type="button" class="btn btn-primary btn-xs" onclick="toAddQuestions();">批量添加试题</button>
				</li> -->
				<li style="float: right;">
					<button type="button" class="btn btn-primary btn-xs" onclick="addQuestion();">添加试题</button>
				</li>
				<!-- <li style="float: right;">
					<button type="button" class="btn btn-primary btn-xs" onclick="toCopyUnitQuestion();">从优题库复制试题</button>
				</li>
				<li style="float: right;">
					<button id="apply-copy-btn" type="utton" class="btn btn-info btn-xs operation" onclick="getApplyQuestionList();">从应用中复制</button>
				</li> -->
			</ul>
		</div>
		<!-- <div class="knowledge-box">
			<div class="aside-title">选择知识点</div>
			<div style="height: 588px;">

				<ul class="cd-accordion-menu" id="knowledgeList">
				</ul>
			</div>
		</div> -->
		<div class="table-responsive mar-top">
			<table class="table table-bordered table-hover" id="dataTable">
				<thead>
					<tr>
						<th class="operation" width="60px">选择</th>
						<th width="60px">序号</th>
						<th width="60px">题型</th>
						<th>题目内容</th>
						<th>正确答案</th>
						<th>难度</th>
						<th>来源</th>
						<th class="operation" width="110px">操作</th>
					</tr>
				</thead>
				<tbody>
				</tbody>
			</table>
			<div class="system-top operation" style="width:300px;float:left; clear: both;">
	            <ul style="margin-top:3px;">
	                <li style="margin-left:19px;">
	                    <label><input type="checkbox" class="selectAll" onclick="selectAll(this);">全选</label>
	                </li>
	                <li>
	                    <button type="button" class="btn btn-primary btn-xs" onclick="deleteQuestion();">批量删除</button>
	                </li>
	                <br style="clear:both;">
	            </ul>
	        </div>
			<div class="pageTest fr"></div>
	       <div class="totalCount fr" style="line-height: 23px; margin-right: 10px;">共<span class="red">0</span>题</div>
       </div>
	</div>
	<!-- 查看试题列表弹窗 -->
	<div id="apply-question-div" class="table-responsive d-none">
		<table id="apply-question-table" class="table table-bordered table-hover">
			<thead>
				<tr>
					<th width="60px">序号</th>
					<th width="60px">题型</th>
					<th>题目内容</th>
					<th>正确答案</th>
					<th width="80px">是否复制</th>
					<th width="110px">操作</th>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</table>
	</div>
	<!-- 单选 -->
     <div id="single-choice" class="item d-none">
     	<div class="item-title">单选题</div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
     		<span class="left option-span">选项：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li><span>A.</span><input name="optionA" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-1" type="radio" value="A"/>是否是答案</label></li>
      			<li><span>B.</span><input name="optionB" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-1" type="radio" value="B"/>是否是答案</label></li>
      			<li><span>C.</span><input name="optionC" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-1" type="radio" value="C"/>是否是答案</label></li>
      			<li><span>D.</span><input name="optionD" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-1" type="radio" value="D"/>是否是答案</label></li>
      			<li><span>E.</span><input name="optionE" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-1" type="radio" value="E"/>是否是答案</label></li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 多选 -->
     <div id="multiple-choice" class="item d-none">
     	<div class="item-title">多选题</div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
   		<span class="left option-span">选项：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li><span>A.</span><input name="optionA" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-2" type="checkbox" value="A"/>是否是答案</label></li>
      			<li><span>B.</span><input name="optionB" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-2" type="checkbox" value="B"/>是否是答案</label></li>
      			<li><span>C.</span><input name="optionC" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-2" type="checkbox" value="C"/>是否是答案</label></li>
      			<li><span>D.</span><input name="optionD" class="input-option" type="text"/><label><input class="correct-option" name="correctOption-2" type="checkbox" value="D"/>是否是答案</label></li>
      			<li><span>E.</span><input name="optionE" class="input-option" type="text" /><label><input class="correct-option" name="correctOption-2" type="checkbox" value="E"/>是否是答案</label></li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 填空题 -->
     <div id="completion" class="item d-none">
     	<div class="item-title">填空题<span style="font-size: 13px;">（需要填空的位置请用三个下划线" ___ "标识出来，如果某个空有多个正确答案，答案之间用" | "符号隔开。）</span></div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
     		<span class="left option-span">答案：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="64"/></li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 选填 -->
     <div id="selection" class="item d-none">
     	<div class="item-title">选填题<span style="font-size: 13px;">（需要填空的位置请用三个下划线" ___ "标识出来，如果某个空有多个正确答案，答案之间用" | "符号隔开。）</span></div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
   		<span class="left option-span">答案：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li>
      				<span>1.</span>
      				<input name="optionA" class="input-item" type="text" maxlength="64"/>
      				<input name="optionB" class="input-item" type="text" maxlength="64"/>
      				<input name="optionC" class="input-item" type="text" maxlength="64"/>
      				<input name="optionD" class="input-item" type="text" maxlength="64"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>2.</span>
      				<input name="optionA" class="input-item" type="text" maxlength="64"/>
      				<input name="optionB" class="input-item" type="text" maxlength="64"/>
      				<input name="optionC" class="input-item" type="text" maxlength="64"/>
      				<input name="optionD" class="input-item" type="text" maxlength="64"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>3.</span>
      				<input name="optionA" class="input-item" type="text" maxlength="64"/>
      				<input name="optionB" class="input-item" type="text" maxlength="64"/>
      				<input name="optionC" class="input-item" type="text" maxlength="64"/>
      				<input name="optionD" class="input-item" type="text" maxlength="64"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>4.</span>
      				<input name="optionA" class="input-item" type="text" maxlength="64"/>
      				<input name="optionB" class="input-item" type="text" maxlength="64"/>
      				<input name="optionC" class="input-item" type="text" maxlength="64"/>
      				<input name="optionD" class="input-item" type="text" maxlength="64"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>5.</span>
      				<input name="optionA" class="input-item" type="text" maxlength="64"/>
      				<input name="optionB" class="input-item" type="text" maxlength="64"/>
      				<input name="optionC" class="input-item" type="text" maxlength="64"/>
      				<input name="optionD" class="input-item" type="text" maxlength="64"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 综合题 -->
     <div id="synthesis" class="item d-none">
     	<div class="item-title">综合题<span style="font-size: 13px;">（注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								提供多个备选项，并指定正确选择时，该空判为选填题；只提供一个备选项时，该空判为填空题，备选项即为正确答案！）</span></div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
   		<span class="left option-span">答案：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li>
      				<span>1.</span>
      				<input name="optionA" class="input-item" type="text" />
      				<input name="optionB" class="input-item" type="text" />
      				<input name="optionC" class="input-item" type="text" />
      				<input name="optionD" class="input-item" type="text" />
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>2.</span>
      				<input name="optionA" class="input-item" type="text" />
      				<input name="optionB" class="input-item" type="text" />
      				<input name="optionC" class="input-item" type="text" />
      				<input name="optionD" class="input-item" type="text" />
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>3.</span>
      				<input name="optionA" class="input-item" type="text"/>
      				<input name="optionB" class="input-item" type="text"/>
      				<input name="optionC" class="input-item" type="text"/>
      				<input name="optionD" class="input-item" type="text"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>4.</span>
      				<input name="optionA" class="input-item" type="text"/>
      				<input name="optionB" class="input-item" type="text"/>
      				<input name="optionC" class="input-item" type="text"/>
      				<input name="optionD" class="input-item" type="text"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      			<li>
      				<span>5.</span>
      				<input name="optionA" class="input-item" type="text"/>
      				<input name="optionB" class="input-item" type="text"/>
      				<input name="optionC" class="input-item" type="text"/>
      				<input name="optionD" class="input-item" type="text"/>
      				<span>正确答案：</span>
      				<select class="select-item">
      					<option value="0">1</option>
      					<option value="1">2</option>
      					<option value="2">3</option>
      					<option value="3">4</option>
      				</select>
  				</li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 点填题 -->
     <div id="click-completion" class="item d-none">
     	<div class="item-title">点填题<span style="font-size: 13px;">（注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								该题型类似于填空题，只是答案从题干中点击选出。）</span></div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
     		<span class="left option-span">答案：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="64"/></li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
     <!-- 点选题 -->
     <div id="click-select" class="item d-none">
     	<div class="item-title">点选题<span style="font-size: 13px;">（注：题干中需要设空的部分请用三个或三个以上的连续下划线“___”标出；最多支持5个空。<br>
								该题是将正确答案随机打乱，让学生点击填入。编辑时提供的答案顺序即为正确顺序。）</span></div>
     	<div><span class="question-span">题干：</span><textarea class="input-question" name="question"></textarea></div>
     	<div style="margin-bottom: 0px;">
     		<span class="left option-span">答案：</span>
      	<div class="left">
      		<ul type="none" class="option-ul">
      			<li><span>1.</span><input name="optionA" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>2.</span><input name="optionB" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>3.</span><input name="optionC" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>4.</span><input name="optionD" class="input-option" type="text" maxlength="64"/></li>
      			<li><span>5.</span><input name="optionE" class="input-option" type="text" maxlength="64"/></li>
      		</ul>
      	</div>
      	<br style="clear: both;" />
     	</div>
     </div>
		 <div id="tl" class="item d-none">
			<div class="item-title">提炼</div>
     	<div><span class="question-span">内容：</span><textarea class="input-question" name="question"></textarea></div>
		 </div>
		 <!-- 新增 -->
		 <div id="add"  class="d-none">
			<div class="online" style="display: flex;align-items: center;">
				<span class="parse-span">排序：</span>
				<div id="sort1" class="py"><input name="sort" class="input-option-read1" type="number" /></div>
			</div>
			<div class="online" style="display: flex;align-items: center;">
				<span class="parse-span">知识点标题：</span>
				<div id="title1" class="py"><input name="title" class="input-option-read" type="text" /></div>
			</div>
			<div class=" online" style="display: flex;align-items: center;">
				<span class="parse-span">知识点描述/题干内容描述/长难句：</span>
				<div id="titleDescribe1" class="py"><textarea name="titleDescribe" class="input-option-read" type="text" ></textarea></div>
			</div>
		 </div>
     <!-- 公用部分 -->
     <div id="common" class="d-none">
     	<div class="parseDiv">
     		<span class="parse-span">解析：</span>
     		<textarea class="parse" name="parse"></textarea>
     	</div>
     	<div class="fl difficultyDiv">
     		<span class="difficulty-span">难度：</span>
     		<select class="difficulty" style="width: 70px;">
     			<option value="1" selected="selected">易</option>
     			<option value="2">中</option>
     			<option value="3">难</option>
     		</select>
     	</div>
     	<div class="fl sourceDiv">
     		<span class="source-span">来源：</span>
     		<select class="source" style="width: 70px;">
     			<option value="1" selected="selected">自编</option>
     			<option value="2">模拟</option>
     			<option value="3">小升初</option>
     			<option value="4">初升高</option>
     			<option value="5">高考</option>
     			<option value="6">应用</option>
     		</select>
     	</div>
     	<div class="fl yearDiv">
     		<span class="year-span">年份：</span>
     		<select class="year" style="width: 70px;">
     		</select>
     	</div>
     	<div class="fl areaDiv">
     		<span class="area-span">地区：</span>
     		<select class="area" style="width: 70px;">
     			<option value="0" selected="selected">全国</option>
     		</select>
     	</div>
     	<!-- <div class="knowDiv" style="clear: both; padding-top: 5px;">
     		<span class="fl difficulty-span">知识点：</span>
     		<div class="fl" style="position: relative;">
     			<div class="knowledge-list"></div>
	     		<button class="know-btn">关联知识点</button>
	     		<div class="knowDia">
	     			<span class="close">×</span>
	     		</div>
     		</div>
     	</div> -->
     </div>
     <!-- 批量添加试题弹窗 -->
     <div id="uploadQuestionsDiv" class="d-none">
     	<div class="upload-tips">提示：编辑试题时请严格依照模板样式</div>
     	<div class="system-top">
			<ul>
				<li style="height: 20px; line-height: 20px;">
					<button type="button" class="btn btn-primary btn-xs"  onclick="downloadExcelTemplate();">下载模板</button>
				</li>
				<li style="height: 20px; line-height: 20px;">
					<form class="form" enctype="multipart/form-data">
						<input type="file" name="file" accept=".xls,.xlsx"/>
						<input type="hidden" name="unitId" value="0"/>
					</form>
				</li>
				<li style="height: 20px; line-height: 20px;">
					<button type="button" class="btn btn-primary btn-xs"  onclick="uploadExcel();">导入试题</button>
				</li>
			</ul>
		</div>
     </div>
</body>

<script type="text/javascript" src="/js/global/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="/js/global/dialog.js"></script>
<script type="text/javascript" src="/js/global/jquery-form.js"></script>
<script type="text/javascript" src="/js/global/common.js"></script>
<script type="text/javascript" src="/js/global/tipso.js"></script>
<script type="text/javascript" src="/js/global/page.js"></script>
<script type="text/javascript" src="/js/global/user-access.js"></script>
<script type="text/javascript" src="/grammar-new/js/read-new-train-manager.js"></script>
</html>
