.icheckbox_minimal-grey,
.iradio_minimal-grey {
	background-size: 14px;
}

.page-head-wrap {
	height: 40px;
	background-color: #00B48B;
	margin-bottom: 15px;
}

.page-head-common,
.page-logo,
.page-head-right a {
	line-height: 40px;
	color: #FFF;
}

.page-logo {
	font-size: 16px;
}

.page-logo strong {
	font-weight: bold;
}

.page-head-right a {
	font-size: 14px;
	padding: 0 14px;
	cursor: pointer;
	float: left;
}

.page-head-right a:hover {
	background-color: #04a179;
}

.sc-title {
	height: 19px;
	line-height: 19px;
	font-size: 18px;
	font-weight: bold;
	color: #333333;
	margin: 25px 7px 0 20px;
}

.water-drop {
	width: 20px;
	height: 25px;
}

.water-drop::before {
	width: 17px;
	height: 22px;
	background-repeat: no-repeat;
	background-size: contain;
	position: absolute;
	content: " ";
	left: 0;
	top: 0;
	z-index: 1;
}

.water-drop-learning,
.water-drop-pass,
.water-drop-fail,
.water-drop-warning {
	cursor: pointer;
}

.water-drop-null::before {
	background-image: url(../images/default/water-drop-null.png);
}

.water-drop-learning::before {
	background-image: url(../images/default/water-drop-learning.png);
}

.water-drop-pass::before {
	background-image: url(../images/default/water-drop-pass.png);
}

.water-drop-fail::before {
	background-image: url(../images/default/water-drop-fail.png);
}

.water-drop-warning::before {
	background-image: url(../images/default/water-drop-warning.png);
}

body {
	background-color: #DAEDED;
}

.container {
	width: 1000px;
	margin: 0 auto;
}

.common-question {
	height: 16px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 16px;
	position: relative;
}

.common-options {
	height: 16px;
	font-size: 16px;
	font-family: Arial;
	font-weight: 400;
	color: #333333;
	line-height: 16px;
}

.common-tips {
	height: 14px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #999999;
	line-height: 14px;
}

.common-question-title {
	height: 20px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #333333;
	line-height: 20px;
}

.knowledge-map li {
	position: relative;
	overflow: hidden;
}

.knowledge-map .knowledge-item::before,
.knowledge-map .knowledge-item-sm ul>li::before {
	display: block;
	content: " ";
	position: absolute;
	width: 1px;
	height: 10000px;
	background: #00b48b;
}

.knowledge-map .knowledge-item::before {
	top: 5px;
	left: 6px;
}

.knowledge-map .knowledge-item-sm ul>li::before {
	top: 0;
	left: 6px;
}

.knowledge-map .no-chidlren::before,
.knowledge-map .no-chidlren ul>li::before {
	height: 0;
}

.knowledge-item>.hitarea {
	background: none !important;
}

.knowledge-item-sm {
	padding: 0 0 0 25px;
	position: relative;
}

.knowledge-item-sm::before {
	display: block;
	content: " ";
	position: absolute;
	width: 20px;
	height: 1px;
	background: #00b48b;
	top: 21px;
	left: 7px;
}

.knowledge-item-sm:last-of-type::after {
	display: block;
	content: " ";
	position: absolute;
	width: 20px;
	height: 6px;
	background: #fff;
	bottom: 0;
	left: 0;
}

.knowledge-item-xs {
	padding: 13px 0 0 27px;
}

.knowledge-item-xs>span {
	float: left;
	width: 15px;
	height: 15px;
	background: #30a28d;
	border-radius: 50%;
	font-size: 10px;
	font-weight: 400;
	color: white;
	line-height: 15px;
	text-align: center;
	margin-right: 6px;
	position: relative;
}

.knowledge-item-xs>span::before {
	display: block;
	content: " ";
	position: absolute;
	width: 20px;
	height: 1px;
	background: #00b48b;
	top: 7px;
	left: -20px;
}

.knowledge-item-xs>strong {
	float: left;
	font-size: 12px;
	font-weight: 400;
	line-height: 15px;
	width: 195px;
}

.knowledge-item-xs:last-of-type>span::after {
	display: block;
	content: " ";
	position: absolute;
	width: 20px;
	height: 20000px;
	background: #fff;
	top: 8px;
	left: -27px;
}

.knowledge-title {
	padding: 0 0 0 24px;
	background: url(../images/default/knowledge-map-title.png) no-repeat 0 center;
	height: 15px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #333333;
	line-height: 15px;
	z-index: 10;
	position: relative;
	top: -2px;
	white-space: normal;
	display: inline-block;
}

.knowledge-title-sm {
	height: 14px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 14px;
	background: url(../images/default/knowledge-map-title-sm.png) no-repeat 0 14px;
	position: relative;
	padding: 14px 0 0 22px;
}

.knowledge-item-sm.knowledge-not-learn>.knowledge-title-sm {
	color: #999;
	background: url(../images/default/knowledge-map-title-sm-grey.png) no-repeat 0 14px;
}

.knowledge-item.knowledge-not-learn>.knowledge-title {
	color: #999;
}

.knowledge-item-xs>span,
.knowledge-item-xs>strong {
	color: #333;
	font-weight: normal !important;
}

.knowledge-item-xs.knowledge-not-learn>span,
.knowledge-item-xs.knowledge-not-learn>strong {
	color: #999;
}

.treeview .no-chidlren {
	background: transparent;
}

strong {
	font-weight: bold !important;
}

u {
	text-decoration: underline !important;
}

.bottom-div {
	text-align: right;
	height: 60px;
}

/* .option li {
    width: 140px;
    float: left;
} */
.none {
	display: none;
}

.tips>div {
	float: left;
}

.parse>div {
	float: left;
}

.question-select {
	position: relative;
	top: 7px;
	background-color: #fff;
	border: 1px solid #ccc;
	display: inline-block;
	font-size: 16px;
	margin: 0 5px;
}

.question-select:hover {
	border: 1px solid #008c72;
}

.question-select>h3 {
	width: 80px;
	padding-left: 10px;
	color: #333;
	float: left;
	line-height: 24px;
	background: url(../images/default/il-arrow.png) no-repeat 70px center;
	cursor: pointer;
	font-size: 16px;
}

.question-select>ul {
	display: none;
	position: absolute;
	top: 25px;
	left: -1px;
	background-color: #fff;
	z-index: 1;
}

.question-select li {
	width: 124px;
	border: 1px solid #666;
	border-top: 0;
	padding-left: 10px;
	width: 80px;
	line-height: 24px;
	font-size: 16px;
	cursor: pointer;
	color: #333;
}

.question-select li:hover {
	background-color: #eee;
}

.border {
	border: 1px solid #ccc;
}

.icheckbox_minimal-green {
	margin: 5px 0 0 0px !important;
}

.tips-parse {
	margin-left: 0;
	color: #999;
	font-size: 20px;
	line-height: 30px;
}

.tips-parse-text {
	font-size: 20px;
	color: #999;
	line-height: 30px;
	word-break: break-all;
	max-width: 798px;
}

.inte-learn-sm-nalysis {
	padding-left: 16px;
	margin-bottom: 5px;
}

.top-box-fixed {
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 9;
}

.progress-box {
	z-index: 10;
	position: relative;
	text-align: center;
	top: -15px;
	color: #c3c2c2;
	padding-right: 80px;
}

.progress-list {
	display: inline-block;
}

.progress-list>span {
	width: 20px;
	height: 20px;
	background-color: #e6e6e6;
	margin-right: 3px;
	display: inline-block;
}

.learned {
	background-color: #66cc99 !important;
}

.learned:hover {
	cursor: pointer;
}

.cut-line-div {
	height: 10px;
	background-color: #f5f5f5;
}

.inte-learn-progress-box {
	position: relative;
	top: 14px;
}

.question {
	word-break: break-word;
}

/* .question>div,.question>p {
	display: inline-block;
} */

.module-last-box {
	padding-bottom: 20px;
}

.last-box {
	padding-bottom: 40px;
}

.container .s-more-up {
	position: absolute;
	bottom: 0;
	left: 50%;
	z-index: 5;
	margin-left: -45px;
	padding-top: 0;
}

.s-more-bar {
	width: 90px;
	height: 45px;
	padding-top: 20px;
	margin: 0 auto;
	color: #999;
	display: none;
}

.s-more-up .rect {
	cursor: pointer;
	position: absolute;
	bottom: 0;
	/* _bottom: -2px; */
	left: 50%;
	margin-left: -28px;
	height: 32px;
	width: 57px;
	font-size: 0;
	vertical-align: bottom;
	background: url(../images/default/more_up_bg_83c39341.png) no-repeat;
	/* _background: url(../images/default/more_up_bg_ie6_8b0a319c.png) no-repeat; */
}

.s-more-up .rect .animate-arrow {
	position: relative;
	display: block;
	height: 13px;
	width: 22px;
	margin: 0 auto 0 auto;
	top: 10px;
	background: url(../images/default/card_setts_fdb17359.png) no-repeat -114px -2px;
	/* _background: url(../images/default/card_setts_ie_57e87032.png) no-repeat -114px -2px; */
	font-size: 0;
}

.green {
	color: green;
}

.red {
	color: red;
}

.s-disable {
	background-color: #e3e3e3;
}

.s-disable:hover {
	border-color: #ccc;
}

.question input {
	height: 24px;
	line-height: 24px;
	margin: 0 5px;
	color: #666;
	font-size: 18px;
	padding: 0 8px 0px 8px;
}

.question .input-error {
	border-color: #ffa442;
	box-shadow: #ffa442 0px 0px 5px;
}

.over-tips {
	color: #666;
	font-size: 16px;
	text-align: center;
	padding: 30px 0;
}

.inte-learn-submit {
	line-height: 32px;
}

.inte-learn-help {
	line-height: 32px;
	display: none;
}

.inte-learn-radio>li>label {
	display: inline-block !important;
	color: #666;
	cursor: pointer;
}

/* .hover:HOVER{
	color: #666;
	opacity: 1;
} */
.progress-outer {
	background-color: #e6e6e6;
	height: 14px;
	width: 600px;
	border-radius: 7px;
	display: inline-block;
	margin: 0 10px;
	line-height: 14px;
}

.progress-inner {
	background-color: #66cc99;
	border-radius: 7px;
	height: 14px;
	display: inline-block;
}

.progress-inner>div {
	text-align: right;
	font-size: 10px;
	color: #fff;
	padding-right: 7px;
	line-height: 14px;
}

.progress {
	color: #66cc99;
	font-size: 24px;
	margin-right: 2px;
	position: relative;
	top: 4px;
}

.inte-total-num {
	font-size: 18px;
	margin-left: 2px;
	position: relative;
	top: 2px;
}

.title-list-div {
	padding: 20px 20px 13px 20px;
	background-color: #fff;
	border-radius: 8px;
	position: fixed;
	left: 60px;
	top: 42%;
	max-width: 260px;
	display: none;
}

.title1-list {
	color: #ccc;
	font-size: 16px;
	width: 270px;
	max-height: 400px;
	overflow: auto;
}

.title1-list>li {
	margin-bottom: 10px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.title1-list>li>span {
	display: inline-block;
	width: 12px;
	height: 11px;
	background: url(../images/default/knowlege-system-circle-pray.png) no-repeat left center;
}

.title1-list .water-drop {
	position: absolute;
	right: 6px;
	top: 8px;
}

.title1-list .knowledge-item>.water-drop {
	top: 0;
}

.title1-list .water-drop-active {
	top: 4px;
}

.title2-list {
	font-size: 14px;
	padding-left: 20px;
}

.title2-list>li {
	margin-top: 5px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.title3-list {
	font-size: 12px;
	padding-left: 20px;
}

.title3-list>li {
	margin-top: 3px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.learned-title>span {
	background: url(../images/default/knowlege-system-circle-light.png) no-repeat left center !important;
}

.learned-title>label {
	color: #666;
	cursor: pointer;
}

.learned-title>label:hover {
	color: #008c72;
}

.question>ul,
.question>ol {
	padding-left: 20px !important;
}

.question>ul>li {
	list-style: disc !important;
}

.question>ol>li {
	list-style: decimal !important;
}

.pace-last-box {
	border-bottom: 1px solid #fff;
}

.border-red {
	border-color: red !important;
}

.order-div {
	display: none;
}

.order-num {
	background: url(../images/default/order-circle.png) no-repeat center;
	width: 31px;
	height: 31px;
	color: #fff;
	line-height: 31px;
	text-align: center;
	font-size: 20px;
	position: absolute;
	left: -31px;
	z-index: 5;
}

.report-div {
	display: none;
}

.report-item {
	background-color: #169bd5;
	width: 40px;
	height: 31px;
	color: #fff;
	line-height: 31px;
	text-align: center;
	font-size: 20px;
	position: absolute;
	right: -40px;
	font-size: 14px;
	cursor: pointer;
	border-radius: 5px;
	z-index: 5;
}

.report-content>div {
	margin: 2px 0;
}

.report-content label {
	width: 70px;
	text-align: right;
	display: inline-block;
}

.error-description {
	width: 413px;
	height: 70px;
	border: 1px solid #ccc;
	padding: 5px;
}

.phone {
	border: 1px solid #aaa;
	padding: 0 5px;
}

.input-line {
	border: none;
	border-bottom: 1px solid #aaa;
	background-color: #fff;
}

.dian-tian-box {
	-webkit-touch-callout: text;
	-webkit-user-select: text;
	-khtml-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
	user-select: text;
}

.select-input {
	border-color: #66cc99;
}

.answer-item-div {
	display: inline-block;
	color: #666;
	font-size: 18px !important;
}

.answer-ul {
	display: inline-block;
	margin-top: 10px;
}

.answer-ul>li {
	display: inline-block;
	padding: 0 10px;
	border: 1px solid #999;
	color: #666;
	margin-right: 10px;
	cursor: pointer;
	font-size: 18px !important;
}

.answer-ul>li:hover {
	border-color: #66cc99;
	color: #66cc99;
}

.span-line {
	min-width: 80px;
	height: 28px;
	display: inline-block;
	border-bottom: 1px solid #aaa;
	overflow: hidden;
	cursor: pointer;
	position: relative;
	top: 5px;
}

.v-none {
	visibility: hidden !important;
}

.goTop {
	position: fixed;
	bottom: 100px;
	right: 16px;
	width: 48px;
	height: 48px;
	cursor: pointer;
	background: url(../images/global/goTop.png) no-repeat;
	z-index: 9;
}

.goTop:hover {
	background: url(../images/global/goTop-hover.png) no-repeat;
}

.grammar-btn {
	background-color: #1ab394;
	border-color: #1ab394;
	color: #ffffff;
	border-radius: 3px;
	padding: 1px 5px;
}

.grammar-btn:hover {
	background-color: #18a689;
	border-color: #18a689;
}

.help-num-title {
	right: 94px;
	top: 0;
	line-height: 40px;
	font-size: 15px;
}

.help-num-title>span {
	color: #ffff00;
}

.seekHelpDia .help-text {
	height: 100px;
	font-size: 17px;
	text-indent: 2em;
}

.seekHelpDia .help-tips {
	text-align: right;
}

.contentAnswerDia {
	font-size: 16px;
	min-height: 150px;
}

.contentAnswerDia .dia-answer-ul {
	margin-left: 40px;
}

.contentAnswerDia .dia-answer-ul>li {
	list-style: decimal;
}

.help-num-title {
	margin: 0 19px 0 0;
	height: 14px;
	font-size: 14px;
	font-weight: 400;
	color: white;
	line-height: 40px;
}

.help-num-title span {
	color: #ffa442;
}

.common-question {
	display: inline-block;
	vertical-align: top;
}

.sm-header-title-goback a {
	border-radius: 12px;
	color: #6accb7;
	background-color: #00855f;
	font-size: 14px;
	height: 24px;
	line-height: 24px;
	padding: 0 12px;
	font-weight: normal;
	display: block;
	margin-top: 7px;
}

.show-type {
	z-index: 1002;
	position: absolute;
	right: 25px;
	top: 18px;
}

.show-type span {
	height: 14px;
	font-size: 14px;
	font-weight: 400;
	line-height: 14px;
	margin: 7px 0 0 0;
}

.show-type div {
	border-radius: 13px;
	color: transparent;
	background-color: #00b48b;
	font-size: 0px;
	height: 26px;
	line-height: 26px;
	padding: 0;
	font-weight: normal;
	width: 54px;
	position: relative;
	cursor: pointer;
}

.show-type div i {
	position: absolute;
	transition: all 0.3s ease;
}

.show-type div .circle {
	width: 22px;
	height: 22px;
	background: white;
	border-radius: 50%;
	left: 2px;
	top: 2px;
}

.show-type div .card-icon,
.show-type div .screen-icon {
	width: 14px;
	height: 11px;
	top: 7px;
	background-repeat: no-repeat;
	background-position: center;
}

.show-type div .card-icon {
	background-image: url(../images/default/il-open-icon.png);
	left: 6px;
}

.show-type div .screen-icon {
	/* background-image: url(../images/default/il-close-icon.png); */
	background-image: url(../images/default/screen-white.png);
	right: 5px;
	/* opacity: 0; */
}

.show-type .screen .screen-icon {
	background-image: url(../images/default/screen-green.png);
}

.show-type .screen .card-icon {
	background-image: url(../images/default/card-white.png);
}

.show-type .card .circle {
	left: 2px;
}

.show-type .card .card-icon {
	opacity: 1;
}

.show-type .card .screen-icon {
	/* opacity: 0; */
}

.show-type .screen {
	/* background-color: #6ed5be; */
}

.show-type .screen .circle {
	left: 31px;
}

.show-type .screen .card-icon {
	/* opacity: 0; */
}

.show-type .screen .screen-icon {
	opacity: 1;
}

.main-top {
	padding-top: 15px;
	width: 1000px;
	margin: 0 auto;
}

.main-top .left p,
.main-top .left ul,
.main-top .left a {
	float: left;
}

.main-top .left p {
	height: 16px;
	font-size: 16px;
	font-weight: 400;
	line-height: 16px;
	margin: 5px 0 0 0;
	color: #666;
}

.main-top .left .number {
	font-size: 18px;
	margin-right: 20px;
}

.main-top .left a {
	height: 14px;
	font-size: 14px;
	font-weight: 400;
	color: #00b48b;
	line-height: 14px;
	margin: 6px 14px 0 0;
}

.main-top .left a:hover {
	color: #008c72;
}

.main-top li {
	float: left;
	margin-right: 8px;
	position: relative;
}

.main-content {
	padding-bottom: 30px;
	background: #fff;
	box-shadow: 0px 0px 4px 0px rgba(18, 214, 189, 0.2);
	border-radius: 8px;
}

#card-box,
#fullScreen_mode {
	padding-top: 100px;
}

.main-content .top {
	height: 64px;
}

.main-content .top h2 {
	height: 15px;
	font-size: 16px;
	font-weight: bold;
	color: #333;
	line-height: 15px;
	margin: 24px 0 0 15px;
}

.main-content .top ul {
	margin: 7px -16px 0 0;
}

.main-content .top .did {
	cursor: pointer;
}

.main-content .top li {
	float: left;
	width: 58px;
	margin-left: 4px;
	position: relative;
}

.main-content .top li::after {
	display: block;
	content: " ";
	position: absolute;
	width: 36px;
	height: 2px;
	background: #bbb;
	left: 29px;
	top: 16px;
}

.main-content .top li:nth-of-type(4)::after {
	width: 0;
}

.main-content .top .did::after {
	background: #a55a17;
}

.main-content .top .active::after {
	background: #bbb;
}

.main-content-icon {
	display: block;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	background-size: contain;
	background-repeat: no-repeat;
}

.main-content-icon-box {
	width: 32px;
	height: 32px;
	position: relative;
	border-radius: 50%;
	z-index: 1;
}

.main-content-icon-inner {
	width: 26px;
	height: 26px;
	background: #bbbbbb;
	border-radius: 50%;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.main-content .top li:nth-of-type(1) i {
	width: 18px;
	height: 12px;
	background-image: url(../images/default/il-guanchadian.png);
}

.main-content .top li:nth-of-type(2) i {
	width: 20px;
	height: 14px;
	background-image: url(../images/default/il-zhishi-pointer.png);
}

.main-content .top li:nth-of-type(3) i {
	width: 16px;
	height: 16px;
	background-image: url(../images/default/il-gonggu.png);
}

.main-content .top li:nth-of-type(4) {
	margin-right: 0;
}

.main-content .top li:nth-of-type(4) i {
	width: 14px;
	height: 14px;
	background-image: url(../images/default/il-lianxi.png);
}

.main-content-text {
	display: block;
	height: 13px;
	font-size: 12px;
	font-weight: 400;
	color: #bbbbbb;
	line-height: 13px;
	margin-top: 2px;
}

.main-content .did .main-content-icon-inner {
	background-color: #a55a17;
}

.main-content .did .main-content-text {
	color: #a55a17;
}

.main-content .active .main-content-icon-box {
	background-color: #d1b6a0;
}

.main-content li .main-content-text {
	margin-left: 4px;
}

.main-content .tab {
	width: 940px;
	height: 512px;
	background: white;
	border: 1px solid #cccccc;
	border-radius: 8px;
	margin-left: 30px;
	display: none;
}

.main-content .tab-title {
	height: 42px;
	border: 1px solid #cccccc;
	border-radius: 0px 22px 22px 0px;
	margin-top: 27px;
	margin-left: -1px;
	float: left;
}

.main-content .tab-title img,
.main-content .tab-title span {
	float: left;
}

.main-content .tab-title-guancha {
	width: 98px;
}

.main-content .tab-title-guancha img {
	width: 24px;
	margin: 13px 8px 0 17px;
}

.main-content .tab-title-zhishi {
	width: 118px;
}

.main-content .tab-title-zhishi img {
	width: 24px;
	margin: 13px 8px 0 17px;
}

.main-content .tab-title-gonggu {
	width: 98px;
}

.main-content .tab-title-gonggu img {
	width: 22px;
	margin: 12px 8px 0 20px;
}

.main-content .tab-title-lianxi {
	width: 98px;
}

.main-content .tab-title-lianxi img {
	width: 22px;
	margin: 11px 8px 0 20px;
}

.main-content .tab-title span {
	height: 17px;
	font-size: 16px;
	font-weight: bold;
	line-height: 17px;
	margin-top: 13px;
}

.main-content .tab-content {
	padding-top: 15px;
	height: 420px;
	position: relative;
	clear: both;
}

.main-content .tab-content .card-item {
	height: 380px;
	overflow-y: auto;
}

.main-content .tab-content .common-question {
	margin: 5px 0 20px;
}

.main-content .tab-content ul {
	margin-bottom: 50px;
}

.main-content .tab-content .common-options {
	padding-left: 27px;
	background: url(../images/default/il-radio.png) no-repeat 0 center;
	margin-bottom: 20px;
	cursor: pointer;
}

.main-content .tab-content .common-options.checked {
	background: url(../images/default/il-radio-checked.png) no-repeat 0 center;
}

.main-content .tab-content .btn-box {
	margin-top: 10px;
}

.main-content .tab-content .btn {
	border-radius: 15px;
	color: #fff;
	background-color: #ffa442;
	font-size: 16px;
	width: 120px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	font-weight: normal;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
}

.main-content .tab-content .lianxi-number span {
	float: left;
	width: 20px;
	height: 20px;
	border: 2px solid #999999;
	border-radius: 50%;
	text-align: center;
	line-height: 20px;
	font-size: 16px;
	font-weight: 400;
	color: #999;
	margin-right: 14px;
}

.main-content .tab-content .lianxi-number .did {
	background: #7fd9c5;
	border: 2px solid #00b48b;
	color: #fff;
}

.look-words-box .words {
	height: 17px;
	font-size: 16px;
	font-weight: bold;
	line-height: 17px;
	margin-right: 40px;
}

.look-words-box .words i {
	color: #ffa442;
}

.verification-icon {
	position: absolute;
	top: -10px;
	right: -54px;
	width: 34px;
	height: 34px;
}

.pass .verification-icon {
	background: url(../images/default/verification-pass.png) no-repeat;
	background-size: contain;
}

.fail .verification-icon {
	background: url(../images/default/verification-fail.png) no-repeat;
	background-size: contain;
}

.knowlege-system-box {
	position: fixed;
	left: 10px;
	top: 40%;
	width: 100px;
	top: 271px;
}

.knowlege-system {
	display: inline-block;
	background-color: #fff;
	border-radius: 0 4px 4px 0;
	text-align: center;
	border: 2px solid #bbb;
	border-radius: 10px;
	overflow: hidden;
}

.knowlege-icon {
	background: url(../images/default/knowlege-system-pray.png) no-repeat center;
	height: 22px;
	width: 22px;
	display: inline-block;
	margin-top: 10px;
}

.knowlege-text {
	margin: 0 10px 10px;
	font: 16px "Microsoft YaHei";
	width: 32px;
	color: #333;
	text-align: center;
}

.k-active>.knowlege-icon {
	background: url(../images/default/knowlege-system-light.png) no-repeat center;
}

.k-active>.knowlege-text {
	color: #008c72;
}

.title-list-div {
	width: 306px;
	background: white;
	border: 2px solid #999999;
	border-radius: 10px;
	position: absolute;
	left: 60px;
	padding: 20px;
	display: none;
	top: 50%;
	transform: translateY(-50%);
}

.knowledge-title {
	padding-right: 50px;
}

.help-page {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background: rgba(0, 0, 0, 0.6);
	z-index: 1000;
}

.help-page .tip {
	float: right;
	width: 230px;
	height: 67px;
	padding-top: 139px;
	margin-top: 62px;
	margin-right: -12px;
	background: url(../images/default/il-help-arrow.png) no-repeat;
}

.help-page .tip p {
	font-size: 12px;
	font-weight: 400;
	color: white;
	position: relative;
	top: -4px;
}

.help-page .tip p:first-of-type {
	padding-left: 106px;
}

.help-page .tip p:last-of-type {
	padding-left: 84px;
}

.help-page .tip button {
	cursor: pointer;
	margin-left: 108px;
	position: relative;
	top: -4px;
	border-radius: 9px;
	color: #fff;
	background-color: #00b48b;
	font-size: 12px;
	height: 18px;
	line-height: 18px;
	padding: 0 12px;
	font-weight: normal;
}

.help-page .tip button:hover {
	background-color: #008c72;
}

.help-page .bg {
	width: 143px;
	height: 40px;
	background: #daeded;
	position: absolute;
	top: 47px;
	right: -10px;
}

.help-btn {
	left: 60% !important;
}

.top-progress {
	width: 1028px;
	margin-top: 40px;
	position: relative;
}

.c-index-list {
	margin-top: 38px;
	margin-left: 50px;
	float: left;
}

.c-index-list>li {
	display: inline-block;
	margin-right: 14px;
	cursor: pointer;
	width: 20px;
	height: 20px;
	line-height: 20px;
	text-align: center;
	border: #999 solid 2px;
	border-radius: 50%;
	font-size: 14px;
	color: #999;
}

.c-index-list>li.active {
	border-color: #00b48b;
	color: #fff;
	background-color: #7fd9c5;
}

.water-drop-active::after {
	display: block;
	content: " ";
	width: 27px;
	height: 30px;
	position: absolute;
	left: -5px;
	top: -5px;
	background-size: contain;
	background: url(../images/default/water-drop-active.png) no-repeat center;
	background-size: contain;
}

.il-popup {
	position: absolute;
	left: 60px;
	z-index: 1000;
}

.il-popup .inner {
	background: rgba(238, 255, 251, 1);
	border: 4px solid rgba(64, 188, 146, 1);
	border-radius: 20px;
	position: relative;
}

/* .il-popup .inner .title {
	background-repeat: no-repeat;
	background-position: center;
	position: absolute;
	left: 50%;
	top: -33px;
	transform: translate(-50%);
}

.il-popup .inner .close {
	width: 30px;
	height: 30px;
	background: url(../images/default/il-popup-close.png) no-repeat;
	position: absolute;
	right: -34px;
	top: -34px;
	margin: 0;
	background-size: contain;
} */

.il-sumUp-popup .inner {
	padding-bottom: 20px;
}

/* .il-sumUp-popup .inner .title {
	background-image: url(../images/default/study-sumUp.png);
} */

.il-popup .line {
	display: block;
	height: 7px;
	margin: 30px auto 31px;
	position: relative;
	/* background: url(../images/default/il-popup-line.png) no-repeat; */
}

.il-popup .line::before,
.il-popup .line::after {
	position: absolute;
	display: block;
	content: " ";
	width: 122px;
	height: 1px;
	background: rgba(161, 237, 211, 1);
	top: 4px;
}

.il-popup .line::before {
	left: 16px;
}

.il-popup .line::after {
	right: 16px;
}

.il-popup .content-title {
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: rgba(51, 51, 51, 1);
	line-height: 26px;
	padding-left: 24px;
	background: url(../images/default/knowledge-map-title.png) no-repeat left center;
}

.il-sumUp-popup .inner .bulb {
	width: 40px;
	height: 51px;
	background: url(../images/default/il-bulb.png) no-repeat;
	position: absolute;
	left: 50%;
	top: 6px;
	transform: translateX(-50%);
}


.il-sumUp-popup .text {
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: rgba(51, 51, 51, 1);
	line-height: 24px;
}

.il-target-popup {
	display: none;
	top: -60px;
	width: 350px;
}

.il-target-popup .inner {
	padding-bottom: 30px;
}

.il-target-popup .inner .bulb {
	width: 51px;
	height: 47px;
	background: url(../images/default/il-bulb-target.png) no-repeat;
	position: absolute;
	left: 50%;
	top: 11px;
	transform: translateX(-50%);
}

.il-target-popup .inner .title {
	background-image: url(../images/default/study-target.png);
}

.il-target-popup .main,
.il-sumUp-popup .main {
	overflow: auto;
}

.il-target-popup .main li {
	padding: 0 29px 0 16px;
	font-size: 16px;
	color: #333;
}

.il-sumUp-popup .main li {
	padding: 0 10px 0 16px;
}

.il-target-popup .main li p,
.il-sumUp-popup .main li p {
	line-height: 27px;
}

.il-target-popup .main li span,
.il-sumUp-popup .main li span {
	font-weight: bold;
}

.learn-target {
	display: none;
	position: fixed;
	left: 13px;
	top: 204px;
	width: 52px;
	height: 54px;
	background: url(../images/default/learn-target.png) no-repeat;
	cursor: pointer;
	padding-right: 20px;
}

.learn-target:hover .il-target-popup {
	display: block;
}

.learn-summary {
	display: none;
	position: fixed;
	left: 13px;
	padding-right: 20px;
	top: 380px;
	width: 52px;
	height: 54px;
	background: url(../images/default/learn-summary.png) no-repeat;
	cursor: pointer;
	z-index: 9;
}

.title-text {
	padding-right: 25px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.il-sumUp-popup {
	top: 50%;
	transform: translateY(-50%);
	display: none;
	margin-top: -4px;
}

.il-sumUp-popup .inner {
	overflow: auto;
}

.il-sumUp-popup .main-wrap {
	max-height: 510px;
	overflow: auto;
	width: 792px;
}

.learn-summary:hover .il-sumUp-popup {
	display: block;
}
