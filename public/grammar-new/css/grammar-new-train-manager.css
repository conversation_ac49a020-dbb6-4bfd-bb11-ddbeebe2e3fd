.knowledge-box{
	float: left;
	width: 350px;
	margin-right: 20px;
	border: 1px solid #ccc;
}

.knowledge-box .aside-title{
	background-color: #1ab394;
	color: #fff;
	font-size: 16px;
	text-align: center;
	line-height: 32px;
}

.knowledge-box ul{
	padding: 0;
}
.knowledge-box label{
	margin: 0;
}

.knowledge-box ul>li{
	list-style: none;
	cursor: pointer;
}

.knowledge-box .has-children>.title{
	padding-right: 25px;
	position: relative;
}

.knowledge-box .title:hover{
	background-color: #e5e5e5;
}

.knowledge-box .title.clicked{
	background-color: #e5e5e5;
}

.knowledge-box .has-children>.title>i {
	background: url(../images/zujuan-page-next.png) center no-repeat;
	width: 22px;
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
	cursor: pointer;
}

.knowledge-box .has-children>.title>i.show {
	background: url(../images/zujuan-page-down.png) center no-repeat;
}

.knowledge-box .title0{
	font-size: 18px;
	line-height: 32px;
	padding-left: 5px;
}

.knowledge-box .title0>i{
	height: 32px;
}

.knowledge-box .title1{
	font-size: 16px;
	line-height: 30px;
	padding-left: 15px;
}

.knowledge-box .title1>i{
	height: 30px;
}

.knowledge-box .title2{
	font-size: 14px;
	line-height: 28px;
	padding-left: 30px;
}

.knowledge-box .title2>i{
	height: 28px;
}

.knowledge-box .title3{
	font-size: 12px;
	line-height: 26px;
	padding-left: 45px;
}

.knowledge-box .title3>i{
	height: 26px;
}

.cd-accordion-menu ul{
	/* display: none; */
}

.source-span,.year-span,.area-span{
	margin-left: 14px;
}

.knowDia{
	display: none;
	position: absolute;
	border-radius: 4px;
	box-shadow: #ccc 0 0 8px 0;
	background: #fff;
	padding: 10px;
	height: 300px;
	width: 280px;
	overflow-y: auto;
	top: -300px;
}

.knowDia li>ul{
	padding-left: 20px !important;
}

.knowledge-span {
	margin-right: 10px;
	border: 1px solid #aaa;
	border-radius: 5px;
	padding: 2px 5px;
}
.input-option-read{
	width: 300px;
}
.py{
	display: inline-block;
}
.input-option-read1{
	width: 100px;
}
.list-item{
	display: flex;
	align-items: center;
}
.line{
	display: flex;
	align-items: center;
}