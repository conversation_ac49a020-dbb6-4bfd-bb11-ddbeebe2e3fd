/****************************查询显示试题列表***************************/
var param={
		grade: 0,
		type: 0,
		versionId: 0,
		courseId: 0,
		unitId: 0,
		provinceId: 0,
		cityId: 0,
		year: 0,
		knowledgePoint: 0,
		questionType: 0,
		difficulty: 0,
		questionText: "",
		pageNum: 1,
		pageSize: 10,
		orderBy: "use_num DESC"
}
$(function(){
	new SelectEvent($("#top-select")).init();
});


function toGetTeachQuestion(){
	param.gradePhase = $(".gradePhase").val();
	param.grade = $(".grade").val();
	param.versionId = $(".version").val();
	param.courseId = $(".course").val();
	param.unitId = $(".unit").val();
	param.lessonId = $(".lesson").val();
	param.provinceId = $(".province").val();
	param.cityId = $(".city").val();
	param.year = $(".year").val();

	param.knowledgePoint = $(".knowledgePoint4").val();
	if(param.knowledgePoint==null || param.knowledgePoint == 0){
		param.knowledgePoint = $(".knowledgePoint3").val();
		if(param.knowledgePoint==null || param.knowledgePoint == 0){
			param.knowledgePoint = $(".knowledgePoint2").val();
			if(param.knowledgePoint==null || param.knowledgePoint == 0){
				param.knowledgePoint = $(".knowledgePoint1").val();
			}
		}
	}
	param.questionType = $(".questionType").val();
	param.difficulty = $(".difficulty").val();
	param.questionText = $("#questionText").val();
	if (param.questionText != null) {
		param.questionText = param.questionText.replace(/\\/g, "\\\\");
		param.questionText = param.questionText.replace(/'/g, "\\'");
	}
	getTeachQuestionList();
}

function getTeachQuestionList1(nowPage){
	getTeachQuestionList(nowPage, 1);
}
function getTeachQuestionList2(nowPage){
	getTeachQuestionList(nowPage, 2);
}

/**
 * 获取试题列表
 */
var teachQuestionNum = 0;
var teachId;
var aid;
function getTeachQuestionList(nowPage, pageIndex){
	if(nowPage==null){
		nowPage=1;
	}
	param.pageNum = nowPage;
	if(pageIndex==null || pageIndex==1){
		if($("#pageSize1").length>0){
			param.pageSize = parseInt($("#pageSize1").val());
		}
	}else{
		if($("#pageSize2").length>0){
			param.pageSize = parseInt($("#pageSize2").val());
		}
	}
	$.ajax({
		type: "get",
		url: "/simulationQuestion/getSimulationQuestionList",
		data: param,
		cache: false,
		dataType: "json",
		success: function(data){
			if(teachId==null){
				teachId = data.teachId;
			}
			if(aid==null){
				aid = data.aid;
			}
			if(param.pageNum==1){
				teachQuestionNum = data.questionNum;
				$(".totalCount>span").text(teachQuestionNum);
			}
			teachQuestionList = data.questionList;
			showTeachQuestionList();
		}
	});
}

/**
 * 展示试题列表
 */
var teachQuestionList = undefined;
function showTeachQuestionList(){
	$("#dataTbody").children().remove();
	var contentHtml = "";
	if(teachQuestionList==null||teachQuestionList.length==0){
		var tip = "*没有符合条件的试题哦~您可以先去添加对应的试题！*"
		contentHtml="<tr><td colspan='"+$("#dataTable thead tr th").length+"' style='color:red'>"+tip+"</td></tr>";
		$("#dataTbody").append(contentHtml);
	}else{
		var questionArr = null;
		$(teachQuestionList).each(function(index){
			contentHtml ="<tr>";
			contentHtml+="<td><input name='questionId' value='"+this.id+"' type='checkbox'/></td>";
			contentHtml+="<td>"+(param.pageSize*(param.pageNum-1)+index+1)+"</td>";
			if(this.teachId=="system"){
				contentHtml += "<td>系统题库</td>";
			}else if(this.teachId==teachId){
				contentHtml += "<td>自建题库</td>";
			}else if(this.aid==aid){
				contentHtml += "<td>校区题库</td>";
			}else{
				contentHtml += "<td>共享题库</td>";
			}
			contentHtml+="<td>"+getKnowledgePointName(this.knowledgePoint)+"</td>";
			contentHtml+="<td>"+questionTypeArr[this.questionType]+"</td>";
			contentHtml+="<td class='text-left "+(this.parse==null?"'":("parse-tips' title='"+this.parse+"'"))+">";
			if(this.questionType==3){//判断题，不显示选项
				contentHtml+=this.question+"</td>";
				contentHtml+="<td>"+(this.correctOption=="T"?"√":"×")+"</td>";
			}else if(this.questionType==4){//填空题
				questionArr = $.trim(this.question).split(/_{3,}/g);
				$(questionArr).each(function(i,item){
					contentHtml += item;
					if(i<questionArr.length-1){
						contentHtml += "<u>&nbsp;"+(i+1)+"&nbsp;</u>";
					}
				});
				contentHtml+="</td><td>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="1."+this.optionA;
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<br/>2."+this.optionB;
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<br/>3."+this.optionC;
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<br/>4."+this.optionD;
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<br/>5."+this.optionE;
				}
				contentHtml+="</td>";
			}else if(this.questionType==1 || this.questionType==2){
				contentHtml+=this.question+"<br>";
				if(this.optionA!=null&&$.trim(this.optionA)!=""){
					contentHtml+="<span>A."+this.optionA+"</span>&emsp;";
				}
				if(this.optionB!=null&&$.trim(this.optionB)!=""){
					contentHtml+="<span>B."+this.optionB+"</span>&emsp;";
				}
				if(this.optionC!=null&&$.trim(this.optionC)!=""){
					contentHtml+="<span>C."+this.optionC+"</span>&emsp;";
				}
				if(this.optionD!=null&&$.trim(this.optionD)!=""){
					contentHtml+="<span>D."+this.optionD+"</span>&emsp;";
				}
				if(this.optionE!=null&&$.trim(this.optionE)!=""){
					contentHtml+="<span>E."+this.optionE+"</span>&emsp;";
				}
				contentHtml+="</td>";
				contentHtml+="<td>"+this.correctOption+"</td>";
			}else{
				contentHtml+=subString(this.question)+"<br></td>";
				contentHtml+="<td>--</td>";
			}
			
			contentHtml+="<td>"+difficultyArr[this.difficulty]+"</td>";
			contentHtml+="<td>"+this.useNum+"</td>";
			if(userType==2){
				contentHtml+="<td><button type='button' class='btn btn-primary btn-xs' onclick='toUpdateTeachQuestion(\""+index+"\")'>修改</button>&emsp;";
				contentHtml+="<button type='button' class='btn btn-primary btn-xs' onclick='deleteTeachQuestion(\""+this.id+"\")'>删除</button></td>";
			}
			contentHtml+="</tr>";
			$("#dataTbody").append(contentHtml);
			$(".parse-tips").tipso({
				useTitle: true,
				position: 'bottom',
				width: '300px'
			});
		});	
	}
	loadPageInfo();
}

function subString(text){
	if(text.length<=100){
		return text;
	}
	var flag = false;
	var result = "";
	var char = "";
	var count = 0;
	for(var i=0; i<text.length; i++){
		char = text.charAt(i);
		if(char=="<"){
			flag = true;
		}else if(char==">"){
			flag = false;
		}else if(!flag){
			count++;
			if(count>100){
				break;
			}
		}
		result += char;
	}
	return result;
}



/**
 * 打开修改试题界面
 */
var questionTypeIdArr = new Array("single-choice","multiple-choice","true-or-false","completion");
function toUpdateTeachQuestion(index){
	var item = teachQuestionList[index];
	var questionTypeId = questionTypeIdArr[item.questionType-1];
	var _html = "<div id='dialog-div'>"+$("#"+questionTypeId).html()+$("#common").html()+"</div>";
	var d = dialog({
        title: '修改试题',
        content: _html,
        width: "772px",
        zIndex: 999,
        padding: "20px",
        okValue: '确 定',
        ok: function () {
        	return updateTeachQuestion(item);
        },
        cancelValue: '取消',
        cancel: function () {}
        });
    d.showModal();
    //回显试题
    $("#dialog-div textarea[name='question']").attr("id", "ckeditor");
    ckeditor = CKEDITOR.replace("ckeditor", {customConfig: '/js/config/ck-add-question-config.js', width: 710, height: 150});
		console.log(CKEDITOR.instances);
		for(let key in CKEDITOR.instances){
			CKEDITOR.instances[key].on('paste', function(event) {
				var editor = event.editor;
				var data = event.data;
		 
				// 使用正则表达式替换中文引号为西文引号
				data.dataValue = data.dataValue.replace(/[“”]/g, '"').replace(/[‘’]/g, "'");
		});
		}
    ckeditor.setData(item.question);
    $("#dialog-div input[name='difficulty'][value='"+item.difficulty+"']").prop("checked",true);
    $("#dialog-div select[name='answerTime'] option[value='"+item.answerTime+"']").prop("selected",true);
    if(item.parse){
    	$("#dialog-div .parse").val(item.parse.replace(/<br>/g,"\r\n"));
    }
    if(item.questionType!=3){//不是判断题，回显选项
    	$("#dialog-div input[name='optionA']").val(item.optionA==null?"":item.optionA);
    	$("#dialog-div input[name='optionB']").val(item.optionB==null?"":item.optionB);
    	$("#dialog-div input[name='optionC']").val(item.optionC==null?"":item.optionC);
    	$("#dialog-div input[name='optionD']").val(item.optionD==null?"":item.optionD);
    	$("#dialog-div input[name='optionE']").val(item.optionE==null?"":item.optionE);
    }
    if(item.questionType!=4){//不是填空题，回显正确答案
    	if(item.questionType==2){//多选题
    		var optionArr = $.trim(item.correctOption).split(",");
    		$(optionArr).each(function(){
    			$("#dialog-div .correct-option[value='"+this+"']").prop("checked",true);
    		});
    	}else{
    		$("#dialog-div .correct-option[value='"+item.correctOption+"']").prop("checked",true);
    	}
    }
}

/**
 * 修改试题
 */
function updateTeachQuestion(item){
	//获取并验证内容
	var text = $.trim(ckeditor.document.getBody().getText());
	if(text==null || text==""){
		alert("请输入题干内容！");
		return false;
	}
	item.question = $.trim(ckeditor.getData());
	item.questionNohtml = text;
	if(item.questionType==4){
		if(item.question.match(/_{3,}/)==null){
			alert("填空题中至少需要一个空！");
			return false;
		}
		if(item.question.split(/_{3,}/).length-1>5){
			alert("填空题最多只能拥有五个空！");
			return false;
		}
	}
//	item.question = item.question.replace(/(\n)|(\r\n)/g,"<br>");
//	item.question = item.question.replace(/(<br>){2,}/g,"<br>");
	if(item.questionType!=3){//不是判断题，记录选项
		item.optionA = $.trim($("#dialog-div input[name='optionA']").val());
		item.optionB = $.trim($("#dialog-div input[name='optionB']").val());
		item.optionC = $.trim($("#dialog-div input[name='optionC']").val());
		item.optionD = $.trim($("#dialog-div input[name='optionD']").val());
		item.optionE = $.trim($("#dialog-div input[name='optionE']").val());
		if((item.optionA==null || item.optionA=="")&&
				(item.optionB==null || item.optionB=="")&&
				(item.optionC==null || item.optionC=="")&&
				(item.optionD==null || item.optionD=="")&&
				(item.optionE==null || item.optionE=="")){
			alert("请至少输入一个选项或答案");
			return false;
		}
		if(item.questionType==4){//填空题检查空和答案是否对应
			var spaceNum = item.question.split(/_{3,}/).length-1;
			if(spaceNum>=1 && item.optionA==""){
				alert("该题第一个空对应的答案不能为空！")
				return false;
			}
			if(spaceNum>=2 && item.optionB==""){
				alert("该题第二个空对应的答案不能为空！")
				return false;
			}
			if(spaceNum>=3 && item.optionC==""){
				alert("该题第三个空对应的答案不能为空！")
				return false;
			}
			if(spaceNum>=4 && item.optionD==""){
				alert("该题第四个空对应的答案不能为空！")
				return false;
			}
			if(spaceNum==5 && item.optionE==""){
				alert("该题第五个空对应的答案不能为空！")
				return false;
			}
			var answerNum = (item.optionA==""?0:1)+
							(item.optionB==""?0:1)+
							(item.optionC==""?0:1)+
							(item.optionD==""?0:1)+
							(item.optionE==""?0:1);
			if(answerNum > spaceNum){
				alert("答案数量不能超过空的数量！");
				return false;
			}
		}
    }
	if(item.questionType==1){//单选
		item.correctOption = $("#dialog-div .correct-option:checked").val();
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		if((item.correctOption=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
				(item.correctOption=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
				(item.correctOption=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
				(item.correctOption=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
				(item.correctOption=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
			alert("没有内容的答案不能为正确答案");
			return false;
		}
	}else if(item.questionType==2){//多选
		item.correctOption = "";
		$("#dialog-div .correct-option:checked").each(function(){
			item.correctOption += $(this).val()+",";
			if(($(this).val()=="A" && (item.optionA==null || $.trim(item.optionA)==""))||
					($(this).val()=="B" && (item.optionB==null || $.trim(item.optionB)==""))||
					($(this).val()=="C" && (item.optionC==null || $.trim(item.optionC)==""))||
					($(this).val()=="D" && (item.optionD==null || $.trim(item.optionD)==""))||
					($(this).val()=="E" && (item.optionE==null || $.trim(item.optionE)==""))){
				alert("没有内容的答案不能为正确答案");
				return false;
			}
		});
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
		item.correctOption = item.correctOption.substring(0,item.correctOption.length-1);
	}else if(item.questionType==3){//判断题
		item.correctOption = $("#dialog-div .correct-option:checked").val();
		if(item.correctOption==null || item.correctOption==""){
			alert("请指示出正确答案");
			return false;
		}
	}
	item.parse = $("#dialog-div .parse").val();
	item.parse = $.trim(item.parse);
	item.parse = item.parse.replace(/(\n)|(\r\n)/g,"<br>");
	item.parse = item.parse.replace(/(<br>){2,}/g,"<br>");
	item.difficulty = $("#dialog-div input[name='difficulty']:checked").val();
	if(item.difficulty==null){
		alert("请选择试题难度");
		return false;
	}
	item.answerTime = $("#dialog-div select[name='answerTime']").val();
	if(item.answerTime==null || item.answerTime == 0){
		alert("请选择答题时间");
		return false;
	}
	item.createTime = new Date(item.createTime);
	
	//发送请求
	var flag = false;
	$.ajax({
		url: "/simulationQuestion/updateSimulationQuestion",
		type: "post",
		contentType: "application/json",
		data: JSON.stringify(item),
		cache: false,
		async: false,
		dataType: "json",
		success: function(data){
			flag = data;
			if(data){
				alert("修改试题成功！");
				getTeachQuestionList(param.pageNum);
			}else{
				alert("修改试题失败！");
			}
		}
	});
	return flag;
}

/**
 * 删除试题
 * @param id
 */
function deleteTeachQuestion(id){
	var idArr = new Array();
	if(id!=null){
		idArr.push(id)
	}else{
		if($("input[name='questionId']:checked").length==0){
			alert("请先选中要删除的试题！");
			return;
		}
		$("input[name='questionId']:checked").each(function(i,item){
			idArr.push($(item).val());
		});
	}
	var d = dialog({
        title: '删除试题',
        content: "您确定要删除选中的试题吗？",
        okValue: '确 定',
        ok: function () {
            this.title('删除中..');
            $.get("/simulationQuestion/deleteSimulationQuestionByIds",{"idArr[]":idArr},function(data){
        		if(data.result){
        			alert("删除成功！");
        			getTeachQuestionList();
        		}else{
        			alert("删除失败！");
        		}
            },"json");
            return;
        },
        cancelValue: '取消',
        cancel: function () {}
        });
    d.showModal();
}


/**
 * 全选按钮点击事件
 * @param obj
 */
function selectAll(obj){
	if($(obj).prop("checked")){
		$("input[name='questionId']").prop("checked",true);	
		$(".selectAll").prop("checked", true)
	}else{
		$("input[name='questionId']").prop("checked",false);		
		$(".selectAll").prop("checked", false)
	}
}

/**
 * 加载分页信息
 */
function loadPageInfo(){
	var pageLen = 1;
	if(teachQuestionNum!=undefined&&teachQuestionNum>1){
		if(param.pageSize==0){
			pageLen = 1;
		}else{
			pageLen = Math.ceil(teachQuestionNum/param.pageSize);					
		}
	}
	
	$('.pageTest1').page({
      leng: pageLen,//分页总数
      activeClass: 'activP' , //active 类样式定义
      page_size:param.pageSize,
      nowPage:param.pageNum,
      selectPage:getTeachQuestionList1
    });
	$("#pageSize").attr("id","pageSize1");
	$('.pageTest2').page({
		leng: pageLen,//分页总数
		activeClass: 'activP' , //active 类样式定义
		page_size:param.pageSize,
		nowPage:param.pageNum,
		selectPage:getTeachQuestionList2
	});
	$("#pageSize").attr("id","pageSize2");
	$(".selectAll").prop("checked",false);
}

/**
点击排序按钮
**/
function orderByQuery(str,obj){
	$(obj).find(".shang").toggleClass('none');
	$(obj).find(".xia").toggleClass('none');
	$(obj).siblings().find(".s-ico").addClass('qianhui');
	$(obj).find(".s-ico").removeClass('qianhui');
	
	param.orderBy=str+" "+$(obj).find(".s-ico span:visible").attr("sort");
	getTeachQuestionList();
}

/***********************批量修改***********************/
function toUpdateQuestions(){
	var idArr = new Array();
	if($("input[name='questionId']:checked").length==0){
		alert("请先选中要修改的试题！");
		return;
	}
	$("input[name='questionId']:checked").each(function(i,item){
		idArr.push($(item).val());
	});
	var _content = "<div id='update-dia'>"+$(".update-div").html()+"</div>";
	var d = dialog({
		title: "批量修改",
		content: _content,
		width: "800px",
		padding: "20px",
		okValue: "确认",
		cancelValue: "取消",
		cancel: true,
		zIndex: 99,
		ok: function(){
			return updateQuestions(idArr);
		}
	});
	new SelectEvent($("#update-dia")).init();
	d.showModal();
}

var updating = false;
function updateQuestions(idArr){
	if(updating){
		return;
	}
	var updateParam = {};
	updateParam.gradePhase = $("#update-dia .gradePhase").val();
	if (updateParam.gradePhase == null || updateParam.gradePhase == 0) {
		alert("请选择学段！");
		return false;
	}
	updateParam.grade = $("#update-dia .grade").val();
	if (updateParam.grade == null || updateParam.grade == 0) {
		alert("请选择年级！");
		return false;
	}
	if (updateParam.grade == 30 || updateParam.grade == 40 || updateParam.grade == 50) {
		updateParam.courseId = 0;
		updateParam.unitId = 0;
		updateParam.versionId = 0;
		updateParam.lessonId = 0;

	} else {
		updateParam.versionId = $("#update-dia .version").val();
		updateParam.courseId = $("#update-dia .course").val();
		if (updateParam.courseId == null || updateParam.courseId == 0) {
			alert("请选择本版教材！");
			return false;
		}
		updateParam.unitId = $("#update-dia .unit").val();
		if (updateParam.unitId == null || updateParam.unitId == 0) {
			alert("请选择单元！");
			return false;
		}
		updateParam.lessonId = $("#update-dia .lesson").val();

	}

	updateParam.provinceId = $("#update-dia .province").val();
	updateParam.cityId = $("#update-dia .city").val();
	updateParam.year = $("#update-dia .year").val();
	
	updateParam.knowledgePoint = $("#update-dia .knowledgePoint4").val();
	if(updateParam.knowledgePoint==null || updateParam.knowledgePoint == 0){
		updateParam.knowledgePoint = $("#update-dia .knowledgePoint3").val();
		if(updateParam.knowledgePoint==null || updateParam.knowledgePoint == 0){
			updateParam.knowledgePoint = $("#update-dia .knowledgePoint2").val();
			if(updateParam.knowledgePoint==null || updateParam.knowledgePoint == 0){
				updateParam.knowledgePoint = $("#update-dia .knowledgePoint1").val();
			}
		}
	}
	
	updateParam.difficulty = $("#update-dia .difficulty").val();
	updateParam.idArr = idArr;
	updating = true;
	var flag = false;
	$.ajax({
		type: "post",
		url: "/simulationQuestion/updateQuestions",
		async: false,
		data: updateParam,
		dataType: "json",
		success: function(data){
			flag = data;
			updating = false;
		},
		error: function(){
			updating = false;
		}
	});
	if(flag){
		alert("批量修改成功！");
		toGetTeachQuestion();
	}else{
		alert("批量修改失败！");
	}
	return flag;
}
