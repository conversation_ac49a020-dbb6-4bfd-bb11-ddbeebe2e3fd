/**
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see https://ckeditor.com/legal/ckeditor-oss-license
 */

CKEDITOR.editorConfig = function( config ) {
	// Define changes to default configuration here.
	// For complete reference see:
	// https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html

	// The toolbar groups arrangement, optimized for two toolbar rows.
	config.toolbarGroups = [
		{ name: 'clipboard',   groups: [ 'clipboard', 'undo' ] },
		{ name: 'editing',     groups: [ 'find', 'selection', 'spellchecker' ] },
		{ name: 'links' },
		{ name: 'insert' },
		{ name: 'forms' },
		{ name: 'tools' },
		{ name: 'document',	   groups: [ 'mode', 'document', 'doctools' ] },
		{ name: 'others' },
		'/',
		{ name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
		{ name: 'paragraph',   groups: [ 'list', 'indent', 'blocks', 'align', 'bidi' ] },
		{ name: 'styles' },
		{ name: 'colors' },
	];

	// Remove some buttons provided by the standard plugins, which are
	// not needed in the Standard(s) toolbar.
	config.removeButtons = 'Subscript,Superscript,SpellCheckAsYouType ';
	
	// Set the most common block elements.
	config.format_tags = 'p;h1;h2;h3;pre';
	// Simplify the dialog windows.
	config.removeDialogTabs = 'image:advanced;link:advanced';
		//编辑器中回车产生的标签
    config.enterMode = CKEDITOR.ENTER_DIV; //可选：CKEDITOR.ENTER_P、CKEDITOR.ENTER_DIV、CKEDITOR.ENTER_BR
    config.shiftEnterMode = CKEDITOR.ENTER_DIV;
		config.enterMode = CKEDITOR.ENTER_BR; 
	config.shiftEnterMode = CKEDITOR.ENTER_BR;
		config.language = 'zh-cn';
	config.colorButton_enableMore = false;//关闭更多颜色按钮
	config.resize_enabled = false;//禁止改变窗口大小
	config.pasteFromWordCleanup=false;
	//预定义颜色
	CKEDITOR.config.colorButton_colorsPerRow = 6;
	// CKEDITOR.config.colorButton_colors = '1ABC9C,2ECC71,3498DB,9B59B6,4E5F70,F1C40F,' +
	// '16A085,27AE60,2980B9,8E44AD,2C3E50,F39C12,' +
	// 'E67E22,E74C3C,ECF0F1,95A5A6,DDD,FFF,' +
	// 'D35400,C0392B,BDC3C7,7F8C8D,999,000';


	// // 格式刷
	// config.extraPlugins = 'copyformatting';
	// // 字体对齐
	// config.extraPlugins = '';
		//字体颜色
		config.extraPlugins = 'button,panelbutton,floatpanel,panel,colorbutton,justify,copyformatting';
		CKEDITOR.config.colorButton_colors = 'FF6600,008C72';
	config.image_previewText=' '; //预览区域显示内容
	config.filebrowserImageUploadUrl= filebrowserImageUploadUrl; //待会要上传的action或servlet
	config.uploadUrl= uploadUrl; //待会要上传的action或servlet
	config.filebrowserUploadUrl =filebrowserUploadUrl
	//config.pasteFromWordIgnoreFontFace = true; //默认为忽略格式
	config.pasteFromWordRemoveFontStyles = false;
	config.pasteFromWordRemoveStyles = false;
	config.versionCheck=false
	 // 设置音频播放器的宽度和高度
};
