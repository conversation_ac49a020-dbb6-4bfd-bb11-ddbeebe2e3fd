/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'zh', {
	copy: '複製',
	copyError: '瀏覽器的安全性設定不允許編輯器自動執行複製動作。請使用鍵盤快捷鍵 (Ctrl/Cmd+C) 複製。',
	cut: '剪下',
	cutError: '瀏覽器的安全性設定不允許編輯器自動執行剪下動作。請使用鏐盤快捷鍵 (Ctrl/Cmd+X) 剪下。',
	paste: '貼上',
	pasteNotification: '請按下「%1」貼上。您的瀏覽器不支援工具列按鈕或是內容功能表選項。',
	pasteArea: '貼上區',
	pasteMsg: '請將您的內容貼於下方區域中並按下「OK」。',
	fileFormatNotSupportedNotification: '不支援 ${formats} 檔案格式。',
	fileWithoutFormatNotSupportedNotification: '檔案格式不支援。'
} );
