/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'it', {
	copy: 'Copia',
	copyError: 'Le impostazioni di sicurezza del browser non permettono di copiare automaticamente il testo. Usa la tastiera (Ctrl/Cmd+C).',
	cut: 'Taglia',
	cutError: 'Le impostazioni di sicurezza del browser non permettono di tagliare automaticamente il testo. Usa la tastiera (Ctrl/Cmd+X).',
	paste: 'Incolla',
	pasteNotification: 'Premere %1 per incollare. Il tuo browser non permette di incollare tramite il pulsante della barra degli strumenti o tramite la voce del menu contestuale.',
	pasteArea: 'Area dove incollare',
	pasteMsg: 'Incollare il proprio contenuto all\'interno dell\'area sottostante e premere OK.',
	fileFormatNotSupportedNotification: 'I file in formato ${formats} non sono supportati.',
	fileWithoutFormatNotSupportedNotification: 'Il formato di file non è supportato.'
} );
