/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'en', {
	copy: 'Copy',
	copyError: 'Your browser security settings don\'t permit the editor to automatically execute copying operations. Please use the keyboard for that (Ctrl/Cmd+C).',
	cut: 'Cut',
	cutError: 'Your browser security settings don\'t permit the editor to automatically execute cutting operations. Please use the keyboard for that (Ctrl/Cmd+X).',
	paste: 'Paste',
	pasteNotification: 'Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.',
	pasteArea: 'Paste Area',
	pasteMsg: 'Paste your content inside the area below and press OK.',
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.',
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.'
} );
