/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'es-mx', {
	copy: 'Copiar',
	copyError: 'La configuración de seguridad de su navegador no permite al editor ejecutar automáticamente operaciones de copiado. Por favor, utilice el teclado para (Ctrl/Cmd+C).',
	cut: 'Cortar',
	cutError: 'La configuración de seguridad de su navegador no permite al editor ejecutar automáticamente operaciones de corte. Por favor, utilice el teclado para (Ctrl/Cmd+X).',
	paste: 'Pegar',
	pasteNotification: 'Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.', // MISSING
	pasteArea: 'Paste Area', // MISSING
	pasteMsg: 'Paste your content inside the area below and press OK.', // MISSING
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
