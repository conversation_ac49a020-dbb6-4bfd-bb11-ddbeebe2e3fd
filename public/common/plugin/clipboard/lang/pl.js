/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'pl', {
	copy: 'Kopiuj',
	copyError: 'Ustawienia bezpieczeństwa Twojej przeglądarki nie pozwalają na automatyczne kopiowanie tekstu. Użyj skrótu klawiszowego Ctrl/Cmd+C.',
	cut: 'Wytnij',
	cutError: 'Ustawienia bezpieczeństwa Twojej przeglądarki nie pozwalają na automatyczne wycinanie tekstu. Użyj skrótu klawiszowego Ctrl/Cmd+X.',
	paste: 'Wklej',
	pasteNotification: 'Naciśnij %1 by wkleić tekst. Twoja przeglądarka nie pozwala na wklejanie za pomocą przycisku paska narzędzi lub opcji menu kontekstowego.',
	pasteArea: 'Miejsce do wklejenia treści',
	pasteMsg: 'Wklej treść do obszaru poniżej i naciśnij OK.',
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
