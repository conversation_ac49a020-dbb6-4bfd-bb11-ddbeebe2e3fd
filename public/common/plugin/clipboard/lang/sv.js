/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'sv', {
	copy: '<PERSON>piera',
	copyError: 'Säkerhetsinställningar i din webbläsare tillåter inte åtgärden kopiera. Använd (Ctrl/Cmd+C) istället.',
	cut: '<PERSON>lip<PERSON> ut',
	cutError: 'Säkerhetsinställningar i din webbläsare tillåter inte åtgärden klipp ut. Använd (Ctrl/Cmd+X) istället.',
	paste: 'Klistra in',
	pasteNotification: 'Tryck på %1 för att klistra in. Din webbläsare stödjer inte inklistring via verktygsfältet eller snabbmenyn.',
	pasteArea: 'Inklistringsområde',
	pasteMsg: 'Klistra in ditt innehåll i området nedan och tryck på OK.',
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
