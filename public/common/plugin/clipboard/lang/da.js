/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'da', {
	copy: 'Kopié<PERSON>',
	copyError: 'Din browsers sikkerhedsindstillinger tillader ikke editoren at få automatisk adgang til udklipsholderen. Brug i stedet tastaturet til at kopiere teksten (Ctrl/Cmd+C).',
	cut: 'Klip',
	cutError: 'Din browsers sikkerhedsindstillinger tillader ikke editoren at få automatisk adgang til udklipsholderen. Brug i stedet tastaturet til at klippe teksten (Ctrl/Cmd+X).',
	paste: 'Indsæt',
	pasteNotification: 'Tryk %1 for at sætte ind. Din browser understøtter ikke indsættelse med værktøjslinje knappen eller kontekst menuen.',
	pasteArea: 'Indsættelses område',
	pasteMsg: 'Indsæt dit indhold i området nedenfor og tryk OK.',
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
