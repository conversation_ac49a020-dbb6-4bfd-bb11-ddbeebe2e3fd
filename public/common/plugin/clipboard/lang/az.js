/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'az', {
	copy: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	copyError: 'Avtomatik köçürülməsi mümkün deyil. Ctrl+C basın.',
	cut: 'Kəs',
	cutError: 'Avtomatik kəsmə mümkün deyil. Ctrl+X basın.',
	paste: 'Əlavə et',
	pasteNotification: 'Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.', // MISSING
	pasteArea: 'Paste Area', // MISSING
	pasteMsg: 'Paste your content inside the area below and press OK.', // MISSING
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
