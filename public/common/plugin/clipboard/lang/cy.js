/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
CKEditor 4 LTS ("Long Term Support") is available under the terms of the Extended Support Model.
*/
CKEDITOR.plugins.setLang( 'clipboard', 'cy', {
	copy: 'Copïo',
	copyError: '\'Dyw gosodiadau diogelwch eich porwr ddim yn caniatàu\'r golygydd i gynnal \'gweithredoedd copïo\' yn awtomatig. Defnyddiwch y bysellfwrdd (Ctrl/Cmd+C).',
	cut: 'Torri',
	cutError: 'Nid yw gosodiadau diogelwch eich porwr yn caniatàu\'r golygydd i gynnal \'gweithredoedd torri\' yn awtomatig. Defnyddiwch y bysellfwrdd (Ctrl/Cmd+X).',
	paste: 'Gludo',
	pasteNotification: 'Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.', // MISSING
	pasteArea: 'Ardal Gludo',
	pasteMsg: 'Paste your content inside the area below and press OK.', // MISSING
	fileFormatNotSupportedNotification: 'The ${formats} file format(s) are not supported.', // MISSING
	fileWithoutFormatNotSupportedNotification: 'The file format is not supported.' // MISSING
} );
