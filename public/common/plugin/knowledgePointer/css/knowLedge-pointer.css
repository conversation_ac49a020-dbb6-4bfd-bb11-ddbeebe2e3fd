.cd-accordion-menu label {
    cursor: pointer;
}
.cd-accordion-menu label, .cd-accordion-menu a {
    position: relative;
    display: block;
    padding: 18px 18px 18px 64px;
    background: #4d5158;
    box-shadow: inset 0 -1px #555960;
    color: #ffffff;
    font-size: 1.6rem;
}
.cd-accordion-menu ul {
    display: none;
}

.knowledge-popup {
	overflow: hidden;
}

.knowledge-list {
	float: left;
	border-right: 1px solid #efefef;
	width: 290px;
	overflow-y: hidden;
}

.knowledge-nav {
	overflow: hidden;
	padding: 0 20px;
	background: #e5e5e5;
}

.knowledge-nav span {
	float: left;
	/* margin-right: 10px; */
	width: 33.3%;
	padding-left: 20px;
	background: url(../img/zujuan-radio.png) no-repeat 0 center;
	line-height: 40px;
	cursor: pointer;
}

.knowledge-nav span:hover {
	color: #00b48b;
}

.knowledge-nav span.active {
	background: url(../img/zujuan-radio-checked.png) no-repeat 0 center;
	color: #008c72;
}

.knowledge-list ul {
	padding:0;
}

.knowledge-list li {
	overflow: hidden;
	cursor: pointer;
	padding: 0 20px;
}

.knowledge-list li span,
.knowledge-list li p {
	float: left;
	line-height: 33px;
	font-size: 14px;
}

.knowledge-list li span {
	width: 14px;
	height: 14px;
	background: url(../img/zujuan-checkbox.png) no-repeat;
	margin-top: 8px;
	margin-right: 5px;
	margin-left: 14px;
}

.knowledge-list li span.checked {
	background: url(../img/zujuan-checkbox-checked.png) no-repeat;
}

.knowledge-right {
	float: left;
	padding: 20px 30px;
	/* width: 490px; */
}

.knowledge-right a {
	padding: 0 26px 0 9px;
	position: relative;
	background-color: #fafafa;
	border: 1px solid #d9d9d9;
	border-radius: 5px;
	margin-bottom: 10px;
	color: #666;
	margin-right: 10px;
	display: inline-block;
	line-height: 160%;
}

.knowledge-close {
	display: inline-block;
	position: absolute;
	top: 5px;
	right: 8px;
	width: 9px;
	height: 9px;
	background: url(../img/know-close.png) no-repeat center;
}
.knowledge-close:hover {
	background: url(../img/know-close-hover.png) no-repeat center;
}

.nicescroll-cursors {
	background-color: #efefef!important;
}
.cd-accordion-menu {
	background-color: #fff;
}

.cd-accordion-menu label, .cd-accordion-menu a {
	color: #333;
	background: #fff;
}

.cd-accordion-menu {
	margin: 0 auto;
	width: 100%;
	box-shadow: none;
	overflow-y: auto;
	padding-top: 10px;
	height: 530px;
}

.cd-accordion-menu label, .cd-accordion-menu a {
	padding: 0px 0 2px 0;
	font-size: 16px;
	box-shadow: none;
}

.cd-accordion-menu ul label, .cd-accordion-menu ul a {
	/* background-color: #fff; */
	box-shadow: none;
}

.cd-accordion-menu ul ul label, .cd-accordion-menu ul ul a {
	padding-left: 0;
	box-shadow: none;
	line-height: 30px;
}

.cd-accordion-menu ul ul ul label, .cd-accordion-menu ul ul ul a {
	padding-left: 0;
}

.cd-accordion-menu label {
	line-height: 30px;
}

.cd-accordion-menu span {
	display: inline-block;
	width: 14px;
	height: 14px;
	background: url(../img/zujuan-checkbox.png) no-repeat 0 center;
	z-index: 10;
	position: relative;
}

.cd-accordion-menu span.checked {
	background: url(../img/zujuan-checkbox-checked.png) no-repeat 0 center; 
}

.cd-accordion-menu label:hover {
	color: red;
}

.cd-accordion-menu a:hover {
	color: red;
}

.cd-accordion-menu {
	padding-right: 20px!important;
	padding-left: 0;
	display: none;
}	

.cd-accordion-menu li {
	padding-right: 0;
}
	
.cd-accordion-menu .has-children>label {
	background: url(../img/zujuan-page-next.png) no-repeat 0 9px; 
	z-index: 9;
}

.cd-accordion-menu .has-children>label.show {
	background: url(../img/zujuan-page-down.png) no-repeat 0 center; 
	z-index: 9;
}

.km-box .aside-title {
	line-height: 32px;
	color: #fff;
	font-size: 14px;
	background-color: #00B48B;
	text-align: center;
}

.knowledge-main {
	float: right;
	width: 510px;
	padding: 0 20px;
	background-color: #fff;
	position: relative;
}

.knowledge-main-title {
	overflow: hidden;
	border-bottom: 1px dashed #d9d9d9;
}

.knowledge-main-title h2 {
	float: left;
	line-height: 53px;
	font-weight: bold;
	font-size: 14px;
	margin: 0;
}

.knowledge-main-title span {
	float: right;
	line-height: 53px;
	font-size: 12px;
	color: #999;
	padding-left: 16px;
	background: url(../img/delete-icon.png) no-repeat 0 center;
	cursor: pointer;
}

.knowledge-main-title span:hover {
	background: url(../img/delete-green.png) no-repeat 0 center;
	color: #00B48B;
}

.knowledge-right {
	/* width: 668px; */
	padding: 20px 0;
	/* border-bottom: 1px dashed #d9d9d9; */
	float: none;
}









