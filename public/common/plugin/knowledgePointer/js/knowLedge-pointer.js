var knowledgePointer = {
	gradePhase: null,
	gradePhaseName: null,
	questionType: null,
	knowledgeList: null,
	questionTypeKnowlegeMap: null,
	//添加知识点
	selectKnowledgePoint: function(questionType, checkedArr, limitNum, callback) {
		knowledgePointer.questionType = questionType;
		knowledgePointer.limitNum = limitNum;
		var _html = "<div id='knowledge_popup' class='knowledge-popup km-box'>";
		_html += '<div class="knowledge-list">';
		_html += '<div id="knowledgeList" style="height: 500px;">';
		_html += '<ul class="cd-accordion-menu animated parentId_'+knowledgePointer.gradePhase+'" style="display: block;"></ul>';
		_html += '</div>';
		_html += '</div>';
		_html += '<div class="knowledge-main">';
		_html += '<div class="knowledge-main-title">';
		_html += '<h2>已选知识点（<i id="selectedKnowNum">0</i>个）</h2>';
		_html += '<span id="cleanKnow" onclick="knowledgePointer.cleanKnowledge();">清空</span>';
		_html += '</div>';
		_html += '<div class="knowledge-right"></div>';
		_html += "</div>";
		_html += "</div>";

		var d = dialog({
			title: "添加知识点>"+knowledgePointer.gradePhaseName,
			content: _html,
			width: 800,
			padding: "0",
			ok: function(){
				var knowledgeArr = new Array();
				if(callback!=null && typeof callback == 'function'){
					var gradePhase = $("#knowledge_popup .knowledge-nav>.active").attr("data");
					$("#knowledge_popup .knowledge-right>a").each(function(i, item){
						knowledgeArr.push({"id": $(item).attr("data"), "name": $(item).text()})
					});
					callback(knowledgeArr);
				}
			},
			okValue: "确认",
			cancelValue: "取消",
			cancel: true,
		});
		d.showModal();
		
		if(knowledgePointer.questionTypeKnowlegeMap==null){
			$.ajax({
				url: "/questionConfig/getQuestionKnowledgeMap",
				type: "post",
				cache: false,
				async: false,
				dataType: "json",
				success: function(data){
					knowledgePointer.questionTypeKnowlegeMap = data;
				}
			});
		}
		
		if(knowledgePointer.knowledgeList==null){
			$.ajax({
				url: "/questionConfig/getKnowledgePointMap",
				type: "post",
				cache: false,
				async: false,
				dataType: "json",
				success: function(data){
					knowledgePointer.knowledgeList = data;
				}
			});
		}
		knowledgePointer.showKnowledgePointList(knowledgePointer.gradePhase);
		
		$("#knowledge_popup .knowledgeList").niceScroll();
		//绑定事件
		$("#knowledge_popup .cd-accordion-menu .has-children>label").click(function(){
			if($(this).next().css("display") == "none"){
				$(this).addClass("show").next().slideDown("fast");
			}else{
				$(this).removeClass("show").next().slideUp("fast");
			}
		});
		
		$("#knowledge_popup .cd-accordion-menu span").click(function(){
			if($(this).hasClass("checked")){
				$(this).removeClass("checked");
				knowledgePointer.removeKnowledge($(this).attr("data"))
			}else{
				$(this).parents("ul").siblings(".checked").each(function(i, item){
					$(item).removeClass("checked");
					knowledgePointer.removeKnowledge($(this).attr("data"))
				});
				$(this).siblings("ul").find(".checked").each(function(i, item){
					$(item).removeClass("checked");
					knowledgePointer.removeKnowledge($(this).attr("data"))
				});
				$(this).addClass("checked");
				var knowledge = {"id": $(this).attr("data"), "name": $(this).next("label").text()};
				knowledgePointer.addKnowledge(knowledge)
			}
		});
		
		if(checkedArr){
			$(checkedArr).each(function(){
				$("#knowledge_popup .cd-accordion-menu span[data='"+this+"']").click();
			});
		}
	},
	showKnowledgePointList: function(parentId){
		var list;
		if(parentId==30 || parentId==40 || parentId==50){
			if(knowledgePointer.questionType==null){
				list = knowledgePointer.knowledgeList[parentId];
			}else{
				list = knowledgePointer.questionTypeKnowlegeMap[parentId][knowledgePointer.questionType];
			}
		}else{
			list = knowledgePointer.knowledgeList[parentId];
		}
		$(list).each(function(i, item){
			var _html = '';
			if(knowledgePointer.knowledgeList[item.id]!=null){
				_html += '<li class="has-children">';
				_html += '<span data="'+item.id+'"></span>';
				_html += '<label>'+item.name+'</label>';
				_html += '<ul class="parentId_'+item.id+'"></ul>';
				_html += '</li>';
				$("#knowledgeList .parentId_"+parentId).append(_html);
				knowledgePointer.showKnowledgePointList(item.id);
			}else{
				_html += '<li><a href="#0"><span data="'+item.id+'"></span><label>'+item.name+'</label></a></li>';
				$("#knowledgeList .parentId_"+parentId).append(_html);
			}
		});
	},
	addKnowledge: function(item){
		if(knowledgePointer.limitNum!=null && knowledgePointer.limitNum>0 
				&& parseInt($("#selectedKnowNum").text())>=knowledgePointer.limitNum){
			alert("最多只能添加"+knowledgePointer.limitNum+"个知识点！");
			return;
		}
		var _html = '<a data="' + item.id + '" href="javascript:void(0);">'
		+ item.name + '<span class="knowledge-close" onclick="knowledgePointer.removeLabel(this)"></span></a>';
		$("#knowledge_popup .knowledge-right").append(_html);
		$("#selectedKnowNum").text($("#knowledge_popup .knowledge-right>a").length);
	},
	removeKnowledge: function(knowledgePoint){
		$("#knowledge_popup .knowledge-right>a[data='"+knowledgePoint+"']").remove();
		$("#selectedKnowNum").text($("#knowledge_popup .knowledge-right>a").length);
	},
	removeLabel: function(that){
		var knowledgePoint = $(that).parent().attr('data');
		$("#knowledgeList .checked[data='"+knowledgePoint+"']").removeClass("checked");
		$(that).parent().remove();
		$("#selectedKnowNum").text($("#knowledge_popup .knowledge-right>a").length);
	},
	cleanKnowledge: function(){
		$("#knowledgeList .checked").removeClass("checked");
		$("#knowledge_popup .knowledge-right").html("");
		$("#selectedKnowNum").text(0);
	}
};
